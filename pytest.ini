[pytest]
; -p no:faulthandler: 禁用pytest获取java异常的故障处理
; --capture=no: 打印详细日志，相当于命令行加 -vs
addopts = -vs --alluredir ./temp

reruns=1
; 自定义测试文件命名规则
python_files = test_*.py case_*.py
python_classes = Test*
python_functions = test_*

; 定义mark标记
markers =
    h5cart : 'h5cart'
    h5smoke : 'h5smoke'
    h5help : 'h5help'
    transaction : 'transaction'
    smoke : 'pc smoke'
    coreflow : 'core flow'
    suqin: 'suqin'
    mweb_regression: 'mweb regression'
    test: 'test'
    pcrewards : 'pc rewards'


