import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_vender.dweb_page_mkpl_vendor_homepage import DWebMkplVendorHomepage
from src.config.weee.log_help import log


@allure.story("桌面端商家主页功能")
class TestVendorHomepage:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("【商家主页】桌面端商家主页操作测试")
    @pytest.mark.present
    def test_101493_dweb_vendor_homepage(self, page: dict, pc_autotest_header, login_trace):
        """
        桌面端商家主页操作流程测试
        """
        _page: Page = page["page"]
        
        # 创建商家主页操作实例
        vendor_homepage = DWebMkplVendorHomepage(_page, pc_autotest_header)

        # 步骤1: 访问商家页面
        with allure.step("访问商家页面"):
            vendor_homepage.navigate_to_vendor_page()
            log.info("访问商家页面完成")

        # 步骤2: 检查页面必须存在的元素
        with allure.step("检查页面必须存在的元素"):
            vendor_homepage.check_required_elements_exist()
            log.info("页面元素检查完成")

        # 步骤3: 等待5秒
        with allure.step("等待5秒"):
            _page.wait_for_timeout(5000)
            log.info("等待5秒完成")

        # 步骤4: 检查探索标签页并点击全部商品标签页
        with allure.step("检查探索标签页并点击全部商品标签页"):
            result = vendor_homepage.check_explore_tab_and_click_all_products()
            log.info(f"探索标签页检查及全部商品点击结果: {result}")
            
            # 断言URL包含?tab=all
            if result:
                _page.wait_for_timeout(2000)  # 等待URL更新
                current_url = _page.url
                assert "?tab=all" in current_url, f"URL不包含?tab=all，当前URL: {current_url}"
                log.info("URL包含?tab=all验证成功")
            
            log.info("探索标签页检查及全部商品点击完成")

        # 步骤5: 等待5秒，滚动商品列表
        with allure.step("等待5秒，滚动商品列表"):
            _page.wait_for_timeout(5000)
            vendor_homepage.scroll_product_list()
            log.info("滚动商品列表完成")

        # 步骤6: 等待3秒，点击置顶按钮
        with allure.step("等待3秒，点击置顶按钮"):
            _page.wait_for_timeout(3000)
            result = vendor_homepage.click_back_to_top()
            log.info(f"置顶按钮点击结果: {result}")
            log.info("置顶按钮点击完成")

        # 步骤7: 等待5秒，按顺序检查并点击标签页
        with allure.step("等待5秒，按顺序检查并点击标签页"):
            _page.wait_for_timeout(5000)
            clicked_tabs = vendor_homepage.check_and_click_tabs_sequence()
            log.info(f"标签页检查及点击结果: {clicked_tabs}")
            
            # 验证每个被点击的标签页的URL
            for tab_name, expected_url_param, url_match in clicked_tabs:
                assert url_match, f"{tab_name}点击后，URL不包含{expected_url_param}"
                log.info(f"{tab_name}URL验证成功")
            
            if clicked_tabs:
                log.info("所有标签页URL验证成功")
            else:
                log.info("⚠没有标签页被点击")
            
            log.info("标签页检查及点击完成")

        log.info("桌面端商家主页操作测试完成")