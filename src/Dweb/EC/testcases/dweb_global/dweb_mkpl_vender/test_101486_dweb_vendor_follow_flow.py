"""
桌面端商家关注功能测试

测试步骤：
1. 访问链接：https://www.sayweee.com/zh/mkpl/vendor/6887
2. 等待3秒，如果有 data-testid='btn-follow-seller' 元素，则点击该元素
3. 输入email: <EMAIL>
4. 点击ele_submit_btn 提交按钮
5. 等待3秒，输入密码：123456 点击提交按钮
6. 再次点击已关注按钮
"""

import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_vender.dweb_page_mkpl_vender_follow import DWebMkplVendorFollowPage
from src.config.weee.log_help import log


@allure.story("桌面端商家关注功能")
class TestVendorFollow:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("【商家关注】桌面端商家关注功能测试")
    @pytest.mark.present
    def test_101486_dweb_vendor_follow_flow(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端商家关注功能测试
        """
        _page: Page = page["page"]
        
        # 创建商家关注页面操作实例
        vendor_follow_page = DWebMkplVendorFollowPage(_page, pc_autotest_header)

        # 步骤1: 访问商家页面
        with allure.step("访问商家页面"):
            vendor_follow_page.goto_vendor_page()
            log.info("访问商家页面完成")

        # 步骤2: 点击关注按钮（如果存在）
        with allure.step("点击关注按钮"):
            initial_text = vendor_follow_page.click_follow_button_if_exists()
            if initial_text:
                log.info("点击关注按钮完成")
            else:
                log.info("未找到关注按钮，测试结束")
                return

        # 步骤3-5: 处理登录流程-登录页面test id等待开发补充，后续更新
        """with allure.step("处理登录流程"):
            login_success = vendor_follow_page.handle_login_flow()
            if login_success:
                log.info("登录流程完成")
            else:
                log.info("登录流程失败")
                return
        """
        # 步骤6: 等待3秒后再次点击关注按钮
        with allure.step("等待3秒后再次点击关注按钮"):
            _page.wait_for_timeout(3000)
            vendor_follow_page.click_follow_button_if_exists()
            log.info("点击关注按钮完成")

        # 步骤7: 等待弹窗出现并验证元素
        with allure.step("等待弹窗出现并验证元素"):
            _page.wait_for_timeout(10000)  # 等待8秒让弹窗出现
            
            # 检查弹窗元素
            dialog_exists = vendor_follow_page.check_dialog_elements()
            log.info(f"弹窗容器存在状态: {dialog_exists}")
            
            # 断言弹窗元素存在
            assert dialog_exists, "弹窗容器不存在"
            log.info("弹窗元素验证成功")
            
            # 点击确定按钮
            vendor_follow_page.click_confirm_button_in_dialog()
            log.info("在弹窗中点击确定按钮完成")

        log.info("桌面端商家关注功能测试完成")
