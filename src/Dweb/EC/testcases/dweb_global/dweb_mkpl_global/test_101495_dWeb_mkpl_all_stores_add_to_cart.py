import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_all_store.dweb_page_mkpl_global_all_stores import DWebMkplGlobalComprehensivePage
from src.config.weee.log_help import log


@allure.story("PC端全球购商品加购减购功能")
class TestDWebMkplAllStoresAddToCart:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("【101495】PC端全球购商品加购减购测试")
    @pytest.mark.present
    def test_101495_dWeb_mkpl_all_stores_add_to_cart(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端全球购商品加购减购测试
        1. 访问全球购页面并清空购物车
        2. 等待3秒
        3. 点击加购按钮2次
        4. 点击减购按钮2次
        5. 验证最终数量为0
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 构造全球购页面操作实例
        mkpl_page = DWebMkplGlobalComprehensivePage(
            p, 
            pc_autotest_header, 
            browser_context=c, 
            page_url="/mkpl/global?mode=sub_page&hide_activity_pop=1"
        )

        # 执行加购减购流程测试
        with allure.step("执行加购减购流程测试"):
            result = mkpl_page.execute_add_to_cart_flow()
            
            assert result, "加购减购流程测试失败，最终数量不为0或操作过程中出现错误"
            log.info("✅ 加购减购流程测试成功")

        log.info("PC端全球购商品加购减购测试完成")