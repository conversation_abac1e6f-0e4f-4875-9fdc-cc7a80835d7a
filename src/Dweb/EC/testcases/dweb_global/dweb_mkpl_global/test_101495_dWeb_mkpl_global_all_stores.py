import allure
import pytest
from playwright.sync_api import Page
from urllib.parse import urlparse, parse_qs

from src.Dweb.EC.dweb_pages.dweb_page_global.dweb_page_mkpl_all_store.dweb_page_mkpl_global_all_stores import DWebMkplGlobalComprehensivePage
from src.config.weee.log_help import log


@allure.story("test_101495_dWeb_mkpl_global_all_stores")
class TestDWebMkplGlobalComprehensiveFlow:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("【101495】 PC全球购-综合流程测试")
    @pytest.mark.present
    def test_101495_dWeb_mkpl_global_comprehensive_flow(self, page: dict, pc_autotest_header, login_trace):
        """
        1. 访问：/mkpl/global?mode=sub_page&hide_activity_pop=1
        2. 等待页面加载完成 5秒
        3. 分别点击 Japan Korea USA Others Tab 每个tab中间间隔5秒
        4. Others tab点击完成后，等待5秒点击All stores tab
        5. 找到加购元素 并点击加购3次
        6. 再次点击减购元素1次
        7. 完成验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 构造全球购综合测试页面
        mkpl_comprehensive_page = DWebMkplGlobalComprehensivePage(
            p, 
            pc_autotest_header, 
            browser_context=c, 
            page_url="/mkpl/global?mode=sub_page&hide_activity_pop=1"
        )

        # 步骤1: 等待页面加载完成 5秒
        with allure.step("等待页面加载完成 5秒"):
            mkpl_comprehensive_page.wait_for_page_load(5)
            log.info("✅ 页面加载等待完成")

        # 步骤2: 分别点击 Japan Korea USA Others Tab，每个tab中间间隔5秒
        with allure.step("分别点击 Japan Korea USA Others Tab，每个tab中间间隔5秒"):
            # 点击Japan tab并验证URL
            with allure.step("点击Japan tab并验证URL"):
                japan_success = mkpl_comprehensive_page.click_japan_tab()
                if japan_success:
                    self._verify_url_key_parameter(p, "Japan", "japan")
                p.wait_for_timeout(5000)
            
            # 点击Korea tab并验证URL
            with allure.step("点击Korea tab并验证URL"):
                korea_success = mkpl_comprehensive_page.click_korea_tab()
                if korea_success:
                    self._verify_url_key_parameter(p, "Korea", "korea")
                p.wait_for_timeout(5000)
            
            # 点击USA tab并验证URL
            with allure.step("点击USA tab并验证URL"):
                usa_success = mkpl_comprehensive_page.click_usa_tab()
                if usa_success:
                    self._verify_url_key_parameter(p, "USA", "usa")
                p.wait_for_timeout(5000)
            
            # 点击Others tab并验证URL
            with allure.step("点击Others tab并验证URL"):
                others_success = mkpl_comprehensive_page.click_others_tab()
                if others_success:
                    self._verify_url_key_parameter(p, "Others", "others")
                p.wait_for_timeout(5000)
            
            # 点击All stores tab
            with allure.step("点击All stores tab"):
                all_stores_success = mkpl_comprehensive_page.click_all_stores_tab()
                p.wait_for_timeout(2000)
            
            # 验证至少有一个tab点击成功
            successful_tabs = sum([japan_success, korea_success, usa_success, others_success, all_stores_success])
            assert successful_tabs > 0, f"至少应该有一些tab点击成功，实际成功: {successful_tabs}/5"

        # 步骤3: 完成验证
        with allure.step("完成验证"):
            log.info("测试流程验证通过")

        log.info("全球购all stores 列表tab切换测试完成")
    
    def _verify_url_key_parameter(self, page: Page, tab_name: str, expected_key: str):
        """
        验证URL中的key参数是否正确

        Args:
            page: Playwright页面对象
            tab_name: tab名称，用于日志记录
            expected_key: 期望的key参数值
        """
        # 等待URL更新
        page.wait_for_timeout(1000)

        # 获取当前页面URL
        current_url = page.url
        log.info(f"当前页面URL: {current_url}")

        # 解析URL参数
        parsed_url = urlparse(current_url)
        query_params = parse_qs(parsed_url.query)

        # 获取key参数
        actual_key = query_params.get('key', [None])[0]

        # 断言key参数是否正确
        assert actual_key == expected_key, f"{tab_name}tab的URL key参数验证失败: 期望 '{expected_key}', 实际 '{actual_key}'"

        log.info(f"{tab_name}tab的URL key参数验证成功: key={actual_key}")
