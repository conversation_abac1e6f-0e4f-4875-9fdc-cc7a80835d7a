import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele import dweb_common_ele
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage


@allure.story("【108558】 购物车页面-切换日期验证")
class TestDWebChangeDeliveryDateOnCartPageUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108558】 购物车页面-切换日期验证")
    def test_108558_dWeb_change_delivery_date_on_cart_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108558】 购物车页面-切换日期验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 直接进入指定页面
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        # p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        # scroll_one_page_until(p, mweb_pdp_ele.ele_share)
        # 点击购物车里的切换日期按钮
        p.get_by_test_id(dweb_cart_ele.ele_cart_normal_delivery_date).click()
        # cart_page.FE.ele(dweb_cart_ele.ele_cart_normal_delivery_date).click()
        p.wait_for_timeout(3000)
        # 断言进入切换日期pop页面
        assert cart_page.FE.ele(dweb_common_ele.ele_delivery_date_popup).is_visible()
        # 点击切换日期
        delivery_data = cart_page.FE.eles(dweb_common_ele.ele_delivery_date)
        for index,item in enumerate(delivery_data) :
            item.click()
            if index==0:
                break

        p.wait_for_timeout(3000)
        # 断言回到购物车页面
        assert cart_page.FE.ele(dweb_cart_ele.ele_cart_normal_delivery_date).is_visible()

