import allure
import pytest

from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home.dweb_porder.dweb_address.dweb_address_ele import *
from src.Dweb.EC.dweb_pages.dweb_page_address.dweb_page_address import DWebAddressPage
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.config.weee.log_help import log

# 注意：所有元素定位都使用 page.get_by_test_id() 方法，而不是 XPath 或 CSS 选择器


@allure.story("PC端-新增地址UI/UX验证")
class TestDWebAddAddressUIUXV2:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("PC端-从首页新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_home_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从首页新增地址UI/UX验证
        此用例的校验点有：
        1. 在首页点击地址按钮（使用get_by_test_id定位），进入地址选择页面
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)
        
        # 点击首页地址按钮
        p.get_by_test_id(ele_home_zipcode).click()
        p.wait_for_timeout(2000)
        
        # 点击新增地址按钮
        p.get_by_test_id(zipcode_pop_add_btn).click()
        p.wait_for_timeout(3000)
        
        # 填写地址表单（使用公共方法，传入ORG4地址）
        address_page.fill_address_form_before_save(
            first_name="Test", 
            last_name="Automation", 
            phone="**********",
            street=SALES_ORG_ADDRESSES["ORG4"]["street"],
            city=SALES_ORG_ADDRESSES["ORG4"]["city"],
            state=SALES_ORG_ADDRESSES["ORG4"]["state"],
            zipcode=SALES_ORG_ADDRESSES["ORG4"]["zipcode"]
        )
        
        # 点击保存按钮
        p.get_by_test_id(save_btn).click()
        p.wait_for_timeout(3000)
        
        # 处理可能出现的"Not now"按钮
        if p.locator("//button[text()='Not now']").all():
            p.locator("//button[text()='Not now']").click()
            p.wait_for_timeout(2000)
            
        # 断言回到首页
        assert p.get_by_test_id("btn-main-banner-img-0").is_visible(), "未成功回到首页"

        # 点击首页zipcode按钮
        p.get_by_test_id(ele_home_zipcode).click()
        p.wait_for_timeout(2000)
        
        # 验证弹出 Change your address pop
        assert p.get_by_test_id(change_address_dialog).is_visible(), "未弹出 Change your address 弹窗"
        
        # 验证地址列表存在
        assert p.get_by_test_id(address_list).is_visible(), "地址列表不存在"
        
        # 验证地址列表项存在
        assert p.get_by_test_id(address_list_item).is_visible(), "地址列表项不存在"
        
        # 验证地址名称存在
        address_text = p.get_by_test_id(address_card_name).text_content()
        assert "Test Automation" in address_text, f"未找到新增的地址名称，实际文本：{address_text}"
        
        # 清理：删除测试地址
        address_page._delete_test_address()
        
        log.info("从首页添加新地址成功")

    @allure.title("PC端-从账户页面新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_account_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从账户页面新增地址UI/UX验证
        此用例的校验点有：
        1. 进入账户页面，点击地址管理（使用get_by_test_id定位）
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象,进入account页面
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c,page_url="/account")
        p.wait_for_timeout(3000)
        
        # 点击设置菜单项
        p.get_by_test_id(account_settings_menu_item).click()
        p.wait_for_timeout(2000)
        
        # 验证进入设置页面
        assert p.url.endswith("/account/settings"), "未进入设置页面"
        
        # 2. 点击Address book
        p.get_by_test_id(account_address_book).click()
        p.wait_for_timeout(2000)
        
        # 2. 验证弹出地址弹窗
        assert p.get_by_test_id(change_address_dialog).is_visible(), "未弹出地址弹窗"
        
        # 3. 验证弹窗标题
        assert p.get_by_test_id(change_zipcode_content).is_visible(), "弹窗内容不存在"
        assert p.get_by_test_id(change_zipcode_title).is_visible(), "弹窗标题不存在"
        
        # 4. 点击添加地址按钮
        p.get_by_test_id(add_address_btn).click()
        p.wait_for_timeout(3000)
        
        # 5. 验证进入Add a new address页面
        assert p.get_by_test_id(address_account_module).is_visible(), "未进入Add a new address页面"
        
        # 6. 填写地址表单（使用公共方法，传入ORG3地址）
        address_page.fill_address_form_before_save(
            first_name="Test", 
            last_name="Account", 
            phone="**********",
            street=SALES_ORG_ADDRESSES["ORG3"]["street"],
            city=SALES_ORG_ADDRESSES["ORG3"]["city"],
            state=SALES_ORG_ADDRESSES["ORG3"]["state"],
            zipcode=SALES_ORG_ADDRESSES["ORG3"]["zipcode"]
        )
        
        # 7. 点击取消按钮
        p.get_by_test_id(cancel_btn).click()
        p.wait_for_timeout(3000)
        
        # 验证返回账户页面
        assert p.url.endswith("/account/settings"), "未返回账户页面"
        
        # 8. 再次点击Address book
        # 首先确保在设置页面
        if not p.url.endswith("/account/settings"):
            p.goto("https://www.sayweee.com/account")
            p.wait_for_timeout(2000)
            p.get_by_test_id(account_settings_menu_item).click()
            p.wait_for_timeout(2000)
        
        p.get_by_test_id(account_address_book).click()
        p.wait_for_timeout(2000)
        
        # 9. 重复步骤2-5
        assert p.get_by_test_id(change_address_dialog).is_visible(), "未弹出地址弹窗"
        p.get_by_test_id(add_address_btn).click()
        p.wait_for_timeout(3000)
        assert p.get_by_test_id(address_account_module).is_visible(), "未进入Add a new address页面"
        
        # 填写地址表单
        address_page.fill_address_form_before_save(
            first_name="Test", 
            last_name="Account", 
            phone="**********",
            street=SALES_ORG_ADDRESSES["ORG3"]["street"],
            city=SALES_ORG_ADDRESSES["ORG3"]["city"],
            state=SALES_ORG_ADDRESSES["ORG3"]["state"],
            zipcode=SALES_ORG_ADDRESSES["ORG3"]["zipcode"]
        )
        
        # 10. 点击保存按钮
        p.get_by_test_id(save_btn).click()
        p.wait_for_timeout(3000)
        
        # 处理可能出现的"Not now"按钮
        if p.locator("//button[text()='Not now']").all():
            p.locator("//button[text()='Not now']").click()
            p.wait_for_timeout(2000)
        
        # 验证返回账户页面
        assert p.url.endswith("/account/settings"), "未返回账户页面"
        
        # 再次点击Address book验证地址添加成功
        # 首先确保在设置页面
        if not p.url.endswith("/account/settings"):
            p.goto("https://www.sayweee.com/account")
            p.wait_for_timeout(2000)
            p.get_by_test_id(account_settings_menu_item).click()
            p.wait_for_timeout(2000)
            
        p.get_by_test_id(account_address_book).click()
        p.wait_for_timeout(2000)
        
        # 验证地址列表存在
        assert p.get_by_test_id(address_list).is_visible(), "地址列表不存在"
        
        # 验证地址列表项存在
        assert p.get_by_test_id(address_list_item).is_visible(), "地址列表项不存在"
        
        # 验证地址名称存在
        address_text = p.get_by_test_id(address_card_name).text_content()
        assert "Test Account" in address_text, f"未找到新增的地址名称，实际文本：{address_text}"
        
        # 清理：删除测试地址
        address_page._delete_test_address()
        
        log.info("从账户页面添加新地址成功")

    @allure.title("PC端-从结算页面新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_checkout_ui_ux_v2(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从结算页面新增地址UI/UX验证
        此用例的校验点有：
        1. 进入结算页面，点击地址选择区域（使用get_by_test_id定位）
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c,page_url="/cart")
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c,page_url="/cart")

        p.wait_for_timeout(3000)
        log.info("进入首页")

        # 1. 点击购物按钮，进入购物车页面
        # p.get_by_test_id(cart_icon).click()
        # p.wait_for_timeout(1000)
        
        # 2. 判断购物车是否为空
        if p.get_by_test_id(cart_empty).is_visible():
            log.info("购物车为空，需要添加商品")
            # 滚动到推荐模块
            address_page.scroll_to_pos(cart_preference)
            p.wait_for_timeout(1000)
            # 加购3件商品
            cart_page.add_recommendation_product(2)
            p.wait_for_timeout(1000)
        
        # 3. 点击购物车的结算按钮
        p.get_by_test_id(cart_checkout_btn).click()
        p.wait_for_timeout(2000)
        
        # 4. 处理可能出现的中间页
        if p.get_by_test_id(cart_select_dialog).is_visible():
            log.info("出现中间页，需要处理")
            # 5. 点击全选
            p.get_by_test_id(btn_select_all_carts).click()
            p.wait_for_timeout(2000)
            
            # 确认选上
            assert p.get_by_test_id(btn_select_all_carts).get_attribute("data-selected") == "true", "全选按钮未被选中"
            
            # 6. 点击中间页上的结算按钮
            p.get_by_test_id(cart_select_checkout_btn).click()
            p.wait_for_timeout(5000)
        
        # 7. 处理可能出现的upsell pop
        if p.get_by_test_id(upsell_title).is_visible():
            log.info("出现upsell弹窗，需要处理")
            # 8. 点击继续结算
            p.get_by_test_id(upsell_continue_checkout_btn).click()
            p.wait_for_timeout(3000)
        
        # 9. 验证进入结算页
        assert "/order/checkout" in p.url, "未进入结算页面"
        
        # 10. 处理地址表单
        # 如果没有地址，直接填写表单
        if p.get_by_test_id(checkout_address_form_firstname_label).is_visible():
            log.info("没有地址，直接填写表单")
            # 10.1 填写地址表单
            address_page.fill_address_form_before_save(
                first_name="Test", 
                last_name="Checkout", 
                phone="**********",
                street=SALES_ORG_ADDRESSES["ORG1"]["street"],
                city=SALES_ORG_ADDRESSES["ORG1"]["city"],
                state=SALES_ORG_ADDRESSES["ORG1"]["state"],
                zipcode=SALES_ORG_ADDRESSES["ORG1"]["zipcode"]
            )
            
            # 10.2 点击保存
            p.get_by_test_id(save_btn).click()
            p.wait_for_timeout(5000)
            
            # 10.3 处理可能出现的upsell pop
            if p.get_by_test_id(upsell_title).is_visible():
                log.info("出现upsell弹窗，需要处理")
                # 10.4 点击继续结算
                p.get_by_test_id(upsell_continue_checkout_btn).click()
                p.wait_for_timeout(3000)
            
            # 10.5 验证地址添加成功
            assert p.get_by_test_id(checkout_address_info).is_visible(), "未显示地址信息"
            address_text = p.get_by_test_id(checkout_address_info).text_content()
            assert "Test Checkout" in address_text, f"未找到新增的地址名称，实际文本：{address_text}"
        
        # 11. 如果结算页有地址，点击新增地址按钮
        elif p.get_by_test_id(checkout_address_card).is_visible():
            log.info("结算页有地址，点击新增地址按钮")
            p.get_by_test_id(checkout_add_address_btn).click()
            p.wait_for_timeout(3000)
            
            # 12. 验证进入地址表单页面
            assert p.get_by_test_id(address_account_module).is_visible(), "未进入地址表单页面"
            
            # 13. 填写地址表单
            address_page.fill_address_form_before_save(
                first_name="Test", 
                last_name="Checkout2", 
                phone="**********",
                street=SALES_ORG_ADDRESSES["ORG2"]["street"],
                city=SALES_ORG_ADDRESSES["ORG2"]["city"],
                state=SALES_ORG_ADDRESSES["ORG2"]["state"],
                zipcode=SALES_ORG_ADDRESSES["ORG2"]["zipcode"]
            )
            
            # 14. 点击保存
            p.get_by_test_id(save_btn).click()
            p.wait_for_timeout(5000)
            
            # 处理可能出现的upsell pop
            if p.get_by_test_id(upsell_title).is_visible():
                log.info("出现upsell弹窗，需要处理")
                p.get_by_test_id(upsell_continue_checkout_btn).click()
                p.wait_for_timeout(3000)
            
            # 15. 验证地址添加成功
            assert p.get_by_test_id(checkout_address_card).is_visible(), "未显示地址卡片"
            address_text = p.get_by_test_id(checkout_address_card).text_content()
            assert "Test Checkout2" in address_text, f"未找到新增的地址名称，实际文本：{address_text}"
        
        # # 清理：删除测试地址
        # p.goto("https://www.sayweee.com/account/settings")
        # p.wait_for_timeout(3000)
        # address_page._delete_test_address()
        #
        # log.info("从结算页面添加新地址成功")


