import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home.dweb_porder.dweb_address import dweb_address_ele
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_porder.dweb_page_address.dweb_page_address import DWebPageAddress
from src.config.weee.log_help import log

@allure.story("[110862]首页/checkout/acccount/order detail-编辑&应用地址验证")
class TestDWebModifyAddressUIUX():
    pytestmark = [pytest.mark.pcorder, pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]
    
    def _edit_address_info(self, p: Page, address_page: DWebPageAddress, first_name="Modified", last_name="Automation", phone="9876543210", note="PC UI自动化"):
        """
        编辑地址信息的公共方法
        
        Args:
            p: Page对象
            address_page: DWebPageAddress对象
            first_name: 名字，默认为"Modified"
            last_name: 姓氏，默认为"Automation"
            phone: 电话，默认为"9876543210"
            note: 备注，默认为"PC UI自动化"
        """
        log.info("编辑地址信息")
        # 验证Delivery Info弹窗显示
        assert p.get_by_test_id(dweb_address_ele.address_first_name).is_visible(), "Delivery Info弹窗未显示"
        
        # 修改姓名和电话
        first_name_input = p.get_by_test_id(dweb_address_ele.address_first_name)
        first_name_input.clear()
        first_name_input.fill(first_name)
        
        last_name_input = p.get_by_test_id(dweb_address_ele.address_last_name)
        last_name_input.clear()
        last_name_input.fill(last_name)
        
        phone_input = p.get_by_test_id(dweb_address_ele.address_phone)
        phone_input.clear()
        phone_input.fill(phone)
        
        # 添加备注
        note_input = p.get_by_test_id(dweb_address_ele.address_note)
        note_input.clear()
        note_input.fill(note)
        
        # 验证街道、城市、zipcode已自动填充
        assert p.get_by_test_id(dweb_address_ele.address_street).input_value() != "", "街道未自动填充"
        assert p.locator(dweb_address_ele.address_city).input_value() != "", "城市未自动填充"
        assert p.get_by_test_id(dweb_address_ele.address_zipcode).input_value() != "", "邮编未自动填充"
        
        # 点击保存
        p.get_by_test_id(dweb_address_ele.address_save_button).click()
        p.wait_for_timeout(2000)

    @allure.title("【110862】 从首页-编辑地址验证")
    def test_110862_dWeb_modify_address_from_home_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【110862】 从首页-编辑地址验证
        1. 点击首页右上角的zipcode/地址区域 data-testid="wid-home-address-selector"
        2. 验证弹出地址选择弹窗 data-testid="wid-delivery-address-modal"
        3. 判断是否有地址卡片 data-testid="wid-address-card"
        4. 如果有地址，点击编辑按钮 data-testid="btn-edit-address"
        4.1 进入 Delivery Info 编辑地址弹窗
        4.2 按照页面修改地址 姓名、电话、备注
        4.3 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        4.4 点击保存 data-testid="btn-save-address"，回到首页
        5. 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = page.get("page")
        c = page.get("context")
        address_page = DWebPageAddress(p, pc_autotest_header, browser_context=c)

        # 1. 点击首页右上角的zipcode/地址区域
        log.info("步骤1：点击首页右上角的zipcode/地址区域")
        p.get_by_test_id(dweb_address_ele.home_address_selector).click()
        p.wait_for_timeout(1000)

        # 2. 验证弹出地址选择弹窗
        log.info("步骤2：验证弹出地址选择弹窗")
        assert p.get_by_test_id(dweb_address_ele.delivery_address_modal).is_visible(), "地址选择弹窗未显示"

        # 3. 判断是否有地址卡片
        log.info("步骤3：判断是否有地址卡片")
        try:
            has_address = p.get_by_test_id(dweb_address_ele.address_card).is_visible(timeout=3000)
        except:
            has_address = False

        if has_address:
            # 4. 有地址，点击编辑
            log.info("步骤4：有地址，点击编辑")
            edit_btn = p.get_by_test_id(dweb_address_ele.edit_address_button)
            assert edit_btn.is_visible(), "编辑按钮不可见"
            edit_btn.click()
            p.wait_for_timeout(2000)

            # 4.1-4.4 编辑地址信息并保存
            log.info("步骤4.1-4.4：编辑地址信息并保存")
            self._edit_address_info(p, address_page)
            assert p.get_by_test_id(dweb_address_ele.home_main_banner).is_visible(), "未成功回到首页"
            log.info("从首页编辑地址测试完成")
        else:
            log.info("没有地址可编辑，跳过测试")
            try:
                close_btn = p.get_by_test_id(dweb_address_ele.close_delivery_dialog)
                if close_btn.is_visible():
                    close_btn.click()
                    p.wait_for_timeout(1000)
            except:
                log.info("关闭按钮不存在或不可见，尝试其他方式关闭")
                try:
                    # 尝试点击弹窗外部区域关闭
                    p.keyboard.press("Escape")
                    p.wait_for_timeout(1000)
                except:
                    log.info("无法关闭弹窗")

    @allure.title("【110862】 从Account页-设置页面-编辑地址验证")
    def test_110862_dWeb_modify_address_from_account_setting_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【110862】 从Account页-设置页面-编辑地址验证
        1. 点击右上角的用户头像/名称 data-testid="wid-user-profile-dropdown"
        2. 在下拉菜单中点击"设置" data-testid="wid-user-settings"
        3. 在左侧菜单中点击"地址簿" data-testid="wid-address-book-menu"
        4. 如果有地址 data-testid="wid-address-item"，点击第一个地址的编辑按钮 data-testid="btn-edit-address"
        4.1 进入 Delivery Info 编辑地址弹窗
        4.2 按照页面修改地址 姓名、电话、备注
        4.3 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        4.4 点击保存 data-testid="btn-save-address"，弹出是否更换地址确认弹窗 data-testid="wid-modal-confirm"
        4.5 点击取消 data-testid="btn-confirm-close"，回到 address book页面
        4.6 再次点击编辑按钮 data-testid="btn-edit-address"
        4.7 重复4.2-4.4步骤
        4.8 点击确认更换按钮 data-testid="btn-confirm-ok"，回到 address book页面
        4.9 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = page.get("page")
        c = page.get("context")
        address_page = DWebPageAddress(p, pc_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)

        # 1. 点击右上角的用户头像/名称
        log.info("步骤1：点击右上角的用户头像/名称")
        p.get_by_test_id(dweb_address_ele.user_profile_dropdown).click()
        p.wait_for_timeout(1000)
        
        # 2. 在下拉菜单中点击"设置"
        log.info("步骤2：在下拉菜单中点击")
        p.get_by_test_id(dweb_address_ele.user_settings).click()
        p.wait_for_timeout(1000)
        
        # 3. 在左侧菜单中点击"地址簿"
        log.info("步骤3：在左侧菜单中点击")
        p.get_by_test_id(dweb_address_ele.address_book_menu).click()
        p.wait_for_timeout(1000)
        
        # 4. 判断是否有地址
        log.info("步骤4：判断是否有地址")
        try:
            has_address = p.get_by_test_id(dweb_address_ele.address_item).is_visible(timeout=3000)
        except:
            has_address = False
        
        if has_address:
            # 4.1 点击第一个地址的编辑按钮
            log.info("步骤4.1：点击第一个地址的编辑按钮")
            address_items = p.get_by_test_id(dweb_address_ele.address_item).all()
            first_address = address_items[0]
            first_address.get_by_test_id(dweb_address_ele.btn_edit_address).click()
            p.wait_for_timeout(2000)
            
            # 4.2-4.4 编辑地址信息，先取消再确认
            log.info("步骤4.2-4.4：编辑地址信息，先取消再确认")
            self._edit_address_info(p, address_page, first_name="Modified", last_name="Account", phone="**********", note="从Account设置页编辑")
            
            # 4.5 验证确认弹窗并点击取消
            try:
                has_confirm_modal = p.get_by_test_id(dweb_address_ele.confirm_modal).is_visible(timeout=3000)
            except:
                has_confirm_modal = False
                
            if has_confirm_modal:
                p.get_by_test_id(dweb_address_ele.btn_confirm_close).click()
                p.wait_for_timeout(1000)
            
            # 4.6-4.7 再次编辑并确认
            address_items = p.get_by_test_id(dweb_address_ele.address_item).all()
            if len(address_items) > 0:
                first_address = address_items[0]
                first_address.get_by_test_id(dweb_address_ele.btn_edit_address).click()
                p.wait_for_timeout(2000)
                self._edit_address_info(p, address_page, first_name="Final", last_name="Test", phone="**********", note="最终编辑")
                
                # 4.8 点击确认
                try:
                    has_confirm_modal = p.get_by_test_id(dweb_address_ele.confirm_modal).is_visible(timeout=3000)
                except:
                    has_confirm_modal = False
                    
                if has_confirm_modal:
                    p.get_by_test_id(dweb_address_ele.btn_confirm_ok).click()
                    p.wait_for_timeout(2000)
            
            log.info("从Account设置页编辑地址测试完成")
        else:
            log.info("没有地址可编辑，跳过测试")

    @allure.title("【110862】 从结算页面-编辑地址验证")
    def test_110862_dWeb_modify_address_from_checkout_page_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【110862】 从结算页面-编辑地址验证
        1. 点击右上角购物车图标 data-testid="wid-cart-icon" 进入购物车
        2. 点击结算按钮 data-testid="wid-checkout-button"
        3. 在结算页面找到地址区域 data-testid="wid-checkout-address-section"
        4. 如果有地址，点击编辑按钮 data-testid="wid-checkout-edit-address"
        4.1 进入 Delivery Info 编辑地址弹窗
        4.2 按照页面修改地址 姓名、电话、备注
        4.3 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        4.4 点击保存 data-testid="btn-save-address"，回到结算页面
        5. 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = page.get("page")
        c = page.get("context")
        address_page = DWebPageAddress(p, pc_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)

        # 1. 点击右上角购物车图标
        log.info("步骤1：点击右上角购物车图标")
        try:
            cart_btn = p.get_by_test_id(dweb_address_ele.cart_icon)
            if cart_btn.is_visible():
                cart_btn.click()
                p.wait_for_timeout(2000)
            else:
                log.info("购物车图标不可见，直接进入结算页")
                p.goto("/checkout")
                p.wait_for_timeout(3000)
        except:
            log.info("购物车图标不存在，直接进入结算页")
            p.goto("/checkout")
            p.wait_for_timeout(3000)

        # 2. 点击结算按钮（如果在购物车页面）
        try:
            checkout_btn = p.get_by_test_id(dweb_address_ele.checkout_button)
            if checkout_btn.is_visible():
                log.info("步骤2：点击结算按钮")
                checkout_btn.click()
                p.wait_for_timeout(3000)
        except:
            log.info("已在结算页面或结算按钮不存在")

        # 3. 在结算页面找到地址区域
        log.info("步骤3：在结算页面找到地址区域")
        try:
            address_section = p.get_by_test_id(dweb_address_ele.checkout_address_section)
            has_address_section = address_section.is_visible(timeout=5000)
        except:
            has_address_section = False

        if has_address_section:
            # 4. 如果有地址，点击编辑按钮
            log.info("步骤4：有地址区域，查找编辑按钮")
            try:
                edit_btn = p.get_by_test_id(dweb_address_ele.checkout_edit_address)
                if edit_btn.is_visible():
                    edit_btn.click()
                    p.wait_for_timeout(2000)
                    
                    # 4.1-4.4 编辑地址信息并保存
                    log.info("步骤4.1-4.4：编辑地址信息并保存")
                    self._edit_address_info(p, address_page, first_name="Checkout", last_name="Edit", phone="5555555555", note="从结算页编辑")
                    
                    # 验证回到结算页面
                    try:
                        checkout_indicator = p.get_by_test_id(dweb_address_ele.checkout_address_section)
                        assert checkout_indicator.is_visible(), "未成功回到结算页面"
                        log.info("从结算页编辑地址测试完成")
                    except:
                        log.info("编辑地址完成，但无法验证是否回到结算页")
                else:
                    log.info("编辑按钮不可见，跳过测试")
            except:
                log.info("找不到编辑按钮，跳过测试")
        else:
            log.info("没有地址区域或不在结算页面，跳过测试")