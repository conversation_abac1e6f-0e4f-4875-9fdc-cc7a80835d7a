import allure
import pytest

from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_address.dweb_page_address import DWebAddressPage
from src.config.weee.log_help import log

# 注意：所有元素定位都使用 page.get_by_test_id() 方法，而不是 XPath 或 CSS 选择器


@allure.story("PC端-新增地址UI/UX验证")
class TestDWebAddAddressUIUX:
    pytestmark = [pytest.mark.dweb_regression]

    @allure.title("PC端-从首页新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_home_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从首页新增地址UI/UX验证
        此用例的校验点有：
        1. 在首页点击地址按钮（使用get_by_test_id定位），进入地址选择页面
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)

        # 从首页添加新地址
        address_page.add_new_address_from_home()

    @allure.title("PC端-从账户页面新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_account_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从账户页面新增地址UI/UX验证
        此用例的校验点有：
        1. 进入账户页面，点击地址管理（使用get_by_test_id定位）
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)

        # 从账户页面添加新地址
        address_page.add_new_address_from_account()

    @allure.title("PC端-从结算页面新增地址UI/UX验证")
    def test_110841_dWeb_add_address_from_checkout_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从结算页面新增地址UI/UX验证
        此用例的校验点有：
        1. 进入结算页面，点击地址选择区域（使用get_by_test_id定位）
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)

        # 从结算页面添加新地址
        address_page.add_new_address_from_checkout()

    @allure.title("PC端-从订单详情页新增地址UI/UX验证")
    @pytest.mark.skip(reason="not implemented yet")
    def test_dWeb_add_address_from_order_detail_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-从订单详情页新增地址UI/UX验证
        此用例的校验点有：
        1. 进入订单详情页，点击更改地址（使用get_by_test_id定位）
        2. 点击"新增地址"按钮（使用get_by_test_id定位），进入新增地址页面
        3. 填写地址信息（街道地址、姓名、电话、备注）
        4. 保存地址，验证地址簿中新增了一条地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)

        # 从订单详情页添加新地址（需要有订单ID）
        # 这里使用一个示例订单ID，实际使用时需要替换为真实的订单ID
        order_id = "example_order_id"
        address_page.add_new_address_from_order_detail(order_id)

    @allure.title("PC端-多页面新增地址综合UI/UX验证")
    @pytest.mark.skip(reason="not implemented yet")
    def test_dWeb_add_address_comprehensive_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        PC端-多页面新增地址综合UI/UX验证
        此用例综合验证从不同页面新增地址的功能：
        1. 从首页新增地址（所有元素定位都使用get_by_test_id方法）
        2. 从账户页面新增地址（所有元素定位都使用get_by_test_id方法）
        3. 从结算页面新增地址（所有元素定位都使用get_by_test_id方法）
        每次新增地址后都会验证地址簿中是否新增了地址，并在测试后删除测试地址
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 创建地址页面对象
        address_page = DWebAddressPage(p, pc_autotest_header, browser_context=c)

        try:
            # 从首页添加新地址
            log.info("开始从首页添加新地址...")
            address_page.add_new_address_from_home()

            # 从账户页面添加新地址
            log.info("开始从账户页面添加新地址...")
            address_page.add_new_address_from_account()

            # 从结算页面添加新地址
            log.info("开始从结算页面添加新地址...")
            address_page.add_new_address_from_checkout()

            log.info("多页面新增地址综合测试完成")
        except Exception as e:
            log.error(f"多页面新增地址综合测试失败: {str(e)}")
            raise
