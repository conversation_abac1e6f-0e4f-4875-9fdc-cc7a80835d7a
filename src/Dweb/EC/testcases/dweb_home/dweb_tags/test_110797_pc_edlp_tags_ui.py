import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import ele_pc_home_everyday_value
from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import ele_pc_pdp_everyday_value
from src.common.commonui import scroll_one_page_until


class TestDwebPageEdlp:
    # 验证pc端 首页，分类，pdp ，搜索结果页面一级页面对应 edlp标签展示
    def test_dweb_home_page_everyday_value_tags(self, page: dict, pc_autotest_header):
        # 验证pc端home page edlp标签展示
        p: Page = page.get("page")
        p.goto("https://www.sayweee.com/en")
        _visible = None

        for i in range(20):
            scroll_one_page_until(p, ele_pc_home_everyday_value)
            _visible = p.locator(ele_pc_home_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")


    def test_dweb_category_page_everyday_value_tags(self, page: dict, pc_autotest_header):
        # 验证pc端分类页edlp标签展示
        p: Page = page.get("page")
        p.goto("https://www.sayweee.com/en/category/trending?filter_sub_category=trending")
        _visible = None

        for i in range(20):
            scroll_one_page_until(p, ele_pc_home_everyday_value)
            _visible = p.locator(ele_pc_home_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")

    def test_dweb_search_page_everyday_value_tags(self, page: dict, pc_autotest_header):
        # 验证pc端搜索结果页edlp标签展示
        p: Page = page.get("page")
        p.goto("https://www.sayweee.com/en/search/seafood?keyword=seafood&trigger_type=search_active")
        #
        _visible = None

        for i in range(20):
            scroll_one_page_until(p, ele_pc_home_everyday_value)
            _visible = p.locator(ele_pc_home_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")

    def test_dweb_pdp_page_everyday_value_tags(self, page: dict, pc_autotest_header):
        # 验证pc端pdp 页面edlp标签展示
        p: Page = page.get("page")
        p.goto("https://www.sayweee.com/en/product/Three-Ladies-Brand-Jasmine-Rice-Large-Bag/23063?category=dried01&parent_category=dried")
        p.wait_for_timeout(3000)
        _visible = None

        for i in range(20):
            scroll_one_page_until(p, ele_pc_pdp_everyday_value)
            _visible = p.locator(ele_pc_pdp_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")