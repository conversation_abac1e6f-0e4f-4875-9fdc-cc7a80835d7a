# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/7/21
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest
import random
import string

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_ftu import DWebTimeBannerPage
from playwright.sync_api import Page
from src.Dweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("Dweb-注册-timebar-未登录用户time bar的功能验证")
class TestDwebFtuEmailSignup:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.dweb_wangyue]

    @allure.title("dweb-注册-timebar-未登录用户time bar的功能验证")
    @pytest.mark.time_banner
    def test_110289_dweb_ftu_email_signup_verify(self, pc_autotest_header, not_login_page):
        "dweb-注册-timebar-未登录用户time bar的功能验证"
        p: Page = not_login_page.get("page")
        c = not_login_page.get("context")
        pc_home_page = DWebTimeBannerPage(p, pc_autotest_header, c)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)

        with allure.step("验证FTU Popup是否存在"):
            if pc_home_page.p_verify_ftu_visibility():
                random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
                random_email = f"{random_string}@example.com"
                # 点击邮箱注册
                pc_home_page.p_click_email_signup()
                # 输入随机生成的邮箱
                email_input = p.get_by_test_id("wid-ftu-login-method-dialog-email-input")
                email_input.fill(random_email)
                p.wait_for_timeout(5000)
                log.info(f"使用邮箱 {random_email} 完成注册流程")

            else:
                # FTU不存在
                log.info("FTU不存在，无法验证该case")