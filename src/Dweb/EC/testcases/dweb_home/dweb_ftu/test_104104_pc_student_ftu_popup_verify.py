# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/7/18
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_ftu import DWebTimeBannerPage
from playwright.sync_api import Page
from src.Dweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

@allure.story("Dweb-student-ftu pop up UI/UX验证")
class TestDwebStudentFtu:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.dweb_wangyue]

    @allure.title("dweb-student-ftu pop up UI/UX验证")
    @pytest.mark.ftu
    def test_104104_pc_student_ftu_popup_verify(self, pc_autotest_header, not_login_page,page_url: str = "student"):
        "dweb-student-ftu pop up UI/UX验证"
        p: Page = not_login_page.get("page")
        c = not_login_page.get("context")
        pc_home_page = DWebTimeBannerPage(p, pc_autotest_header, c)
        p.goto(TEST_URL + "/" + page_url)
        p.wait_for_timeout(10000)

        with allure.step("验证FTU Popup是否存在"):
            if pc_home_page.p_verify_ftu_visibility():
                log.info("FTU Popup验证成功")
            else:
                log.info("FTU Popup验证失败,studentpopup不存在")

