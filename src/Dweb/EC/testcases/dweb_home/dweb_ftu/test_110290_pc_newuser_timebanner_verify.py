# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/6/17
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest
import re

from playwright.sync_api import Page, expect
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_ftu import DWebTimeBannerPage
from playwright.sync_api import Page
from src.Dweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log

@allure.story("Dweb-注册-timebar-登录用户time bar的功能验证")
class TestDwebNewuserTimebanner:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.dweb_wangyue]

    @allure.title("dweb-注册-timebar-登录用户time bar的功能验证")
    @pytest.mark.time_banner
    def test_110290_dweb_newuser_timebanner_click_verify(self, page: dict, pc_autotest_header, login_trace):
        "dweb-注册-timebar-登录用户time bar的功能验证"
        p: Page = page.get("page")
        c = page.get("context")
        pc_homepage = DWebTimeBannerPage(p, pc_autotest_header, c)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)

        # 1.验证time banner是否存在
        with allure.step("验证Time Banner是否存在"):
            try:
                pc_homepage.p_verify_time_banner_visibility()
                # Time Banner存在，执行点击操作
                with allure.step("点击Time Banner"):
                    pc_homepage.p_click_time_banner()

                # 验证是否跳转到https://click.sayweee.com域名的页面
                with allure.step("验证跳转"):
                    try:
                        # 等待页面加载完成
                        p.wait_for_load_state('networkidle')
                        # 检查当前URL是否包含https://click.sayweee.com
                        expect(p).to_have_url(re.compile(r"https://click\.sayweee\.com"))
                        log.info("成功跳转到https://click.sayweee.com域名的页面")
                    except:
                        log.error("点击失败，未跳转到https://click.sayweee.com域名的页面")
                        raise AssertionError("点击失败，未跳转到预期页面")
                #返回测试url
                with allure.step("返回测试页面"):
                    p.goto(TEST_URL)
                    p.wait_for_load_state('networkidle')

            except AssertionError:
                log.info("Time Banner 不存在")
                allure.step("Time Banner 不存在，跳过后续操作")

        log.info("Time Banner验证完成")


