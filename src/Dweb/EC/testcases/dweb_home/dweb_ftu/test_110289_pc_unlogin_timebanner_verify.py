# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/6/17
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_ftu import DWebTimeBannerPage
from playwright.sync_api import Page
from src.Dweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("Dweb-注册-timebar-未登录用户time bar的功能验证")
class TestDwebUNLOGINTimebanner:
    pytestmark = [pytest.mark.dweb_regression, pytest.mark.dweb_wangyue]

    @allure.title("dweb-注册-timebar-未登录用户time bar的功能验证")
    @pytest.mark.time_banner
    def test_110289_dweb_unlogin_timebanner_click_verify(self, pc_autotest_header, not_login_page):
        "dweb-注册-timebar-未登录用户time bar的功能验证"
        p: Page = not_login_page.get("page")
        c = not_login_page.get("context")
        pc_home_page = DWebTimeBannerPage(p, pc_autotest_header, c)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)

        # 1.验证FTU popup是否存在并关闭

        with allure.step("验证FTU Popup是否存在"):
            if pc_home_page.p_verify_ftu_visibility():
                pc_home_page.p_close_ftu()
            else:
                log.info("FTU Popup 不存在")

        # 2.验证Time Banner是否存在
        with allure.step("验证Time Banner是否存在"):
            try:
                pc_home_page.p_verify_time_banner_visibility()
                # 3.Time Banner存在，执行点击和关闭操作
                with allure.step("点击Time Banner"):
                    pc_home_page.p_click_time_banner()
                    pc_home_page.p_close_ftu()

                with allure.step("关闭Time Banner"):
                    pc_home_page.p_close_time_banner()

                # 4.验证Time Banner是否已关闭
                with allure.step("验证Time Banner已关闭"):
                    pc_home_page.p_verify_time_banner_closed()
            except AssertionError:
                log.info("Time Banner 不存在")
                allure.step("Time Banner 不存在，跳过后续操作")

        log.info("Time Banner验证完成")


