import allure
import pytest
from playwright.sync_api import Page, expect, Locator

from src.Dweb.EC.conftest import page
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_search_by_category_at_home import HomeSearchPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Dweb.EC.dweb_pages.dweb_page_search import ele_search_input


@allure.story("PC-首页-搜索页面联想商品UI/UX验证")
class TestPCSearchFunction:
    pytestmark = [
        pytest.mark.pc_regression,
        pytest.mark.pc_search
    ]

    @allure.title("PC-首页-搜索页面联想商品UI/UX验证")
    def test_homepage_search_suggestion(self, page: dict, pc_autotest_header, login_trace):
        """
        测试步骤：
        1. 访问首页验证搜索框默认文案
        2. 输入apple关键词
        3. 验证联想结果的图片和文案
        4. 点击联想词跳转结果页
        5. 验证结果页URL和商品
        """

        p: Page = page.get("page")
        c = page.get("context")
        search_page = HomeSearchPage(p, pc_autotest_header)
        test_keyword = "apple"
        expected_url_pattern = f"https://www.sayweee.com/en/search/{test_keyword}?keyword={test_keyword}&trigger_type=search_suggestion"

        # === 1. 访问首页并验证默认状态 ===
        p.goto(TEST_URL)
        p.wait_for_load_state("load")

        p.get_by_test_id("wid-main-search-fouced").click()

        log.info("验证默认placeholder文案成功")

        # === 2. 输入关键词 ===

        p.get_by_test_id("wid-main-search-fouced").fill(test_keyword)
        p.wait_for_selector("div[class^='grid']", state="visible", timeout=5000)
        log.info(f"已输入关键词: {test_keyword}")

        # === 3. 验证联想结果 ===
        # 验证图片
        suggestion_images = p.locator("//img[@alt='picture']")
        expect(suggestion_images.first).to_be_visible()
        expect(suggestion_images.first).to_have_attribute("alt", "picture")

        # 验证文案
        first_suggestion = p.locator(f"//div[@title='{test_keyword}']").first
        suggestion_text = first_suggestion.text_content()
        expect(first_suggestion).to_contain_text(test_keyword)
        log.info(f"首条联想建议内容: {suggestion_text}")

        # === 4. 点击联想词 ===
        first_suggestion.click()
        p.wait_for_timeout(4000)
        log.info("已点击首条联想建议")

        # === 5. 验证结果页 ===
        # URL验证
        expect(p).to_have_url(expected_url_pattern)

        # 商品验证
        product_cards = p.locator("[data-testid='wid-product-card-container']")
        expect(product_cards.first).to_be_visible()


        titles = p.get_by_test_id("wid-product-card-title").all()
        assert titles, f"products not found, test_keyword={test_keyword}"
        for t in titles:
            assert test_keyword.lower() in t.text_content().lower(), f"test_keyword={test_keyword}, content={t.text_content()}"




