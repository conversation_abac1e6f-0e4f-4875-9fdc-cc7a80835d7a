import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_search_by_category_at_home import HomeSearchPage


@allure.story("首页搜索")
class TestSearchAtHome:
    pytestmark = [pytest.mark.dweb_regression]
    @allure.title("首页按任意关键字搜索，关键字为{data}")
    @pytest.mark.parametrize("data", ["fruit", "tofu", "rice"])
    def test_search_by_category(self, page: dict, data, pc_autotest_header, login_trace):
        hsp = HomeSearchPage(page["page"], pc_autotest_header)
        hsp.search(data)

    @allure.title("首页按热词搜索")
    def test_search_by_hotkey(self, page: dict, pc_autotest_header, login_trace):
        hsp = HomeSearchPage(page["page"], pc_autotest_header)
        hsp.hot_key_search()
