import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home_add_item_to_cart import AddItemToCartPage


@allure.story("加入购物车")
class TestAddItemToCartPage:

    @allure.title("首页各合集加入购物车")
    def _test_add_to_cart(self, page: dict, pc_autotest_header, login_trace):
        aitc = AddItemToCartPage(page["page"], pc_autotest_header)
        aitc.add_to_cart()

    @allure.title("首页下滑")
    def _test_home_scroll(self, page: dict, pc_autotest_header, login_trace):
        aitc = AddItemToCartPage(page["page"], pc_autotest_header)
        aitc.scroll_to_bottom()


