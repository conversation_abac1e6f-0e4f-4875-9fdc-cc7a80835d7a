import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案 ")
class TestDWebPDPReviewListUIUX:
  
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【100616】 验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案")
    def test_global_fbw_pdp_info_ui(self, page: dict, pc_autotest_header, login_trace):
        """
        【100616】验证Global+ FBW 商品PDP查看Fulfilled by weee模块文案
        测试步骤：
        1. 访问Global FBW商品PDP页面
        2. 校验页面基本元素
        3. 校验Weee配送信息模块主容器
        4. 验证配送图标元素
        5. 验证配送标题和Weee logo
        6. 验证配送副标题信息
        7. 验证模块样式和布局
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接进入Global FBW商品pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/2X-Iron-Spatula-Cooking-Spoon-Spatula-Long-Beech-Handle/2120534")

        p.wait_for_timeout(5000)

        # 2. 校验Weee配送信息模块主容器存在
        info_module = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info)
        assert info_module.is_visible(), "Global FBW配送信息模块不可见"

        # 验证主容器的data-testid属性
        module_testid = info_module.get_attribute("data-testid")
        assert module_testid == "wid-product-promotion-fulfilled-by-weee", f"模块testid不正确: {module_testid}"

        # 3. 校验配送图标元素
        info_icon = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_icon)
        assert info_icon.is_visible(), "配送图标不可见"

        # 验证图标的data-testid属性
        icon_testid = info_icon.get_attribute("data-testid")
        assert icon_testid == "wid-product-promotion-fulfilled-by-weee-icon", f"图标testid不正确: {icon_testid}"

        # 验证图标属性
        icon_alt = info_icon.get_attribute("alt")
        assert icon_alt == "promotion fulfilled by weee", f"图标alt属性不正确: {icon_alt}"

        icon_width = info_icon.get_attribute("width")
        icon_height = info_icon.get_attribute("height")
        assert icon_width == "30" and icon_height == "30", f"图标尺寸不正确: {icon_width}x{icon_height}"

        # 验证图标src属性
        icon_src = info_icon.get_attribute("src")
        assert "weeecdn.net" in icon_src, f"图标src不正确: {icon_src}"

        print(f"配送图标验证完成 - 尺寸: {icon_width}x{icon_height}, alt: {icon_alt}")

        # 4. 校验配送标题元素
        title_element = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_title)
        assert title_element.is_visible(), "配送标题不可见"

        # 验证标题的data-testid属性
        title_testid = title_element.get_attribute("data-testid")
        assert title_testid == "wid-product-promotion-fulfilled-by-weee-title", f"标题testid不正确: {title_testid}"

        title_text = title_element.text_content()
        assert "Fulfilled by" in title_text, f"标题文本不包含'Fulfilled by': {title_text}"

        # 5. 校验内嵌的Weee logo
        weee_logo = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_weee_logo)
        assert weee_logo.is_visible(), "Weee logo不可见"

        logo_width = weee_logo.get_attribute("width")
        logo_height = weee_logo.get_attribute("height")
        assert logo_width == "41" and logo_height == "12", f"Weee logo尺寸不正确: {logo_width}x{logo_height}"

        logo_src = weee_logo.get_attribute("src")
        assert "weeecdn.com" in logo_src, f"Weee logo src不正确: {logo_src}"

        print(f"配送标题验证完成 - 文本: {title_text}")
        print(f"Weee logo验证完成 - 尺寸: {logo_width}x{logo_height}")

        # 6. 校验配送副标题元素
        subtitle_element = pdp_page.FE.ele(dweb_pdp_ele.ele_pdp_global_fbw_info_subtitle)
        assert subtitle_element.is_visible(), "配送副标题不可见"

        # 验证副标题的data-testid属性
        subtitle_testid = subtitle_element.get_attribute("data-testid")
        assert subtitle_testid == "wid-product-promotion-fulfilled-by-weee-sub-title", f"副标题testid不正确: {subtitle_testid}"

        subtitle_text = subtitle_element.text_content()
        assert "Sold by" in subtitle_text, f"副标题不包含销售商信息: {subtitle_text}"
        assert "Delivered with your groceries" in subtitle_text, f"副标题不包含配送信息: {subtitle_text}"

        # 验证副标题中包含具体的销售商名称
        assert "·" in subtitle_text, f"副标题格式不正确，缺少分隔符: {subtitle_text}"

        print(f"配送副标题验证完成 - 文本: {subtitle_text}")

        # 7. 验证模块样式和布局
        module_classes = info_module.get_attribute("class")
        expected_classes = ["flex", "items-center", "justify-between", "py-2.5"]
        for expected_class in expected_classes:
            assert expected_class in module_classes, f"模块缺少样式类'{expected_class}': {module_classes}"

        # 8. 验证标题元素的样式
        title_classes = title_element.get_attribute("class")
        assert "enki-body-2xs-medium" in title_classes, f"标题样式类不正确: {title_classes}"
        assert "text-black" in title_classes, f"标题颜色类不正确: {title_classes}"

        # 9. 验证副标题元素的样式
        subtitle_classes = subtitle_element.get_attribute("class")
        assert "enki-body-3xs" in subtitle_classes, f"副标题样式类不正确: {subtitle_classes}"
        assert "text-[#777]" in subtitle_classes, f"副标题颜色类不正确: {subtitle_classes}"
        assert "mt-1" in subtitle_classes, f"副标题间距类不正确: {subtitle_classes}"

        # 10. 验证模块在页面中的位置（通常在商品信息区域）
        module_position = info_module.bounding_box()
        assert module_position is not None, "无法获取模块位置信息"
        assert module_position['y'] > 0, "模块位置异常"
        print("Global FBW Weee配送信息模块UI/UX验证全部完成")
        # 11. 输出完整的验证结果摘要
        print("=" * 50)
        print("验证结果摘要:")
        print(f"✓ 模块容器: {module_testid}")
        print(f"✓ 配送图标: {icon_testid} ({icon_width}x{icon_height})")
        print(f"✓ 配送标题: {title_testid}")
        print(f"✓ Weee Logo: 尺寸 {logo_width}x{logo_height}")
        print(f"✓ 配送副标题: {subtitle_testid}")
        print(f"✓ 标题内容: {title_text}")
        print(f"✓ 副标题内容: {subtitle_text}")
        print("=" * 50)