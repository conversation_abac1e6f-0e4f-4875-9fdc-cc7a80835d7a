import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【112255】 PDP-晒单数量及跳转晒单列表页流程验证")
class TestDWebPDPReviewListUIUX:
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【112255】 PDP-晒单数量及跳转晒单列表页流程验证")
    def test_112255_dWeb_pdp_review_list_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112255】 PDP-晒单数量及跳转晒单列表页流程验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 1.直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460")
        # 2.滚动到指定位置
        scroll_one_page_until(p, dweb_pdp_ele.ele_mod_review)
        # pdp_page.FE.ele(pdp_elements.ele_mod_review).hover()
        # p.get_by_test_id("wid-slide-panel-arrow-right").click()
        # p.get_by_test_id("btn-start-over").click()
        # 3.获取review卡片
        review_cards = pdp_page.FE.eles(dweb_pdp_ele.ele_mod_review_card)
        for index, item in enumerate(review_cards):
            # 3.1 验证头像存在
            assert item.query_selector(u"//div[contains(@class,'ReviewCard_header')]//img")
            # 3.2 验证创建时间存在
            assert item.query_selector(u"//div[contains(@class,'ReviewCard_reviewCreateTime')]")
            # 3.3 获取review 卡片文案
            review_comment = item.query_selector(u"//span[contains(@class,'ReviewCard_comments')]").text_content()
            # 3.4 点击review卡片弹出pop
            item.click()
            p.wait_for_timeout(2000)
            # 3.5 断言review pop
            assert pdp_page.FE.ele(dweb_pdp_ele.ele_review_pop_review_list).is_visible()
            # 3.6 断言默认Relevance 排序
            assert pdp_page.FE.ele(u"//div[@data-testid='wid-sort-dropdown']//div").text_content() == "Relevance"
            # 3.7 获取review pop 里评论list
            review_pop_item_list = pdp_page.FE.eles(dweb_pdp_ele.ele_review_pop_item_list)
            for index2, item2 in enumerate(review_pop_item_list):
                # 3.8 获取第一个review_comment
                review_comment_item2 = item2.query_selector(
                    u"//div[contains(@class,'mb-2 tracking')]//span").text_content()
                # 3.9 断言pdp点击进入的那条晒单会置顶显示
                assert review_comment == review_comment_item2
                if index2 == 0:
                    break
            # 4.断言加购按钮存在
            assert pdp_page.FE.ele(u"//div[@data-type='popup']//div[@data-role='addButton']").is_visible()
            # 5.关闭pop
            p.get_by_test_id("btn-modal-close").click()
            if index == 3:
                break
