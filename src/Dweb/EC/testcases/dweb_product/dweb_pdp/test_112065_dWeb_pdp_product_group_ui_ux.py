import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证")
class TestDWebPDPProductGroupUIUX:
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证")
    def test_112065_dWeb_pdp_product_group_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【112065】 PC-PDP-商品组-只有一个属性模块UI/UX验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 1.直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422")
        initial_url = p.url
        p.wait_for_timeout(5000)
        # 2.滚动到指定位置
        scroll_one_page_until(p, dweb_pdp_ele.ele_product_group)
        # 3.断言product group 文案存在
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_product_group_title).is_visible()
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_product_group_title + u"/span").text_content()
        assert pdp_page.FE.ele(dweb_pdp_ele.ele_product_group_title + u"/strong").text_content()

        # 4.获取product group 组件商品list
        product_group_list = pdp_page.FE.eles(dweb_pdp_ele.ele_product_group_item_list)
        len1 = len(product_group_list)
        print(len1)
        for index, item in enumerate(product_group_list):
            # 4.1 验证product_group 商品信息

            ele_item = u"//div[contains(@data-testid,wid-pdp-variation-card-)]"
            # 4.2 验证product_group 商品图片
            assert item.query_selector(ele_item + u"//div[@data-component='CroppedImage']/img")
            # 4.3 验证product_group 商品title
            assert item.query_selector(
                ele_item + u"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_title')]").text_content()
            # 4.4 验证product_group 商品价格
            assert item.query_selector(
                ele_item + u"//div[contains(@class,'imageCard_right')]//div[contains(@class,'imageCard_price')]/div").text_content()
            # 4.5 点击product group 商品
            variation_card = "wid-pdp-variation-card-" + str(index)
            p.get_by_test_id(variation_card).click()
            p.wait_for_timeout(5000)
            # 4.6 断言页面url 会变化
            new_url = p.url

            # 4.7 断言页面 URL 是否发生了变化
            # assert new_url != initial_url, "The page URL did not change."
            # 4.8 滚动到指定位置
            scroll_one_page_until(p, dweb_pdp_ele.ele_product_group)
            if index==len1-2:
                break
