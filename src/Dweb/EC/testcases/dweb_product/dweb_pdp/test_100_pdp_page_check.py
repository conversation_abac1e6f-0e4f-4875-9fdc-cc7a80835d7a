import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage

from allure import step
from pytest_assume.plugin import assume


@allure.story("产品PDP页面校验")
class TestAtPDP:
    pytestmark = [pytest.mark.pdp, pytest.mark.smoke, pytest.mark.dweb_regression]
    pdp_url = [r"https://www.sayweee.com/en/product/Calbee-Takoyaki-Ball/18362?category=snack02&parent_category=snack"]
    pdp_fbw_url = [
        r"https://www.sayweee.com/en/product/Pork-floss-with-mochi-cake-2pc/2044455?category=freshbakery02&parent_category=freshbakery"
    ]

    @pytest.fixture(scope='class')
    def setup(self, page: dict, pc_autotest_header):
        pp = DWebPDPPage(page["page"], pc_autotest_header, page.get("context"))
        yield pp

    @allure.title("进入pdp页面检查元素并加购")
    @pytest.mark.parametrize("pdp_url", pdp_url)
    def test_check_pdp_and_add_to_cart(self, setup, pdp_url, login_trace):
        """
        training
        """
        setup.goto_pdp_and_check(pdp_url)

    @allure.title("进入fbw pdp页面检查元素并加购")
    @pytest.mark.parametrize("pdp_fbw_url", pdp_fbw_url)
    def test_check_fbw_pdp_and_add_to_cart(self, setup, pdp_fbw_url, login_trace):
        """
        100616 验证Global+ FBW 商品PDP UI-UX
        """
        setup.goto_fbw_pdp_and_check(pdp_fbw_url)
