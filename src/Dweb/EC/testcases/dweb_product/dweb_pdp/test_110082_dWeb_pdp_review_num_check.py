import re

import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_product import dweb_pdp_ele
from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_page_pdp.dweb_page_pdp import DWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【110082】 PDP-Review-晒单数量流程验证")
class TestDWebPDPReviewNumCheck:
    pytestmark = [pytest.mark.pcpdp,pytest.mark.dweb_todo, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【110082】 PDP-Review-晒单数量流程验证")
    def test_110082_dWeb_pdp_review_num_check(self, page: dict, pc_autotest_header, login_trace):
        """
        【112145】 PC-PDP-video list UI/UX验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 1.直接进入指定pdp页面
        pdp_page = DWebPDPPage(p, pc_autotest_header, browser_context=c,
                               page_url="/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460")
        # 2.滚动到指定位置
        scroll_one_page_until(p, dweb_pdp_ele.ele_mod_review)
        # 3.获取review 上的review 数量
        review_num = pdp_page.FE.ele(dweb_pdp_ele.ele_mod_review + u"//strong").text_content()
        match = re.search(r"\((\d+)\)", review_num)
        if match:
            number = match.group(1)
            # 3.1 pc端晒单数量大于等于6个显示see all，点击see all
            if int(number) >= 6:
                assert pdp_page.FE.ele(dweb_pdp_ele.ele_mod_review + u"//span[@aria-label='explore more Reviews']")
                # 3.2 点击 see more 按钮
                pdp_page.FE.ele(dweb_pdp_ele.ele_mod_review + u"//span[@aria-label='explore more Reviews']").click()
            else:
                assert not pdp_page.FE.ele(dweb_pdp_ele.ele_mod_review + u"//span[@aria-label='explore more Reviews']")
