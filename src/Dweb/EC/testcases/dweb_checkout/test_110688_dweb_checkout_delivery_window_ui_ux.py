# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  xiao miao
@Version        :  V1.0.0
------------------------------------
@File           :  test_110688_dweb_checkout_delivery_window_ui_ux.py
@Description    :
@CreateTime     :  2025/7/24 23:54
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/24 23:54
"""

import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.dweb_page_checkout.dweb_page_checkout import DWebCheckoutPage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("PC结算页-delivery window模块UI/UX验证")
class TestWebCheckoutDeliveryWindowUIUX:
    pytestmark = [pytest.mark.dweb_todo, pytest.mark.miaoxiao]

    @allure.title("PC结算页delivery window模块UI/UX验证")
    @pytest.mark.present
    def test_110688_dweb_checkout_delivery_window_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【110688】 PC结算页delivery window模块UI/UX验证

        测试步骤:
        1. 清空购物车
        2. 从分类页加购生鲜商品
        3. 进入购物车页面
        4. 点击结算按钮进入结算页
        5. 验证结算页标题，处理upsell弹窗
        6. 查看结算页是否有delivery window模块
        7. 没有delivery window就结束case
        8. 有delivery window开始验证模块
        9. 验证delivery window title
        10. 验证delivery window送达时间
        11. 验证delivery window文案
        12. 验证delivery window价格
        13. 验证delivery window选择功能
        14. 验证选择时间窗口后订单总金额变化
        """
        p: Page = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '94538')
        p.wait_for_timeout(2000)

        # 1. 清空购物车
        empty_cart(pc_autotest_header)

        # 2. 从分类页加购生鲜商品
        with allure.step("从分类页加购生鲜商品"):
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            local_filter_id = category_page.page.get_by_test_id(dweb_category_ele.ele_local_delivery_test_id)

            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=3,
            )
            p.wait_for_timeout(5000)
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 3-4. 进入购物车页面并点击结算按钮
        with allure.step("点击结算按钮进入结算页"):
            DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            checkout_button = p.get_by_test_id("wid-cart-summary-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")

        p.wait_for_timeout(2000)

        # 5. 验证结算页标题并处理upsell弹窗
        with allure.step("验证结算页标题并处理upsell弹窗"):
            checkout_page = DWebCheckoutPage(p, pc_autotest_header, browser_context=c,
                                             page_url="/order/checkout?cart_domain=grocery")

            # 检测upsell弹窗
            upsell_title = p.get_by_test_id("wid-upsell-title")
            if upsell_title.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结账按钮")
                continue_button = p.get_by_test_id("wid-upsell-continue-to-checkout")
                continue_button.click()
                p.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下一步")

        p.wait_for_timeout(2000)

        # 6. 向下滑动页面，查看是否有delivery window模块
        with allure.step("查看结算页是否有delivery window模块"):
            p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            p.wait_for_timeout(2000)

            delivery_window_items = p.get_by_test_id("wid-review-order-deliver-windows-item").all()

            # 7. 没有delivery window就结束case
            if not delivery_window_items:
                log.info("未找到delivery window模块，测试结束")
                return

            log.info(f"找到{len(delivery_window_items)}个delivery window项")

        # 8-13. 验证delivery window模块各个元素
        with allure.step("验证delivery window模块"):
            for i, window_item in enumerate(delivery_window_items):
                log.info(f"验证第{i + 1}个delivery window项")

                # 9. 验证title
                title_element = window_item.get_by_test_id("wid-review-order-deliver-windows-item-desc")
                assert title_element.is_visible(), f"第{i + 1}个delivery window的title不可见"
                title_text = title_element.text_content()
                log.info(f"delivery window title: {title_text}")

                # 10. 验证送达时间
                arrival_time = window_item.get_by_test_id("wid-review-order-deliver-windows-item-arrival-before")
                assert arrival_time.is_visible(), f"第{i + 1}个delivery window的送达时间不可见"
                arrival_text = arrival_time.text_content()
                log.info(f"delivery window送达时间: {arrival_text}")

                # 11. 验证文案
                content_text = window_item.get_by_test_id("wid-review-order-deliver-windows-item-content-text")
                assert content_text.is_visible(), f"第{i + 1}个delivery window的文案不可见"
                content = content_text.text_content()
                log.info(f"delivery window文案: {content}")

                # 12. 验证价格
                price_element = window_item.get_by_test_id("wid-review-order-deliver-windows-item-price")
                assert price_element.is_visible(), f"第{i + 1}个delivery window的价格不可见"
                price_text = price_element.text_content()
                log.info(f"delivery window价格: {price_text}")

                # 13. 验证选择功能
                checkbox = window_item.get_by_test_id("wid-review-order-deliver-windows-item-checkbox")
                assert checkbox.is_visible(), f"第{i + 1}个delivery window的选择框不可见"

                # 验证初始状态
                initial_selected = checkbox.get_attribute("data-selected")
                log.info(f"delivery window初始选择状态: {initial_selected}")

                # 向下滑动页面找到订单总金额元素
                p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                p.wait_for_timeout(2000)

                # 14. 验证选择时间窗口后订单总金额变化
                # 获取初始订单总金额
                order_amount_element = p.get_by_test_id("wid-checkout-purchase-panel-amount")
                if not order_amount_element.is_visible(timeout=5000):
                    # 如果元素不可见，继续向下滑动寻找
                    for scroll_attempt in range(3):
                        p.evaluate("window.scrollBy(0, 500)")
                        p.wait_for_timeout(1000)
                        if order_amount_element.is_visible(timeout=2000):
                            break
                    else:
                        log.error("无法找到订单总金额元素")
                        continue

                assert order_amount_element.is_visible(), "订单总金额元素不可见"
                initial_amount_text = order_amount_element.text_content().strip()
                initial_amount = float(initial_amount_text.replace('$', '').replace(',', ''))
                log.info(f"初始订单总金额: ${initial_amount}")

                # 点击选择时间窗口
                checkbox.click()
                p.wait_for_timeout(2000)

                # 验证选择后状态
                selected_state = checkbox.get_attribute("data-selected")
                log.info(f"delivery window选择后状态: {selected_state}")
                assert selected_state == "true", f"第{i + 1}个delivery window选择后状态不正确"

                # 验证选择后订单总金额发生变化
                selected_amount_text = order_amount_element.text_content().strip()
                selected_amount = float(selected_amount_text.replace('$', '').replace(',', ''))
                log.info(f"选择后订单总金额: ${selected_amount}")

                assert selected_amount != initial_amount, f"选择时间窗口后金额未发生变化，初始金额: ${initial_amount}，选择后金额: ${selected_amount}"
                log.info(f"验证选择时间窗口后金额发生变化: ${initial_amount} -> ${selected_amount}")

                # 取消选择时间窗口
                checkbox.click()
                p.wait_for_timeout(2000)

                # 验证取消选择后状态
                unselected_state = checkbox.get_attribute("data-selected")
                log.info(f"delivery window取消选择后状态: {unselected_state}")
                assert unselected_state == "false", f"第{i + 1}个delivery window取消选择后状态不正确"

                # 验证取消选择后订单总金额恢复
                final_amount_text = order_amount_element.text_content().strip()
                final_amount = float(final_amount_text.replace('$', '').replace(',', ''))
                log.info(f"取消选择后订单总金额: ${final_amount}")

                assert abs(
                    final_amount - initial_amount) < 0.01, f"取消选择时间窗口后金额不正确，期望${initial_amount}，实际${final_amount}"
                log.info(f"验证取消选择时间窗口后金额恢复正确: ${final_amount}")

        log.info("PC结算页delivery window模块UI/UX验证测试完成")
