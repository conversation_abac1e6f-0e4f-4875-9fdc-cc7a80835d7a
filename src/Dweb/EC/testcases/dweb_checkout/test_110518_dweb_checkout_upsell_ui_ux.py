# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  xiao miao
@Version        :  V1.0.0
------------------------------------
@File           :  test_110518_dweb_checkout_upsell_ui_ux.py
@Description    :
@CreateTime     :  2025/7/28 04:10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/28 04:10
"""

import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.dweb_page_checkout.dweb_page_checkout import DWebCheckoutPage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("PC结算页-upsell页面-商品数据模块UI/UX验证")
class TestDWebCheckoutUpsellUIUX:
    pytestmark = [pytest.mark.dweb_todo, pytest.mark.miaoxiao]

    @allure.title("PC结算页-upsell页面-商品数据模块UI/UX验证")
    @pytest.mark.present
    def test_110518_dweb_checkout_upsell_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【110518】 PC结算页-upsell页面-商品数据模块UI/UX验证

        测试步骤:
        1. 清空购物车
        2. 从分类页加购生鲜商品
        3. 进入购物车页面
        4. 点击结算按钮进入结算页
        5. 检测到有upsell标题就开始验证upsell页面，没有的话就结束case
        6. 验证upsell中数据模块，有则验证，无则跳过，并输出验证过哪个模块
        7. 验证存在数据的模块都有商品卡片
        8. 对于每个数据模块的第一个商品点击加购按钮
        9. 所有模块数据都验证完成后点击继续按钮回到结算页
        """
        p: Page = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '94538')
        p.wait_for_timeout(2000)

        # 1. 清空购物车
        empty_cart(pc_autotest_header)

        # 2. 从分类页加购生鲜商品
        with allure.step("从分类页加购生鲜商品"):
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            local_filter_id = category_page.page.get_by_test_id(dweb_category_ele.ele_local_delivery_test_id)

            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=3,
            )
            p.wait_for_timeout(5000)
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 3-4. 进入购物车页面并点击结算按钮
        with allure.step("点击结算按钮进入结算页"):
            DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            checkout_button = p.get_by_test_id("wid-cart-summary-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")

        p.wait_for_timeout(2000)

        # 5. 检测到有upsell标题就开始验证upsell页面，没有的话就结束case
        with allure.step("检测upsell页面"):
            upsell_title = p.get_by_test_id("wid-upsell-title")
            if not upsell_title.is_visible(timeout=5000):
                log.info("未检测到upsell页面，测试结束")
                return

            title_text = upsell_title.text_content()
            log.info(f"检测到upsell页面，标题: {title_text}")

        # 6. 验证upsell中数据模块
        with allure.step("验证upsell中数据模块"):
            # 定义所有可能的模块ID
            module_ids = [
                "wid-upsell-item-checkout_forgot_short_term-title",
                "wid-upsell-item-checkout_snack-title",
                "wid-upsell-item-checkout_beverages-title",
                "wid-upsell-item-checkout_recently_viewed-title",
                "wid-upsell-item-checkout_complete-title",
                "wid-upsell-item-checkout_fresh_bakery-title",
                "wid-upsell-item-checkout_fresh_deli-title",
                "wid-upsell-item-checkout_sale-title"
            ]

            # 存储找到的模块
            found_modules = []

            # 向下滚动页面以确保所有模块都被加载
            for _ in range(3):
                p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                p.wait_for_timeout(1000)

            # 回到顶部
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(1000)

            # 检查每个模块
            for module_index, module_id in enumerate(module_ids):
                module = p.get_by_test_id(module_id)
                if module.is_visible(timeout=1000):
                    module_text = module.text_content()
                    log.info(f"找到模块 {module_index + 1}: {module_id}, 内容: {module_text}")
                    found_modules.append(module_id)

                    # 7. 验证模块中的商品卡片
                    with allure.step(f"验证模块 {module_id} 中的商品卡片"):
                        # 滚动到该模块
                        module.scroll_into_view_if_needed()
                        p.wait_for_timeout(1000)

                        # 使用多种方式查找商品卡片
                        product_cards = []

                        # 方式1：直接在模块父容器中查找
                        # module_container = module.locator("..").first
                        # product_cards = module_container.get_by_test_id("wid-product-card-title").all()

                        # 方式2：在模块的祖父容器中查找（参考test2.py的方法）
                        if not product_cards:
                            try:
                                module_container = module.locator("xpath=ancestor::div[2]").first
                                product_cards = module_container.get_by_test_id("wid-product-card-container").all()
                                log.info(f"方式2找到 {len(product_cards)} 个商品卡片")
                            except:
                                pass

                        # 方式3：尝试不同的商品卡片test-id
                        # if not product_cards:
                        #     try:
                        #         module_container = module.locator("xpath=ancestor::div[2]").first
                        #         for card_testid in ["wid-product-card-image", "wid-product-card-title"]:
                        #             product_cards = module_container.get_by_test_id(card_testid).all()
                        #             if product_cards:
                        #                 log.info(f"方式3使用{card_testid}找到 {len(product_cards)} 个商品卡片")
                        #                 break
                        #     except:
                        #         pass

                        if product_cards:
                            log.info(f"模块 {module_id} 中找到 {len(product_cards)} 个商品卡片")
                            # 8. 对该模块的第一个商品点击加购
                            with allure.step(f"对模块 {module_id} 的第一个商品进行加购操作"):
                                try:
                                    # 方式1：直接在模块容器中查找所有加购按钮
                                    add_buttons = module_container.get_by_test_id("btn-atc-plus").all()

                                    # 方式2：如果方式1找不到，尝试在商品卡片的父容器中查找
                                    if not add_buttons:
                                        first_card_container = product_cards[0].locator("xpath=ancestor::div[3]").first
                                        add_buttons = first_card_container.get_by_test_id("btn-atc-plus").all()

                                    # 方式3：如果还是找不到，尝试更大范围查找
                                    if not add_buttons:
                                        # 滚动确保按钮可见
                                        product_cards[0].scroll_into_view_if_needed()
                                        p.wait_for_timeout(500)
                                        add_buttons = p.get_by_test_id("btn-atc-plus").all()
                                        # 只取前几个按钮，避免点击到其他模块的按钮
                                        if len(add_buttons) > 0:
                                            add_buttons = add_buttons[:3]

                                    if add_buttons:
                                        # 确保按钮可见且可点击
                                        for i, button in enumerate(add_buttons):
                                            if button.is_visible() and button.is_enabled():
                                                try:
                                                    button.scroll_into_view_if_needed()
                                                    p.wait_for_timeout(500)
                                                    button.click(timeout=3000)
                                                    p.wait_for_timeout(1000)
                                                    log.info(f"模块 {module_id} 第一个商品已点击加购按钮")
                                                    break
                                                except Exception as e:
                                                    log.warning(
                                                        f"模块 {module_id} 第{i + 1}个加购按钮点击失败: {str(e)}")
                                                    continue
                                        else:
                                            log.warning(f"模块 {module_id} 所有加购按钮都无法点击")
                                    else:
                                        log.warning(f"模块 {module_id} 未找到加购按钮")

                                except Exception as e:
                                    log.warning(f"模块 {module_id} 加购操作失败: {str(e)}")

                            # 8. 对该模块的第一个商品点击加购
                            # with allure.step(f"对模块 {module_id} 的第一个商品进行加购操作"):
                            #     # 获取商品卡片的父容器来查找加购按钮
                            #     first_card_container = product_cards[0].locator(
                            #         "xpath=ancestor::div[contains(@data-testid,'product-card') or contains(@class,'product')]").first
                            #     add_buttons = first_card_container.get_by_test_id("btn-atc-plus").all()
                            #
                            #     # 如果在卡片容器中找不到，尝试在更大范围查找
                            #     if not add_buttons:
                            #         add_buttons = module_container.get_by_test_id("btn-atc-plus").all()
                            #
                            #     if add_buttons:
                            #         try:
                            #             add_buttons[0].click()
                            #             p.wait_for_timeout(1000)
                            #             log.info(f"模块 {module_id} 第一个商品已点击加购按钮")
                            #         except Exception as e:
                            #             log.warning(f"模块 {module_id} 加购操作失败: {str(e)}")
                            #     else:
                            #         log.warning(f"模块 {module_id} 未找到加购按钮")
                        else:
                            log.warning(f"模块 {module_id} 中未找到商品卡片")

                #     # 7. 验证模块中的商品卡片
                #     with allure.step(f"验证模块 {module_id} 中的商品卡片"):
                #         # 滚动到该模块
                #         module.scroll_into_view_if_needed()
                #         p.wait_for_timeout(1000)
                #
                #         # 查找该模块下的商品卡片 - 需要在模块容器内查找
                #         module_container = module.locator("..").first  # 获取模块的父容器
                #         product_cards = module_container.get_by_test_id("wid-product-card-title").all()
                #
                #         if product_cards:
                #             log.info(f"模块 {module_id} 中找到 {len(product_cards)} 个商品卡片")
                #
                #             # 8. 对该模块的第一个商品点击加购
                #             with allure.step(f"对模块 {module_id} 的第一个商品进行加购操作"):
                #                 add_buttons = module_container.get_by_test_id("btn-atc-plus").all()
                #                 if add_buttons:
                #                     try:
                #                         add_buttons[0].click()
                #                         p.wait_for_timeout(1000)
                #                         log.info(f"模块 {module_id} 第一个商品已点击加购按钮")
                #                     except Exception as e:
                #                         log.warning(f"模块 {module_id} 加购操作失败: {str(e)}")
                #                 else:
                #                     log.warning(f"模块 {module_id} 未找到加购按钮")
                #         else:
                #             log.warning(f"模块 {module_id} 中未找到商品卡片")
                # else:
                #     log.info(f"未找到模块: {module_id}")

            # 输出验证过的模块总结
            log.info(f"总共找到 {len(found_modules)} 个模块: {', '.join(found_modules)}")

        # 9. 点击继续按钮回到结算页
        with allure.step("点击继续按钮回到结算页"):
            # 滚动到页面顶部以找到继续按钮
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(1000)

            continue_button = p.get_by_test_id("wid-upsell-continue-to-checkout")
            if continue_button.is_visible(timeout=3000):
                continue_button.click()
                p.wait_for_timeout(3000)
                log.info("已点击继续按钮")

                # 验证是否回到结算页
                assert "/order/checkout" in p.url, "点击继续按钮后未回到结算页"
                log.info("成功回到结算页")
            else:
                log.error("未找到继续按钮")
                assert False, "未找到继续按钮"

        log.info("PC结算页-upsell页面-商品数据模块UI/UX验证测试完成")