
import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.dweb_page_checkout.dweb_page_checkout import DWebCheckoutPage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.config.weee.log_help import log


@allure.story("PC结算页-单个生鲜购物车切换日期UI/UX验证")
class TestWebCheckoutSingleFreshCartChangeDateUIUX:
    pytestmark = [pytest.mark.dweb_todo, pytest.mark.huimin]

    @allure.title("PC结算单个生鲜购物车切换日期UI/UX验证")
    @pytest.mark.present
    def test_108559_dweb_checkout_single_fresh_cart_change_date_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108559】 PC结算单个生鲜购物车切换日期UI/UX验证

        测试步骤:
        1. 清空购物车
        2. 从分类页加购生鲜商品
        3. 进入购物车页面
        4. 点击结算按钮进入结算页
        5. 验证结算页标题
        6. 点击切换日期按钮
        7. 如果支持切换日期会拉起切换日期popup
        8. 验证切换日期 popup顶部标题有内容
        9. 验证切换日期 popup的切换日期列表是否有数据
        10. 点击任意一个可选择的日期
        11. 验证日期切换成功后弹窗关闭
        12. 处理可能的upsell弹窗
        13. 验证回到结算页面
        14. 验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期
        """
        p: Page = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '94538')
        p.wait_for_timeout(2000)
        # 2.清除购物车
        empty_cart(pc_autotest_header)

        # 2. 从分类页加购生鲜商品
        with (allure.step("从分类页加购生鲜商品")):
            # 构造的分类页面
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            # 去分类页加购Local类型的商品进购物车
            local_filter_id = category_page.page.get_by_test_id(dweb_category_ele.ele_local_delivery_test_id)

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=3,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 4. 点击结算按钮进入结算页
        with allure.step("点击结算按钮进入结算页"):
            # 构造的购物车页面
            DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            # 点击结算按钮
            checkout_button = p.get_by_test_id("wid-cart-summary-checkout")
            assert checkout_button.is_visible(), "结算按钮不可见"
            checkout_button.click()
            log.info("成功点击结算按钮")
        p.wait_for_timeout(2000)
        # 5. 验证结算页标题/找不到标题
        with allure.step("验证结算页标题"):
            # 构造的结算页页面
            checkout_page = DWebCheckoutPage(p, pc_autotest_header, browser_context=c,page_url="/order/checkout?cart_domain=grocery")
            checkout_page.verify_checkout_title()
            # 处理可能的upsell弹窗
            upsell_button = p.get_by_test_id("wid-upsell-continue-to-checkout-btn")
            if upsell_button.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结算按钮")
                upsell_button.click()
                p.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下一步")
        p.wait_for_timeout(2000)

        # 6. 点击切换日期按钮
        with allure.step("点击切换日期按钮"):
            change_date_btn = p.get_by_test_id("wid-checkout-review-order-card-change-date-btn")
            assert change_date_btn.is_visible(), "切换日期按钮不可见"
            change_date_btn.click()
            log.info("成功点击切换日期按钮")
            p.wait_for_timeout(2000)

        # 7. 验证切换日期popup弹出
        with allure.step("验证切换日期popup弹出"):
            change_date_popup = p.get_by_test_id("wid-dialog-change-delivery-date-content")
            assert change_date_popup.is_visible(timeout=5000), "切换日期弹窗未显示"
            log.info("切换日期弹窗显示成功")

        # 8. 验证切换日期popup顶部标题有内容
        with allure.step("验证切换日期popup顶部标题"):
            modal_title = p.get_by_test_id("wid-modal-title")
            assert modal_title.is_visible(), "弹窗标题不可见"
            title_text = modal_title.text_content()
            assert title_text and title_text.strip(), "弹窗标题内容为空"
            log.info(f"弹窗标题验证成功: {title_text}")

        # 9. 验证切换日期popup的切换日期列表是否有数据
        with allure.step("验证切换日期popup的切换日期列表是否有数据"):
            date_weeks_list = p.get_by_test_id("wid-dialog-change-delivery-date-weeks-list")
            assert date_weeks_list.is_visible(), "切换日期列表不可见"
            log.info("切换日期列表显示成功")

            # 验证列表内容不为空
            list_content = date_weeks_list.text_content()
            assert list_content and list_content.strip(), "切换日期列表内容为空"
            log.info(f"切换日期列表有数据，内容长度: {len(list_content)} 字符")

            # 可选：验证列表中是否包含日期相关的元素
            date_items_in_list = date_weeks_list.locator("[data-testid*='date']").all()
            if len(date_items_in_list) > 0:
                log.info(f"在日期列表中找到 {len(date_items_in_list)} 个日期相关元素")
            else:
                log.info("在日期列表中未找到具体的日期元素，但列表有内容")

        # 10. 点击任意一个可选择的日期
        with allure.step("点击任意一个可选择的日期"):
            # 获取所有可选择的日期选项
            date_items = p.get_by_test_id("wid-change-date-date-item").all()
            assert len(date_items) > 0, "未找到可选择的日期选项"
            log.info(f"找到 {len(date_items)} 个可选择的日期选项")

            # 选择第一个可见且可点击的日期选项
            selected_date_item = None
            for i, date_item in enumerate(date_items):
                if date_item.is_visible() and date_item.is_enabled():
                    selected_date_item = date_item
                    log.info(f"选择第 {i+1} 个日期选项")
                    break

            assert selected_date_item is not None, "未找到可点击的日期选项"

            # 记录选择的日期文本
            selected_date_text = selected_date_item.text_content()
            log.info(f"准备选择日期: {selected_date_text}")

            # 点击选择的日期
            selected_date_item.click()
            p.wait_for_timeout(2000)
            log.info("成功点击日期选项")

        # 11. 验证日期切换成功后弹窗关闭
        with allure.step("验证日期切换成功后弹窗关闭"):
            # 验证切换日期弹窗关闭
            assert not change_date_popup.is_visible(timeout=5000), "切换日期弹窗未关闭"
            log.info("切换日期弹窗已关闭")

        # 12. 处理可能的upsell弹窗
        with allure.step("处理可能的upsell弹窗"):
            try:
                upsell_button = p.get_by_test_id("wid-upsell-continue-to-checkout")
                if upsell_button.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗，点击继续结算按钮")
                    upsell_button.click()
                    p.wait_for_timeout(3000)
                    log.info("成功点击upsell弹窗的继续结算按钮")
                else:
                    log.info("未检测到upsell弹窗，直接进行下一步")
            except Exception as e:
                log.debug(f"处理upsell弹窗时出现异常或未检测到upsell弹窗: {str(e)}")
                log.info("未检测到upsell弹窗，直接进行下一步")

        # 13. 验证回到结算页面
        with allure.step("验证回到结算页面"):
            assert "/order/checkout" in p.url, "未回到结算页面"
            log.info("成功回到结算页面")

        # 14. 验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期
        with allure.step("验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期"):
            # 等待页面更新
            p.wait_for_timeout(3000)
            log.info("开始验证结算页面日期更新")

            # 向下滑动页面查找日期按钮
            updated_date_btn = None
            max_scroll_attempts = 5

            for scroll_attempt in range(max_scroll_attempts):
                log.info(f"第 {scroll_attempt + 1} 次尝试定位日期按钮")

                # 尝试定位日期按钮
                date_btn = p.get_by_test_id("wid-checkout-review-order-card-change-date-btn")
                if date_btn.is_visible():
                    updated_date_btn = date_btn
                    log.info(f"在第 {scroll_attempt + 1} 次尝试中找到日期按钮")
                    break

                # 如果没找到，向下滑动页面
                if scroll_attempt < max_scroll_attempts - 1:
                    log.info("日期按钮不可见，向下滑动页面")
                    p.evaluate("window.scrollBy(0, 300)")  # 向下滑动300像素
                    p.wait_for_timeout(1000)  # 等待滑动完成

            # 验证是否找到日期按钮
            if updated_date_btn and updated_date_btn.is_visible():
                # 滚动到日期按钮位置，确保完全可见
                updated_date_btn.scroll_into_view_if_needed()
                p.wait_for_timeout(1000)

                # 获取更新后的日期文本
                updated_date_text = updated_date_btn.text_content()
                assert updated_date_text and updated_date_text.strip(), "结算页面日期显示为空"
                log.info(f"结算页面显示的最新日期: {updated_date_text}")

                # 验证日期确实发生了变化（如果可能的话）
                if 'selected_date_text' in locals() and selected_date_text:
                    if updated_date_text != selected_date_text:
                        log.info(f"日期切换成功: 从 '{selected_date_text}' 更新为 '{updated_date_text}'")
                    else:
                        log.info("页面显示的日期与选择的日期一致")
                else:
                    log.info("日期更新验证完成，页面显示最新日期")

                log.info("结算页面日期验证成功")
            else:
                log.warning(f"经过 {max_scroll_attempts} 次滑动尝试，仍未找到日期按钮")
                # 记录当前页面信息用于调试
                current_url = p.url
                log.info(f"当前页面URL: {current_url}")

                # 查找页面上所有包含date的元素
                all_date_elements = p.locator("[data-testid*='date']").all()
                log.info(f"页面上找到 {len(all_date_elements)} 个包含'date'的元素")

                for i, element in enumerate(all_date_elements):
                    try:
                        testid = element.get_attribute("data-testid")
                        is_visible = element.is_visible()
                        log.info(f"元素 {i+1}: testid='{testid}', 可见={is_visible}")
                    except Exception as e:
                        log.debug(f"获取元素 {i+1} 信息失败: {str(e)}")

                log.info("日期切换操作已完成，但无法验证页面显示更新")

        log.info("PC结算单个生鲜购物车切换日期UI/UX验证测试完成")