{"name": "PC结算页delivery window模块UI/UX验证", "status": "passed", "description": "\n        【110688】 PC结算页delivery window模块UI/UX验证\n\n        测试步骤:\n        1. 清空购物车\n        2. 从分类页加购生鲜商品\n        3. 进入购物车页面\n        4. 点击结算按钮进入结算页\n        5. 验证结算页标题，处理upsell弹窗\n        6. 查看结算页是否有delivery window模块\n        7. 没有delivery window就结束case\n        8. 有delivery window开始验证模块\n        9. 验证delivery window title\n        10. 验证delivery window送达时间\n        11. 验证delivery window文案\n        12. 验证delivery window价格\n        13. 验证delivery window选择功能\n        14. 验证选择时间窗口后订单总金额变化\n        ", "steps": [{"name": "从分类页加购生鲜商品", "status": "passed", "start": 1754141029037, "stop": 1754141069395}, {"name": "点击结算按钮进入结算页", "status": "passed", "start": 1754141069395, "stop": 1754141073259}, {"name": "验证结算页标题并处理upsell弹窗", "status": "passed", "start": 1754141075316, "stop": 1754141086008}, {"name": "查看结算页是否有delivery window模块", "status": "passed", "start": 1754141088055, "stop": 1754141090157}, {"name": "验证delivery window模块", "status": "passed", "start": 1754141090157, "stop": 1754141104131}], "start": 1754141018770, "stop": 1754141104131, "uuid": "2b2191fa-9ae0-418f-a34d-fc7bcd5e5122", "historyId": "03dd404760da0a490870e4e7ade744e4", "testCaseId": "03dd404760da0a490870e4e7ade744e4", "fullName": "src.Dweb.EC.testcases.dweb_checkout.test_110688_dweb_checkout_delivery_window_ui_ux.TestWebCheckoutDeliveryWindowUIUX#test_110688_dweb_checkout_delivery_window_ui_ux", "labels": [{"name": "story", "value": "PC结算页-delivery window模块UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_checkout"}, {"name": "suite", "value": "test_110688_dweb_checkout_delivery_window_ui_ux"}, {"name": "subSuite", "value": "TestWebCheckoutDeliveryWindowUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "1232-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_checkout.test_110688_dweb_checkout_delivery_window_ui_ux"}]}