{"name": "PC-CMS页面导航组件UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: DOMException: Failed to execute 'querySelectorAll' on 'Document': '#8ADIWdYW-6_R83G6Xo9lH' is not a valid selector.\n    at query (<anonymous>:3329:41)\n    at <anonymous>:3339:7\n    at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\n    at SelectorEvaluatorImpl._queryCSS (<anonymous>:3326:17)\n    at SelectorEvaluatorImpl._querySimple (<anonymous>:3206:19)\n    at <anonymous>:3154:29\n    at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\n    at SelectorEvaluatorImpl.query (<anonymous>:3147:19)\n    at Object.query (<anonymous>:3361:44)\n    at <anonymous>:3319:21", "trace": "self = <test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX object at 0x000001D0B9161D50>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...qbOPIAykYqXA_6tqEKzxjjpAj7meI2U2ob-lT1p-IBDEckcOhdKTua_oq20L5Z6CAE7leqnc_PH1i2vYufQrhK7xlAtsjMEunujd84sKTcgMMHdI', ...}\nlogin_trace = None\n\n    @allure.title(\"PC-CMS页面导航组件UI/UX验证\")\n    @pytest.mark.present\n    def test_113133_dweb_cms_page_nav_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113133】 PC-CMS页面导航组件UI/UX验证\n    \n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入crazy8页面\n        with allure.step(\"进入crazy8页面\"):\n            home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n            p.goto(f\"{TEST_URL}/cms/page/activity/sale-Crazy8\")\n            p.wait_for_timeout(3000)\n            log.info(\"成功进入crazy8页面\")\n    \n        # 2. 向下滚动页面，查找导航组件\n        with allure.step(\"查找导航组件\"):\n            nav_component = None\n            max_scrolls = 10\n            scroll_count = 0\n    \n            while scroll_count < max_scrolls:\n                nav_component = p.get_by_test_id(\"mod-cm_page_nav\")\n                if nav_component.is_visible():\n                    nav_component.scroll_into_view_if_needed()\n                    p.wait_for_timeout(1000)\n                    log.info(\"找到导航组件\")\n                    break\n    \n                # 向下滚动\n                p.evaluate(\"window.scrollBy(0, window.innerHeight)\")\n                p.wait_for_timeout(1000)\n                scroll_count += 1\n    \n            # 如果没有找到导航组件就结束case\n            if not nav_component or not nav_component.is_visible():\n                log.info(\"未找到导航组件，测试结束\")\n                return\n    \n        # 3. 验证组件吸顶展示\n        with allure.step(\"验证导航组件吸顶展示\"):\n            # 继续向下滚动，验证组件是否吸顶\n            p.evaluate(\"window.scrollBy(0, 500)\")\n            p.wait_for_timeout(2000)\n    \n            # 检查组件是否仍然可见（吸顶状态）\n            assert nav_component.is_visible(), \"导航组件未保持吸顶状态\"\n            log.info(\"验证导航组件吸顶展示成功\")\n        # 4. 查找组件内的导航元素并验证点击功能\n        with allure.step(\"验证导航元素点击功能\"):\n            # 获取导航组件内的所有导航项 - 改为查找data-id\n            nav_items = nav_component.locator(\"[data-id]\").all()\n    \n            if not nav_items:\n                log.info(\"导航组件内未找到导航项\")\n                return\n    \n            log.info(f\"找到{len(nav_items)}个导航项\")\n    \n            for i, nav_item in enumerate(nav_items):\n                # 获取导航项的data-id\n                data_id = nav_item.get_attribute(\"data-id\")\n                if not data_id:\n                    log.warning(f\"第{i + 1}个导航项没有data-id属性\")\n                    continue\n    \n                # 获取导航项的文本内容\n                nav_text = nav_item.text_content()\n                log.info(f\"验证第{i + 1}个导航项，data-id: {data_id}, 内容: {nav_text}\")\n    \n                # 点击导航项\n                nav_item.click()\n                p.wait_for_timeout(2000)\n    \n                # 验证页面是否定位到对应的组件\n                # target_element = p.locator(f\"[data-testid='{data_id}']\")\n                target_element = p.locator(f\"#{data_id}\")\n>               if target_element.is_visible():\n\ntest_113133_dweb_cms_page_nav_ui_ux.py:113: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:17546: in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:499: in is_visible\n    return await self._frame.is_visible(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:348: in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x000001D0B9674290>\nmethod = 'isVisible'\nparams = {'selector': '#8ADIWdYW-6_R83G6Xo9lH', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: DOMException: Failed to execute 'querySelectorAll' on 'Document': '#8ADIWdYW-6_R83G6Xo9lH' is not a valid selector.\nE           at query (<anonymous>:3329:41)\nE           at <anonymous>:3339:7\nE           at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\nE           at SelectorEvaluatorImpl._queryCSS (<anonymous>:3326:17)\nE           at SelectorEvaluatorImpl._querySimple (<anonymous>:3206:19)\nE           at <anonymous>:3154:29\nE           at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\nE           at SelectorEvaluatorImpl.query (<anonymous>:3147:19)\nE           at Object.query (<anonymous>:3361:44)\nE           at <anonymous>:3319:21\n\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【113133】 PC-CMS页面导航组件UI/UX验证\n\n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        ", "steps": [{"name": "进入crazy8页面", "status": "passed", "start": 1754390021477, "stop": 1754390059893}, {"name": "查找导航组件", "status": "passed", "start": 1754390059893, "stop": 1754390061662}, {"name": "验证导航组件吸顶展示", "status": "passed", "start": 1754390061662, "stop": 1754390064001}, {"name": "验证导航元素点击功能", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: DOMException: Failed to execute 'querySelectorAll' on 'Document': '#8ADIWdYW-6_R83G6Xo9lH' is not a valid selector.\n    at query (<anonymous>:3329:41)\n    at <anonymous>:3339:7\n    at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\n    at SelectorEvaluatorImpl._queryCSS (<anonymous>:3326:17)\n    at SelectorEvaluatorImpl._querySimple (<anonymous>:3206:19)\n    at <anonymous>:3154:29\n    at SelectorEvaluatorImpl._cached (<anonymous>:3116:20)\n    at SelectorEvaluatorImpl.query (<anonymous>:3147:19)\n    at Object.query (<anonymous>:3361:44)\n    at <anonymous>:3319:21\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_cms\\test_113133_dweb_cms_page_nav_ui_ux.py\", line 113, in test_113133_dweb_cms_page_nav_ui_ux\n    if target_element.is_visible():\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 17546, in is_visible\n    self._sync(self._impl_obj.is_visible(timeout=timeout))\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 499, in is_visible\n    return await self._frame.is_visible(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 348, in is_visible\n    return await self._channel.send(\"isVisible\", locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1754390064001, "stop": 1754390066768}], "start": 1754390021477, "stop": 1754390066769, "uuid": "e8c14c94-75ed-433e-909c-f15f1abd1725", "historyId": "5347b64b33a9fccafd8c9b24789bd326", "testCaseId": "5347b64b33a9fccafd8c9b24789bd326", "fullName": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX#test_113133_dweb_cms_page_nav_ui_ux", "labels": [{"name": "story", "value": "PC-CMS页面导航组件UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cms"}, {"name": "suite", "value": "test_113133_dweb_cms_page_nav_ui_ux"}, {"name": "subSuite", "value": "TestDWebCMSPageNavUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "12996-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux"}]}