{"name": "PC-CMS页面导航组件UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Protocol error (Page.navigate): Cannot navigate to invalid URL", "trace": "self = <test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX object at 0x0000026204755DD0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...KKiUEQNwK4wholkHOJ0pU3vK8-epXK2eqqUar0ebhFoWut9DxXDHOxHdiUif8bY9Dcd2IuXBOk72h1PHOnznsFPKXBcCqQrggNbpMKaZRCVyX8y0', ...}\nlogin_trace = None\n\n    @allure.title(\"PC-CMS页面导航组件UI/UX验证\")\n    @pytest.mark.present\n    def test_113133_dweb_cms_page_nav_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113133】 PC-CMS页面导航组件UI/UX验证\n    \n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入crazy8页面\n        with allure.step(\"进入crazy8页面\"):\n            home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n>           p.goto(\"/cms/page/activity/sale-Crazy8\")\n\ntest_113133_dweb_cms_page_nav_ui_ux.py:46: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:9312: in goto\n    self._sync(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py:475: in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:139: in goto\n    await self._channel.send(\"goto\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x0000026204C943D0>\nmethod = 'goto', params = {'url': '/cms/page/activity/sale-Crazy8'}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.Error: Protocol error (Page.navigate): Cannot navigate to invalid URL\n\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: Error"}, "description": "\n        【113133】 PC-CMS页面导航组件UI/UX验证\n\n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        ", "steps": [{"name": "进入crazy8页面", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.Error: Protocol error (Page.navigate): Cannot navigate to invalid URL\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_cms\\test_113133_dweb_cms_page_nav_ui_ux.py\", line 46, in test_113133_dweb_cms_page_nav_ui_ux\n    p.goto(\"/cms/page/activity/sale-Crazy8\")\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 9312, in goto\n    self._sync(\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_page.py\", line 475, in goto\n    return await self._main_frame.goto(**locals_to_params(locals()))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 139, in goto\n    await self._channel.send(\"goto\", locals_to_params(locals()))\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1754388593037, "stop": 1754388626026}], "start": 1754388593037, "stop": 1754388626027, "uuid": "25ad21a3-d2ec-434d-816f-4a5a981153ef", "historyId": "5347b64b33a9fccafd8c9b24789bd326", "testCaseId": "5347b64b33a9fccafd8c9b24789bd326", "fullName": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX#test_113133_dweb_cms_page_nav_ui_ux", "labels": [{"name": "story", "value": "PC-CMS页面导航组件UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cms"}, {"name": "suite", "value": "test_113133_dweb_cms_page_nav_ui_ux"}, {"name": "subSuite", "value": "TestDWebCMSPageNavUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "3956-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux"}]}