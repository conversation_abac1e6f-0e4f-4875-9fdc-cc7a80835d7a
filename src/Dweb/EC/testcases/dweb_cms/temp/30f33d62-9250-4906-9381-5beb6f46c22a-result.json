{"name": "PC-CMS页面导航组件UI/UX验证", "status": "broken", "statusDetails": {"message": "TypeError: DWebHomePage.__init__() got an unexpected keyword argument 'page_url'", "trace": "self = <test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX object at 0x000001FB9EE32190>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='about:blank'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...-ZV8DLnYGLO5paLfEA3FFHBDuURQ2c1WZ66sVfX0lREj-x96OcIcygmNz420JKCaC3Us36xLDK3E_XauhzUG3yYaTpfdNetjwmn9Zm-1G9RI6qsM', ...}\nlogin_trace = None\n\n    @allure.title(\"PC-CMS页面导航组件UI/UX验证\")\n    @pytest.mark.present\n    def test_113133_dweb_cms_page_nav_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113133】 PC-CMS页面导航组件UI/UX验证\n    \n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入crazy8页面\n        with allure.step(\"进入crazy8页面\"):\n>           home_page = DWebHomePage(p, pc_autotest_header, browser_context=c,\n                                   page_url=\"/cms/page/activity/sale-Crazy8\")\nE           TypeError: DWebHomePage.__init__() got an unexpected keyword argument 'page_url'\n\ntest_113133_dweb_cms_page_nav_ui_ux.py:45: TypeError"}, "description": "\n        【113133】 PC-CMS页面导航组件UI/UX验证\n\n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        ", "steps": [{"name": "进入crazy8页面", "status": "broken", "statusDetails": {"message": "TypeError: DWebHomePage.__init__() got an unexpected keyword argument 'page_url'\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_cms\\test_113133_dweb_cms_page_nav_ui_ux.py\", line 45, in test_113133_dweb_cms_page_nav_ui_ux\n    home_page = DWebHomePage(p, pc_autotest_header, browser_context=c,\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1754388245058, "stop": 1754388245058}], "start": 1754388245058, "stop": 1754388245059, "uuid": "0789a514-4ae5-4bd0-bf50-49fb78578664", "historyId": "5347b64b33a9fccafd8c9b24789bd326", "testCaseId": "5347b64b33a9fccafd8c9b24789bd326", "fullName": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX#test_113133_dweb_cms_page_nav_ui_ux", "labels": [{"name": "story", "value": "PC-CMS页面导航组件UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cms"}, {"name": "suite", "value": "test_113133_dweb_cms_page_nav_ui_ux"}, {"name": "subSuite", "value": "TestDWebCMSPageNavUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "19628-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux"}]}