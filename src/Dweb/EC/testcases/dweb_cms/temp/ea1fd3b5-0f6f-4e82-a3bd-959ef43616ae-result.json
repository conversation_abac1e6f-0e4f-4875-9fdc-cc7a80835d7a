{"name": "PC-CMS页面导航组件UI/UX验证", "status": "broken", "statusDetails": {"message": "TypeError: 'NoneType' object is not subscriptable", "trace": "self = <test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX object at 0x00000239FBC46550>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en/cms/page/activity/sale-Crazy8'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...jvOL8lzU47_Yos9m4jt7liR6LELvfaHnzv11e8hL4E6nP3J_-RZeeupJbToc12l6rO5KhG5QYEtce0DTRstvnt36I_w3J7f9iQA9ImQ6HCSVSeuQ', ...}\nlogin_trace = None\n\n    @allure.title(\"PC-CMS页面导航组件UI/UX验证\")\n    @pytest.mark.present\n    def test_113133_dweb_cms_page_nav_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【113133】 PC-CMS页面导航组件UI/UX验证\n    \n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n    \n        # 1. 进入crazy8页面\n        with allure.step(\"进入crazy8页面\"):\n            home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n            p.goto(f\"{TEST_URL}/cms/page/activity/sale-Crazy8\")\n            p.wait_for_timeout(3000)\n            log.info(\"成功进入crazy8页面\")\n    \n        # 2. 向下滚动页面，查找导航组件\n        with allure.step(\"查找导航组件\"):\n            nav_component = None\n            max_scrolls = 10\n            scroll_count = 0\n    \n            while scroll_count < max_scrolls:\n                nav_component = p.get_by_test_id(\"mod-cm_page_nav\")\n                if nav_component.is_visible():\n                    nav_component.scroll_into_view_if_needed()\n                    p.wait_for_timeout(1000)\n                    log.info(\"找到导航组件\")\n                    break\n    \n                # 向下滚动\n                p.evaluate(\"window.scrollBy(0, window.innerHeight)\")\n                p.wait_for_timeout(1000)\n                scroll_count += 1\n    \n            # 如果没有找到导航组件就结束case\n            if not nav_component or not nav_component.is_visible():\n                log.info(\"未找到导航组件，测试结束\")\n                return\n    \n        # 3. 验证组件吸顶展示\n        with allure.step(\"验证导航组件吸顶展示\"):\n            # 继续向下滚动，验证组件是否吸顶\n            p.evaluate(\"window.scrollBy(0, 500)\")\n            p.wait_for_timeout(2000)\n    \n            # 检查组件是否仍然可见（吸顶状态）\n            assert nav_component.is_visible(), \"导航组件未保持吸顶状态\"\n            log.info(\"验证导航组件吸顶展示成功\")\n    \n        # 4. 查找组件内的导航元素并验证点击功能\n        with allure.step(\"验证导航元素点击功能\"):\n            # 获取导航组件内的所有导航项 - 查找data-nav-id\n            nav_items = nav_component.locator(\"[data-nav-id]\").all()\n    \n            if not nav_items:\n                log.info(\"导航组件内未找到导航项\")\n                return\n    \n            log.info(f\"找到{len(nav_items)}个导航项\")\n    \n            for i, nav_item in enumerate(nav_items):\n                # 获取导航项的data-nav-id\n                data_nav_id = nav_item.get_attribute(\"data-nav-id\")\n                if not data_nav_id:\n                    log.warning(f\"第{i + 1}个导航项没有data-nav-id属性\")\n                    continue\n    \n                # 获取导航项的文本内容\n                nav_text = nav_item.text_content()\n                log.info(f\"验证第{i + 1}个导航项，data-nav-id: {data_nav_id}, 内容: {nav_text}\")\n    \n                # 点击导航项\n                nav_item.click()\n                p.wait_for_timeout(2000)\n    \n                # 验证页面是否定位到对应的组件\n                # 尝试多种方式查找目标元素\n                target_element = None\n    \n                # 方式1: 尝试使用id属性（如果data-nav-id对应页面元素的id）\n                try:\n                    if not data_nav_id.startswith(('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')):\n                        # 只有当id不以数字开头时才使用#选择器\n                        target_element = p.locator(f\"#{data_nav_id}\")\n                        if not target_element.is_visible():\n                            target_element = None\n                except:\n                    target_element = None\n    \n                # 方式2: 使用data-testid属性查找\n                if not target_element:\n                    try:\n                        target_element = p.locator(f\"[data-testid='{data_nav_id}']\")\n                        if not target_element.is_visible():\n                            target_element = None\n                    except:\n                        target_element = None\n    \n                # 方式3: 使用id属性选择器（适用于以数字开头的id）\n                if not target_element:\n                    try:\n                        target_element = p.locator(f\"[id='{data_nav_id}']\")\n                        if not target_element.is_visible():\n                            target_element = None\n                    except:\n                        target_element = None\n    \n                if target_element and target_element.is_visible():\n                    # 检查目标元素是否在视口内\n                    target_rect = target_element.bounding_box()\n>                   viewport_height = p.viewport_size[\"height\"]\nE                   TypeError: 'NoneType' object is not subscriptable\n\ntest_113133_dweb_cms_page_nav_ui_ux.py:146: TypeError"}, "description": "\n        【113133】 PC-CMS页面导航组件UI/UX验证\n\n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        ", "steps": [{"name": "进入crazy8页面", "status": "passed", "start": 1754620502370, "stop": 1754620542924}, {"name": "查找导航组件", "status": "passed", "start": 1754620542924, "stop": 1754620544720}, {"name": "验证导航组件吸顶展示", "status": "passed", "start": 1754620544720, "stop": 1754620546917}, {"name": "验证导航元素点击功能", "status": "broken", "statusDetails": {"message": "TypeError: 'NoneType' object is not subscriptable\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_cms\\test_113133_dweb_cms_page_nav_ui_ux.py\", line 146, in test_113133_dweb_cms_page_nav_ui_ux\n    viewport_height = p.viewport_size[\"height\"]\n                      ~~~~~~~~~~~~~~~^^^^^^^^^^\n"}, "start": 1754620546917, "stop": 1754620549691}], "start": 1754620502370, "stop": 1754620549692, "uuid": "475bc823-eeb8-4d91-9ead-ef101abdba92", "historyId": "5347b64b33a9fccafd8c9b24789bd326", "testCaseId": "5347b64b33a9fccafd8c9b24789bd326", "fullName": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX#test_113133_dweb_cms_page_nav_ui_ux", "labels": [{"name": "story", "value": "PC-CMS页面导航组件UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cms"}, {"name": "suite", "value": "test_113133_dweb_cms_page_nav_ui_ux"}, {"name": "subSuite", "value": "TestDWebCMSPageNavUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "14704-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux"}]}