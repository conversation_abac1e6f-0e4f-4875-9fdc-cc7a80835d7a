{"name": "PC-CMS页面导航组件UI/UX验证", "status": "passed", "description": "\n        【113133】 PC-CMS页面导航组件UI/UX验证\n\n        测试步骤:\n        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8\n        2. 向下滚动页面，查找是否有导航组件 data-testid=\"mod-cm_page_nav\"，如果没有就结束case\n        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示\n        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致\n        ", "steps": [{"name": "进入crazy8页面", "status": "passed", "start": 1754632009752, "stop": 1754632048324}, {"name": "查找导航组件", "status": "passed", "start": 1754632048324, "stop": 1754632049867}, {"name": "验证导航组件吸顶展示", "status": "passed", "start": 1754632049867, "stop": 1754632052091}, {"name": "验证导航元素点击功能", "status": "passed", "start": 1754632052091, "stop": 1754632065988}], "start": 1754632009752, "stop": 1754632065988, "uuid": "1e13faa4-3260-46a2-bc79-5520cf08b789", "historyId": "5347b64b33a9fccafd8c9b24789bd326", "testCaseId": "5347b64b33a9fccafd8c9b24789bd326", "fullName": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux.TestDWebCMSPageNavUIUX#test_113133_dweb_cms_page_nav_ui_ux", "labels": [{"name": "story", "value": "PC-CMS页面导航组件UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_cms"}, {"name": "suite", "value": "test_113133_dweb_cms_page_nav_ui_ux"}, {"name": "subSuite", "value": "TestDWebCMSPageNavUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "3476-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_cms.test_113133_dweb_cms_page_nav_ui_ux"}]}