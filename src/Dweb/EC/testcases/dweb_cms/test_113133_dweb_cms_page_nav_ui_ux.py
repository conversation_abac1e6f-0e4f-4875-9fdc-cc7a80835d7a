# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_113133_dweb_cms_page_nav_ui_ux.py
@Description    :
@CreateTime     :  2025/1/27 10:00
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/1/27 10:00
"""

import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


@allure.story("PC-CMS页面导航组件UI/UX验证")
class TestDWebCMSPageNavUIUX:
    pytestmark = [pytest.mark.dweb_todo, pytest.mark.miaoxiao]

    @allure.title("PC-CMS页面导航组件UI/UX验证")
    @pytest.mark.present
    def test_113133_dweb_cms_page_nav_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【113133】 PC-CMS页面导航组件UI/UX验证

        测试步骤:
        1. 进入crazy8页面 /cms/page/activity/sale-Crazy8
        2. 向下滚动页面，查找是否有导航组件 data-testid="mod-cm_page_nav"，如果没有就结束case
        3. 有这个导航组件就开始以下验证，页面向下滑动，该组件吸顶展示
        4. 查找该组件里面有多少元素，id，依次点击元素，验证点击元素时页面定位到的组件id与点击的一致
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入crazy8页面
        with allure.step("进入crazy8页面"):
            home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)
            p.goto(f"{TEST_URL}/cms/page/activity/sale-Crazy8")
            p.wait_for_timeout(3000)
            log.info("成功进入crazy8页面")

        # 2. 向下滚动页面，查找导航组件
        with allure.step("查找导航组件"):
            nav_component = None
            max_scrolls = 10
            scroll_count = 0

            while scroll_count < max_scrolls:
                nav_component = p.get_by_test_id("mod-cm_page_nav")
                if nav_component.is_visible():
                    nav_component.scroll_into_view_if_needed()
                    p.wait_for_timeout(1000)
                    log.info("找到导航组件")
                    break

                # 向下滚动
                p.evaluate("window.scrollBy(0, window.innerHeight)")
                p.wait_for_timeout(1000)
                scroll_count += 1

            # 如果没有找到导航组件就结束case
            if not nav_component or not nav_component.is_visible():
                log.info("未找到导航组件，测试结束")
                return

        # 3. 验证组件吸顶展示
        with allure.step("验证导航组件吸顶展示"):
            # 继续向下滚动，验证组件是否吸顶
            p.evaluate("window.scrollBy(0, 500)")
            p.wait_for_timeout(2000)
            
            # 检查组件是否仍然可见（吸顶状态）
            assert nav_component.is_visible(), "导航组件未保持吸顶状态"
            log.info("验证导航组件吸顶展示成功")

        # 4. 查找组件内的导航元素并验证点击功能
        with allure.step("验证导航元素点击功能"):
            # 获取导航组件内的所有导航项 - 改为查找data-id
            nav_items = nav_component.locator("[data-id]").all()

            if not nav_items:
                log.info("导航组件内未找到导航项")
                return

            log.info(f"找到{len(nav_items)}个导航项")

            for i, nav_item in enumerate(nav_items):
                # 获取导航项的data-id
                data_id = nav_item.get_attribute("data-id")
                if not data_id:
                    log.warning(f"第{i + 1}个导航项没有data-id属性")
                    continue

                # 获取导航项的文本内容
                nav_text = nav_item.text_content()
                log.info(f"验证第{i + 1}个导航项，data-id: {data_id}, 内容: {nav_text}")

                # 点击导航项
                nav_item.click()
                p.wait_for_timeout(2000)

                # 验证页面是否定位到对应的组件
                # target_element = p.locator(f"[data-testid='{data_id}']")
                target_element = p.locator(f"#{data_id}")
                if target_element.is_visible():
                    # 检查目标元素是否在视口内
                    target_rect = target_element.bounding_box()
                    viewport_height = p.viewport_size["height"]

                    if target_rect and target_rect["y"] >= 0 and target_rect["y"] <= viewport_height:
                        log.info(f"成功定位到目标组件: {data_id}")
                    else:
                        log.warning(f"目标组件{data_id}不在当前视口内")
                else:
                    log.warning(f"未找到对应的目标组件: {data_id}")



        log.info("PC-CMS页面导航组件UI/UX验证测试完成")