import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class TestActivitySalePage:
    def __init__(self):
        self.driver = webdriver.Chrome()  # 假设使用Chrome浏览器
        self.wait = WebDriverWait(self.driver, 10)

    def test_navigation_component(self):
        # 1. 进入指定页面
        self.driver.get("https://example.com/cms/page/activity/sale-Crazy8")

        # 2. 向下滚动页面查找导航组件
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        try:
            nav_component = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="mod-cm_page_nav"]')))
        except TimeoutException:
            print("未找到导航组件，测试结束")
            return

        # 3. 验证导航组件吸顶展示
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(1)  # 等待滚动完成
        self.driver.execute_script("window.scrollTo(0, 500);")

        try:
            self.wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '[data-testid="mod-cm_page_nav"]')))
            print("导航组件成功吸顶展示")
        except TimeoutException:
            print("导航组件未吸顶展示")

        # 4. 查找导航项并验证点击
        scroll_container = self.driver.find_element(By.ID, "scroll-container")
        nav_items = scroll_container.find_elements(By.CSS_SELECTOR, "[data-nav-id]")

        print(f"找到 {len(nav_items)} 个导航项")

        for item in nav_items:
            nav_id = item.get_attribute("data-nav-id")
            item.click()
            time.sleep(1)  # 等待页面滚动

            try:
                target_element = self.driver.find_element(By.ID, nav_id)
                if self.is_element_in_viewport(target_element):
                    print(f"成功定位到导航项 {nav_id}")
                else:
                    print(f"导航项 {nav_id} 未正确定位")
            except NoSuchElementException:
                print(f"未找到与导航项 {nav_id} 对应的元素")

    def is_element_in_viewport(self, element):
        """检查元素是否在视口中"""
        return self.driver.execute_script("""
            var rect = arguments[0].getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        """, element)

    def teardown(self):
        self.driver.quit()


if __name__ == "__main__":
    test = TestActivitySalePage()
    try:
        test.test_navigation_component()
    finally:
        test.teardown()
