import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108218】 订单列表tab切换功能验证")
class TestDWebMyAllOrderTabUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108218】 订单列表tab切换功能验证")
    def test_108218_dWeb_my_all_order_tab_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108218】 订单列表tab切换功能验证
        测试步骤：
        1. 直接访问/account/my_orders页面
        2. 默认选中 all tab data-testid="wid-order-list-tab-all"，data-status="selected"，其他tab是 data-status="unselected"
        3. 切换pending tab，data-testid="wid-order-list-tab-1" ，切换成功之后 data-status="selected"
        4. 以此内推，切换 待发货、已发货、待晒单、已取消tab
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 直接访问/account/my_orders页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 2. 验证默认选中all tab，其他tab未选中
        all_tab = p.get_by_test_id(dweb_order_list_ele.order_all_tab_ele)
        assert all_tab.is_visible(), "all tab不可见"
        assert all_tab.get_attribute("data-status") == "selected", "all tab默认未选中"
        log.info("验证all tab默认选中状态成功")

        # 验证其他tab默认未选中
        tabs = [
            (dweb_order_list_ele.order_pending_tab_ele, "pending"),
            (dweb_order_list_ele.order_unshipped_tab_ele, "unshipped"),
            (dweb_order_list_ele.order_shipped_tab_ele, "shipped"),
            (dweb_order_list_ele.order_review_tab_ele, "review"),
            (dweb_order_list_ele.order_cancelled_tab_ele, "cancelled")
        ]

        for tab_id, tab_name in tabs:
            tab = p.get_by_test_id(tab_id)
            assert tab.is_visible(), f"{tab_name} tab不可见"
            assert tab.get_attribute("data-status") == "unselected", f"{tab_name} tab默认应该未选中"

        log.info("验证其他tab默认未选中状态成功")

        # 3. 切换pending tab
        pending_tab = p.get_by_test_id(dweb_order_list_ele.order_pending_tab_ele)
        pending_tab.click()
        p.wait_for_timeout(2000)
        
        assert pending_tab.get_attribute("data-status") == "selected", "pending tab切换后未选中"
        assert "filter_status=1" in p.url, "未切换到pending tab"
        assert all_tab.get_attribute("data-status") == "unselected", "all tab应该变为未选中"
        log.info("验证pending tab切换成功")

        # 4. 切换待发货tab
        unshipped_tab = p.get_by_test_id(dweb_order_list_ele.order_unshipped_tab_ele)
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        
        assert unshipped_tab.get_attribute("data-status") == "selected", "unshipped tab切换后未选中"
        assert "filter_status=2" in p.url, "未切换到unshipped tab"
        assert pending_tab.get_attribute("data-status") == "unselected", "pending tab应该变为未选中"
        log.info("验证unshipped tab切换成功")

        # 5. 切换已发货tab
        shipped_tab = p.get_by_test_id(dweb_order_list_ele.order_shipped_tab_ele)
        shipped_tab.click()
        p.wait_for_timeout(2000)
        
        assert shipped_tab.get_attribute("data-status") == "selected", "shipped tab切换后未选中"
        assert "filter_status=3" in p.url, "未切换到shipped tab"
        assert unshipped_tab.get_attribute("data-status") == "unselected", "unshipped tab应该变为未选中"
        log.info("验证shipped tab切换成功")

        # 6. 切换待晒单tab
        review_tab = p.get_by_test_id(dweb_order_list_ele.order_review_tab_ele)
        review_tab.click()
        p.wait_for_timeout(2000)
        
        assert review_tab.get_attribute("data-status") == "selected", "review tab切换后未选中"
        assert "filter_status=6" in p.url, "未切换到review tab"
        assert shipped_tab.get_attribute("data-status") == "unselected", "shipped tab应该变为未选中"
        log.info("验证review tab切换成功")

        # 7. 切换已取消tab
        cancelled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        
        assert cancelled_tab.get_attribute("data-status") == "selected", "cancelled tab切换后未选中"
        assert "filter_status=4" in p.url, "未切换到cancelled tab"
        assert review_tab.get_attribute("data-status") == "unselected", "review tab应该变为未选中"
        log.info("验证cancelled tab切换成功")

        # 8. 最后切换回all tab验证
        all_tab.click()
        p.wait_for_timeout(2000)
        
        assert all_tab.get_attribute("data-status") == "selected", "all tab切换后未选中"
        assert "filter_status" not in p.url or "filter_status=all" in p.url, "未切换回all tab"
        assert cancelled_tab.get_attribute("data-status") == "unselected", "cancelled tab应该变为未选中"
        log.info("验证all tab切换成功")

        log.info("PC版订单tab切换功能验证完成")