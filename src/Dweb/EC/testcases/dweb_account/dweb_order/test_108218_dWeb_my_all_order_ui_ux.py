import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_buy_again_ele, dweb_order_list_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_buy_again import DWebBuyAgainPage
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108218】 订单列表All tab-列表流程验证")
class TestDWebMyAllOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108218】 订单列表All tab-列表流程验证")
    def test_108218_dWeb_my_all_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108218】 订单列表All tab-列表流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到全部tab下
        2、如果存在订单，订单上会存在各种按钮
        3、点击 Return/ Refund 按钮，进入售后退款页面，再返回已送达页面
        6、点击reorder 按钮 ，右侧拉出再来一单选择商品页面，关闭返回已送达页面
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到全部订单tab
        p.get_by_test_id(dweb_order_list_ele.order_all_tab_ele).click()
        p.wait_for_timeout(2000)
        # 验证已切换到全部订单tab
        assert "filter_status=all" in p.url, "未切换到全部订单tab"

        # 验证已切换到全部订单 tab
        # assert "/order/list?filter_status=all" in p.url, "未切换到全部订单tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(dweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 全部订单tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(dweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(dweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("btn-main-banner-img-0").is_visible(), "未成功跳转到首页"
        else:
            # 获取全部订单tab下的normal订单
            order_R_items = p.get_by_test_id(dweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(dweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(dweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(dweb_order_list_ele.order_list_G_card_ele).all()
            order_Pre_items = p.get_by_test_id(dweb_order_list_ele.order_list_Pre_card_ele).all()
            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("全部订单tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("全部订单tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_tab_info(item_R, "all")
                    assert order_page.assert_order_card_info(item_R,"R"), f"第{index + 1}个生鲜订单卡片信息验证失败"
                    assert order_page.order_card_btn(item_R), f"第{index + 1}个生鲜订单卡片按钮验证失败"
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_S,"S"), f"第{index+1}个seller订单卡片信息验证失败"
                    assert order_page.order_card_btn(item_S), f"第{index + 1}个seller订单卡片按钮验证失败"
                    break
            elif len(order_P_items) > 0:
                for index, item_P in enumerate(order_P_items):
                    # 验证前10 个积分订单内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_P,"P"), f"第{index+1}个积分订单卡片信息验证失败"
                    break
            elif len(order_G_items) > 0:
                for index, item_G in enumerate(order_G_items):
                    # 验证前10 个订单礼品卡内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_G, "G"), f"第{index + 1}个里礼品卡订单卡片信息验证失败"
                    break
            elif len(order_Pre_items) > 0:
                for index, item_Pre in enumerate(order_Pre_items):
                    # 验证前10 个预售订单内容信息
                    # 使用公共断言方法验证seller订单卡片信息
                    assert order_page.assert_order_card_info(item_Pre, "Pre"), f"第{index + 1}个预售订单卡片信息验证失败"
                    break
            order_R_items[0].click()
            p.wait_for_timeout(2000)
            if order_R_items[0].get_by_test_id(dweb_order_list_ele.order_list_card_status_ele).text_content()!="Pending":
                # 断言页面进入订单详情页面
                assert "order/detail/" in p.url, "没有进入订单详情页面"
            else:
                assert "/order/dealpay/v2/" in p.url, "没有进入deal pay页面"
                # 点击卡片进入pdp
            p.go_back()














        #
        # if p.locator(dweb_order_list_ele.order_list_card_ele).is_visible():
        #     log.info("全部订单tab下有订单")
        # else:
        #     log.info("全部订单tab下没有订单")
        #     pytest.skip("全部订单tab下没有订单，跳过测试")
        # # 2. 检查存在全部订单订单
        # order_list_items = p.locator(dweb_order_list_ele.order_list_card_ele).all()
        # assert len(order_list_items) > 0,"全部订单tab下没有订单，无法继续测试"
        # for index, item in enumerate(order_list_items):
        #     # 验证前10 个订单内容信息
        #     assert item.locator(dweb_order_list_ele.order_list_card_status_ele).is_visible(), "订单内容信息不存在"
        #     assert item.locator(dweb_order_list_ele.order_list_card_detail_ele).is_visible(), "订单内容信息不存在"
        #     # seller 订单
        #     if item.locator(dweb_order_list_ele.order_list_card_title_ele).is_visible():
        #         assert item.locator(dweb_order_list_ele.order_list_card_title_icon_ele), "seller订单icon信息不存在"
        #
        #     assert item.locator(dweb_order_list_ele.order_list_card_items_ele).is_visible(), " Delivery date\Order number\Items\Total 信息不存在"
        #     # 验证订单卡片上 Delivery date\Order number\Items\Total 信息
        #     for item2 in item.locator(dweb_order_list_ele.order_list_card_items_ele).all():
        #         for item3 in item2.locator(dweb_order_list_ele.order_list_card_item_ele).all():
        #             assert item3.locator(dweb_order_list_ele.order_list_card_info_ele).is_visible()
        #             assert item3.locator(dweb_order_list_ele.order_list_card_label_ele).is_visible()
        #     # 断言全部订单下不包含取消订单
        #     assert item.locator(dweb_order_list_ele.order_list_card_statu_ele).text_content() not in ("Canceled","Cancelled"), "订单状态不是Cancelled"
        #     assert item.locator(dweb_order_list_ele.order_list_card_product_ele).is_visible()
        # # 点击卡片进入pdp
        # order_list_items[0].locator(dweb_order_list_ele.order_list_card_product_ele).click()
        # p.wait_for_timeout(2000)
        # # 断言页面进入订单详情页面
        # assert "order/detail/" in p.url , "没有进入订单详情页面"
        # p.go_back()




