import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【102677】 PC-Account 页面-我的订单-UI/UX验证")
class TestDWebAccountAllOrderUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【102677】 PC-Account 页面-我的订单-UI/UX验证")
    def test_102677_dWeb_account_my_order_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【102677】 PC-Account 页面-我的订单-UI/UX验证
        测试步骤：
       1、依次显示：今日订单（如有）、全部订单、待付款、待发货、已发货、待晒单、已取消按钮
        2、点击我的订单，进入我的订单页面
        3、并默认选中今日订单（如有），否则默认“全部订单”tab
        4、切换各订单类型的tab
        5、切换成功，数据跟随订单类型过滤正确 --这一步在每个订单case 里已包含
        6、切换右上角 订单时间范围
        """
        p: Page = page.get("page")
        c = page.get("context")

        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # all全部订单 tab
        all_tab = p.get_by_test_id(dweb_order_list_ele.order_all_tab_ele)
        assert all_tab.is_visible(), "未找到全部订单tab"
        # 切换到全部订单tab
        all_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到全部订单tab
        print(p.url)
        assert "filter_status=all" in p.url, "未切换到全部订单tab"


        # 待付款tab
        pending_tab = p.get_by_test_id(dweb_order_list_ele.order_pending_tab_ele)
        assert pending_tab.is_visible(), "未找到待付款tab"
        # 切换到待付款tab
        pending_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待付款tab
        print(p.url)
        assert "filter_status=1" in p.url, "未切换到待付款tab"

        # 切换到待发货tab
        unshipped_tab = p.get_by_test_id(dweb_order_list_ele.order_unshipped_tab_ele)
        assert unshipped_tab.is_visible(), "未找到待发货tab"
        # 切换到待发货tab
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待发货tab
        print(p.url)
        assert "filter_status=2" in p.url, "未切换到待发货tab"

        # 切换到已发货tab
        shipped_tab = p.get_by_test_id(dweb_order_list_ele.order_shipped_tab_ele)
        assert shipped_tab.is_visible(), "未找到已发货tab"
        # 切换到已发货tab
        shipped_tab.click()
        p.wait_for_timeout(2000)
        assert "filter_status=3" in p.url, "未切换到已发货tab"

        # 切换到待晒单tab
        reviewed_tab = p.get_by_test_id(dweb_order_list_ele.order_review_tab_ele)
        assert reviewed_tab.is_visible(), "未找到待晒单tab"
        # 切换到待晒单tab
        reviewed_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待晒单tab
        assert "filter_status=6" in p.url, "未切换到待晒单tab"

        # 切换到已取消tab
        canceled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert canceled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消tab
        canceled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消tab
        assert "filter_status=4" in p.url, "未切换到已取消tab"