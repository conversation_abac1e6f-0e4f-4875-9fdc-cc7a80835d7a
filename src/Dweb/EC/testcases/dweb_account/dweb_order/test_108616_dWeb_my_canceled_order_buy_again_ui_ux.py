import allure
import pytest

from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_order_list_ele, dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_page_account.dweb_page_order.dweb_page_order_list import DWebOrderListPage
from src.config.weee.log_help import log


@allure.story("【108616】 订单列表已取消tab-再来一单流程验证")
class TestDWebMyCanceledOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.dweb_porder, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108616】 订单列表已取消tab-再来一单流程验证")
    def test_108616_dWeb_my_canceled_order_buy_again_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【108616】 订单列表已取消tab-再来一单流程验证
        测试步骤：
        1、进入订单列表页面，点击切换到已取消tab下
        2、找到订单列表下再来一单按钮，右侧会撤拉出商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选去掉，加入购物车按钮置灰，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面
        3\4 两条在 再来一单case 已包含
        """
        p: Page = page.get("page")
        c = page.get("context")
        
        # 1. 进入订单列表页面
        order_page = DWebOrderListPage(p, pc_autotest_header, browser_context=c, page_url="/account/my_orders")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(dweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消 tab
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消 tab
        assert "filter_status=4" in p.url, "未切换到已取消tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(dweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 已取消tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(dweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(dweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("btn-main-banner-img-0").is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表上buy order 按钮
            buy_order_btns = p.get_by_test_id(dweb_order_list_ele.order_buy_again_btn).all()
            if len(buy_order_btns) == 0:
                log.warning("已取消tab下没有订单，无法继续测试")
                pytest.skip("已取消tab下没有订单，跳过测试")
            else:
                for index, item in enumerate(buy_order_btns):
                    # 点击再来一单按钮
                    item.click()
                    p.wait_for_timeout(3000)
                    p.get_by_test_id(dweb_buy_again_ele.buy_again_page_info).is_visible()
                    break







