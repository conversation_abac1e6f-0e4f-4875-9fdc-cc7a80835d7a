# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_114780_dweb_home_cmscollection_ui_ux.py
@Description    :
@CreateTime     :  2025/8/1 06:48
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/8/1 06:48
"""
import allure
import pytest
from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from src.api.zipcode import switch_zipcode
from src.config.weee.log_help import log


@allure.story("PC首页-CMS合集UI/UX验证")
class TestDWebHomeCMSCollectionUIUX:
    pytestmark = [pytest.mark.dweb_todo, pytest.mark.miaoxiao]

    @allure.title("PC首页-CMS合集UI/UX验证")
    @pytest.mark.present
    def test_114780_dweb_home_cmscollection_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【114780】PC首页-CMS合集UI/UX验证

        测试步骤:
        1. 访问首页
        2. 页面向下滑找到CMS合集
        3. 找到合集后验证这个合集下方存在商品卡片
        4. 合集中验证第一个商品的基本元素
        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击
        6. 对于第一个商品进行加购
        7. 点击合集右侧更多按钮
        8. 点击商品图片进入pdp
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 先通过API切换zipcode
        switch_zipcode(pc_autotest_header, '99991')
        p.wait_for_timeout(2000)

        # # # 1. 访问首页
        # # with allure.step("访问首页"):
        # #     home_page = DWebHomePage(p, pc_autotest_header, browser_context=c, zipcode="99991")
        # #     p.wait_for_timeout(3000)
        # #     log.info("成功访问首页")
        # # 1. 访问首页
        # with allure.step("访问首页"):
        #     home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)
        #     p.wait_for_timeout(3000)
        #
        #     # 手动切换zipcode到99991
        #     home_page.home_page_switch_zipcode("99991")
        #     p.wait_for_timeout(2000)
        #     log.info("成功访问首页并切换zipcode到99991")

        # 2. 页面向下滑找到CMS合集
        with allure.step("查找CMS合集"):
            cms_collection = None
            max_scrolls = 10
            scroll_count = 0

            while scroll_count < max_scrolls:
                # 查找包含mod-item-carousel-cm_collection的合集
                collections = p.locator("[data-testid*='mod-item-carousel-cm_collection']").all()
                if collections:
                    cms_collection = collections[0]
                    cms_collection.scroll_into_view_if_needed()
                    p.wait_for_timeout(1000)
                    log.info(f"找到CMS合集: {cms_collection.get_attribute('data-testid')}")
                    break

                # 向下滚动
                p.evaluate("window.scrollBy(0, window.innerHeight)")
                p.wait_for_timeout(1000)
                scroll_count += 1

            assert cms_collection, "未找到CMS合集"

        # 3. 验证合集下方存在商品卡片
        with allure.step("验证合集中存在商品卡片"):
            product_cards = cms_collection.get_by_test_id("wid-product-card-container").all()
            assert len(product_cards) > 0, "合集中未找到商品卡片"
            log.info(f"合集中找到 {len(product_cards)} 个商品卡片")

        # 4. 合集中验证第一个商品的基本元素
        with allure.step("验证第一个商品的基本元素"):
            first_product = product_cards[0]

            # 验证商品图片
            product_image = first_product.get_by_test_id("wid-product-card-image")
            assert product_image.is_visible(), "商品图片不可见"
            log.info("商品图片验证通过")

            # 验证商品价格
            product_price = first_product.get_by_test_id("wid-product-card-price")
            assert product_price.is_visible(), "商品价格不可见"
            price_text = product_price.text_content()
            assert price_text and len(price_text.strip()) > 0, "商品价格为空"
            log.info(f"商品价格验证通过: {price_text}")

            # 验证商品标题
            product_title = first_product.get_by_test_id("wid-product-card-title")
            assert product_title.is_visible(), "商品标题不可见"
            title_text = product_title.text_content()
            assert title_text and len(title_text.strip()) > 0, "商品标题为空"
            log.info(f"商品标题验证通过: {title_text}")

        # 5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击
        with allure.step("验证收藏按钮功能"):
            # 鼠标悬浮到商品卡片上
            first_product.hover()
            p.wait_for_timeout(1000)

            # 验证收藏按钮存在
            favorite_btn = first_product.get_by_test_id("btn-favorite")
            assert favorite_btn.is_visible(), "收藏按钮不可见"
            log.info("收藏按钮验证通过")

            # 点击收藏按钮
            try:
                favorite_btn.click()
                p.wait_for_timeout(1000)
                log.info("成功点击收藏按钮")
            except Exception as e:
                log.warning(f"点击收藏按钮失败: {str(e)}")

        # 6. 对于第一个商品进行加购
        with allure.step("对第一个商品进行加购"):
            # 确保还在商品卡片上悬浮
            first_product.hover()
            p.wait_for_timeout(500)

            # 查找加购按钮
            atc_btn = first_product.get_by_test_id("btn-atc-plus")
            assert atc_btn.is_visible(), "加购按钮不可见"

            try:
                atc_btn.click()
                p.wait_for_timeout(2000)
                log.info("成功点击加购按钮")
            except Exception as e:
                log.warning(f"点击加购按钮失败: {str(e)}")

        # 7. 点击合集右侧更多按钮
        with allure.step("点击合集更多按钮"):
            more_btn = cms_collection.get_by_test_id("btn-more-link")
            if more_btn.is_visible():
                try:
                    more_btn.click()
                    p.wait_for_timeout(3000)
                    log.info("成功点击更多按钮")

                    # 验证是否跳转到相应页面
                    current_url = p.url
                    log.info(f"点击更多按钮后的URL: {current_url}")

                    # 返回首页继续测试
                    p.go_back()
                    p.wait_for_timeout(2000)

                    # 重新定位到CMS合集
                    collections = p.locator("[data-testid*='mod-item-carousel-cm_collection']").all()
                    if collections:
                        cms_collection = collections[0]
                        cms_collection.scroll_into_view_if_needed()
                        p.wait_for_timeout(1000)
                        product_cards = cms_collection.get_by_test_id("wid-product-card-container").all()
                        first_product = product_cards[0]

                except Exception as e:
                    log.warning(f"点击更多按钮失败: {str(e)}")
            else:
                log.info("未找到更多按钮，跳过此步骤")

        # 8. 点击商品图片进入pdp
        with allure.step("点击商品图片进入PDP"):
            product_image = first_product.get_by_test_id("wid-product-card-image")

            try:
                product_image.click()
                p.wait_for_timeout(3000)

                # 验证是否进入PDP页面
                current_url = p.url
                assert "/product/" in current_url, f"未成功进入PDP页面，当前URL: {current_url}"

                # 验证PDP页面基本元素
                assert p.locator("h1").is_visible(), "PDP页面标题不可见"
                log.info(f"成功进入PDP页面: {current_url}")

            except Exception as e:
                log.error(f"点击商品图片进入PDP失败: {str(e)}")
                assert False, f"点击商品图片进入PDP失败: {str(e)}"

        log.info("PC首页-CMS合集UI/UX验证测试完成")