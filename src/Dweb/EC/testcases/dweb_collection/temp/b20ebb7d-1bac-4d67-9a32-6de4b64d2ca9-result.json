{"name": "PC首页-CMS合集UI/UX验证", "status": "passed", "description": "\n        【114780】PC首页-CMS合集UI/UX验证\n\n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        ", "steps": [{"name": "访问首页", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_collection\\test_114780_dweb_home_cmscollection_ui_ux.py\", line 61, in test_114780_dweb_home_cmscollection_ui_ux\n    home_page.home_page_switch_zipcode(\"99991\")\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\dweb_pages\\dweb_common_page.py\", line 26, in home_page_switch_zipcode\n    self.page.get_by_test_id(\"wid-btn-zipcode-btn\").click()\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 15784, in click\n    self._sync(\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 113, in _sync\n    self._dispatcher_fiber.switch()\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py\", line 56, in greenlet_main\n    self._loop.run_until_complete(self._connection.run_as_sync())\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 641, in run_until_complete\n    self.run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 321, in run_forever\n    super().run_forever()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 608, in run_forever\n    self._run_once()\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py\", line 1898, in _run_once\n    event_list = self._selector.select(timeout)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 444, in select\n    self._poll(timeout)\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py\", line 825, in _poll\n    status = _overlapped.GetQueuedCompletionStatus(self._iocp, ms)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1754144381314, "stop": 1754144451496}], "start": 1754144378433, "stop": 1754144451499, "uuid": "5c78e074-5163-472a-8e26-d3a34a27c536", "testCaseId": "48e2e0f1654051d395123f4259ef03d0", "fullName": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX#test_114780_dweb_home_cmscollection_ui_ux"}