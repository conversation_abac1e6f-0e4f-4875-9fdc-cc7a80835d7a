{"name": "PC首页-CMS合集UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到CMS合集\nassert None", "trace": "self = <src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX object at 0x000002B1A0CE18D0>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='about:blank'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...ta45zXaVJCs8tKEpDaTMr4PwRRu4BPTo_-FA-VTgc7wosXTH0bQz4ki9E0XccENgW5fv9taDjPhDXEHMBzfxvBY1VxDuavkZMrcYtzXRa6izJzvs', ...}\nlogin_trace = None\n\n    @allure.title(\"PC首页-CMS合集UI/UX验证\")\n    @pytest.mark.present\n    def test_114780_dweb_home_cmscollection_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【114780】PC首页-CMS合集UI/UX验证\n    \n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 先通过API切换zipcode\n        switch_zipcode(pc_autotest_header, '99991')\n        p.wait_for_timeout(2000)\n    \n        # # # 1. 访问首页\n        # # with allure.step(\"访问首页\"):\n        # #     home_page = DWebHomePage(p, pc_autotest_header, browser_context=c, zipcode=\"99991\")\n        # #     p.wait_for_timeout(3000)\n        # #     log.info(\"成功访问首页\")\n        # # 1. 访问首页\n        # with allure.step(\"访问首页\"):\n        #     home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n        #     p.wait_for_timeout(3000)\n        #\n        #     # 手动切换zipcode到99991\n        #     home_page.home_page_switch_zipcode(\"99991\")\n        #     p.wait_for_timeout(2000)\n        #     log.info(\"成功访问首页并切换zipcode到99991\")\n    \n        # 2. 页面向下滑找到CMS合集\n        with allure.step(\"查找CMS合集\"):\n            cms_collection = None\n            max_scrolls = 10\n            scroll_count = 0\n    \n            while scroll_count < max_scrolls:\n                # 查找包含mod-item-carousel-cm_collection的合集\n                collections = p.locator(\"[data-testid*='mod-item-carousel-cm_collection']\").all()\n                if collections:\n                    cms_collection = collections[0]\n                    cms_collection.scroll_into_view_if_needed()\n                    p.wait_for_timeout(1000)\n                    log.info(f\"找到CMS合集: {cms_collection.get_attribute('data-testid')}\")\n                    break\n    \n                # 向下滚动\n                p.evaluate(\"window.scrollBy(0, window.innerHeight)\")\n                p.wait_for_timeout(1000)\n                scroll_count += 1\n    \n>           assert cms_collection, \"未找到CMS合集\"\nE           AssertionError: 未找到CMS合集\nE           assert None\n\ntest_114780_dweb_home_cmscollection_ui_ux.py:86: AssertionError"}, "description": "\n        【114780】PC首页-CMS合集UI/UX验证\n\n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        ", "steps": [{"name": "查找CMS合集", "status": "failed", "statusDetails": {"message": "AssertionError: 未找到CMS合集\nassert None\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_collection\\test_114780_dweb_home_cmscollection_ui_ux.py\", line 86, in test_114780_dweb_home_cmscollection_ui_ux\n    assert cms_collection, \"未找到CMS合集\"\n"}, "start": 1754144578227, "stop": 1754144589270}], "start": 1754144574814, "stop": 1754144589270, "uuid": "5cab7443-68cf-4944-84b8-b4845aa6060f", "historyId": "48e2e0f1654051d395123f4259ef03d0", "testCaseId": "48e2e0f1654051d395123f4259ef03d0", "fullName": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX#test_114780_dweb_home_cmscollection_ui_ux", "labels": [{"name": "story", "value": "PC首页-CMS合集UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_collection"}, {"name": "suite", "value": "test_114780_dweb_home_cmscollection_ui_ux"}, {"name": "subSuite", "value": "TestDWebHomeCMSCollectionUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "18332-Main<PERSON><PERSON><PERSON>"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux"}]}