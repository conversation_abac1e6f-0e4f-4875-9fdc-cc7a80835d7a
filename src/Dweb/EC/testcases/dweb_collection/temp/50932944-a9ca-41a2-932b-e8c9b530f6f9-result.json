{"name": "PC首页-CMS合集UI/UX验证", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.", "trace": "self = <src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX object at 0x0000020862C5D090>\npage = {'context': <BrowserContext browser=<Browser type=<BrowserType name=chromium executable_path=C:\\Users\\<USER>\\AppDat...aywright\\chromium-1091\\chrome-win\\chrome.exe> version=120.0.6099.28>>, 'page': <Page url='https://www.sayweee.com/en'>}\npc_autotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'Zipcode': '98011', 'app-version': 'null', 'authorization': 'Bearer...dfIkQKlpISNpf9NQVX3PFcX3VpgoJIoecRs61przjxvgAMDOUkSxUjN4OMA4sDGkUOZ3TITFrpXbDONjbA0XGEUNLGbxstno5rw4IhXGaSrbyPqs', ...}\nlogin_trace = None\n\n    @allure.title(\"PC首页-CMS合集UI/UX验证\")\n    @pytest.mark.present\n    def test_114780_dweb_home_cmscollection_ui_ux(self, page: dict, pc_autotest_header, login_trace):\n        \"\"\"\n        【114780】PC首页-CMS合集UI/UX验证\n    \n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        \"\"\"\n        p: Page = page.get(\"page\")\n        c = page.get(\"context\")\n        # 先通过API切换zipcode\n        switch_zipcode(pc_autotest_header, '99991')\n    \n        # # 1. 访问首页\n        # with allure.step(\"访问首页\"):\n        #     home_page = DWebHomePage(p, pc_autotest_header, browser_context=c, zipcode=\"99991\")\n        #     p.wait_for_timeout(3000)\n        #     log.info(\"成功访问首页\")\n        # 1. 访问首页\n        with allure.step(\"访问首页\"):\n            home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)\n            p.wait_for_timeout(3000)\n    \n            # 手动切换zipcode到99991\n>           home_page.home_page_switch_zipcode(\"99991\")\n\ntest_114780_dweb_home_cmscollection_ui_ux.py:60: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n..\\..\\dweb_pages\\dweb_common_page.py:26: in home_page_switch_zipcode\n    self.page.get_by_test_id(\"wid-btn-zipcode-btn\").click()\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py:15784: in click\n    self._sync(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py:159: in click\n    return await self._frame.click(self._selector, strict=True, **params)\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py:484: in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:62: in send\n    return await self._connection.wrap_api_call(\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:492: in wrap_api_call\n    return await cb()\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _\n\nself = <playwright._impl._connection.Channel object at 0x00000208687CF290>\nmethod = 'click'\nparams = {'selector': 'internal:testid=[data-testid=\"wid-btn-zipcode-btn\"s]', 'strict': True}\nreturn_as_dict = False\n\n    async def inner_send(\n        self, method: str, params: Optional[Dict], return_as_dict: bool\n    ) -> Any:\n        if params is None:\n            params = {}\n        callback = self._connection._send_message_to_server(\n            self._object, method, params\n        )\n        if self._connection._error:\n            error = self._connection._error\n            self._connection._error = None\n            raise error\n        done, _ = await asyncio.wait(\n            {\n                self._connection._transport.on_error_future,\n                callback.future,\n            },\n            return_when=asyncio.FIRST_COMPLETED,\n        )\n        if not callback.future.done():\n            callback.future.cancel()\n>       result = next(iter(done)).result()\nE       playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n\n..\\..\\..\\..\\..\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py:100: TimeoutError"}, "description": "\n        【114780】PC首页-CMS合集UI/UX验证\n\n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        ", "steps": [{"name": "访问首页", "status": "broken", "statusDetails": {"message": "playwright._impl._errors.TimeoutError: Timeout 50000ms exceeded.\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\testcases\\dweb_collection\\test_114780_dweb_home_cmscollection_ui_ux.py\", line 60, in test_114780_dweb_home_cmscollection_ui_ux\n    home_page.home_page_switch_zipcode(\"99991\")\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\dweb_pages\\dweb_common_page.py\", line 26, in home_page_switch_zipcode\n    self.page.get_by_test_id(\"wid-btn-zipcode-btn\").click()\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\sync_api\\_generated.py\", line 15784, in click\n    self._sync(\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_locator.py\", line 159, in click\n    return await self._frame.click(self._selector, strict=True, **params)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_frame.py\", line 484, in click\n    await self._channel.send(\"click\", locals_to_params(locals()))\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 62, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 492, in wrap_api_call\n    return await cb()\n           ^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\playwright\\_impl\\_connection.py\", line 100, in inner_send\n    result = next(iter(done)).result()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1754143668467, "stop": 1754143770277}], "start": 1754143667462, "stop": 1754143770277, "uuid": "3f1ad7ad-a6c1-4109-9faf-32bebbfc1ebc", "historyId": "48e2e0f1654051d395123f4259ef03d0", "testCaseId": "48e2e0f1654051d395123f4259ef03d0", "fullName": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX#test_114780_dweb_home_cmscollection_ui_ux", "labels": [{"name": "story", "value": "PC首页-CMS合集UI/UX验证"}, {"name": "tag", "value": "present"}, {"name": "tag", "value": "case"}, {"name": "tag", "value": "test"}, {"name": "tag", "value": "dweb_todo"}, {"name": "tag", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "parentSuite", "value": "src.Dweb.EC.testcases.dweb_collection"}, {"name": "suite", "value": "test_114780_dweb_home_cmscollection_ui_ux"}, {"name": "subSuite", "value": "TestDWebHomeCMSCollectionUIUX"}, {"name": "host", "value": "SHLAP10864"}, {"name": "thread", "value": "7708-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux"}]}