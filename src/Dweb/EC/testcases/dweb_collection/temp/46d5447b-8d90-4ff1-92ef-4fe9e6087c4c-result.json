{"name": "PC首页-CMS合集UI/UX验证", "status": "skipped", "description": "\n        【114780】PC首页-CMS合集UI/UX验证\n\n        测试步骤:\n        1. 访问首页\n        2. 页面向下滑找到CMS合集\n        3. 找到合集后验证这个合集下方存在商品卡片\n        4. 合集中验证第一个商品的基本元素\n        5. 鼠标悬浮在第一个商品卡片上，验证存在收藏按钮并点击\n        6. 对于第一个商品进行加购\n        7. 点击合集右侧更多按钮\n        8. 点击商品图片进入pdp\n        ", "start": 1754144525177, "stop": 1754144525177, "uuid": "22f0b9e8-d729-4e24-8fee-7209533943eb", "testCaseId": "48e2e0f1654051d395123f4259ef03d0", "fullName": "src.Dweb.EC.testcases.dweb_collection.test_114780_dweb_home_cmscollection_ui_ux.TestDWebHomeCMSCollectionUIUX#test_114780_dweb_home_cmscollection_ui_ux"}