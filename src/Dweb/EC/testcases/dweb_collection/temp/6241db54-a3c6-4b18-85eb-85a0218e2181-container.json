{"uuid": "e6412478-91ef-4184-8b77-b34a53772ce3", "children": ["22f0b9e8-d729-4e24-8fee-7209533943eb"], "befores": [{"name": "porder", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\pluggy\\_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\_pytest\\fixtures.py\", line 902, in call_fixture_func\n    fixture_result = fixturefunc(**kwargs)\n                     ^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\Dweb\\EC\\conftest.py\", line 136, in porder\n    porder = query_simple_preorder_v1(pc_autotest_header)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\api\\porder.py\", line 22, in query_simple_preorder_v1\n    res = HR.request({\n          ^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\utils\\HttpRequest.py\", line 31, in request\n    response = cls.__get(url=data.get(\"path\"), params=data.get(\"param\"), headers=data.get(\"headers\"), **kwargs)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\src\\utils\\HttpRequest.py\", line 51, in __get\n    response = requests.get(url, params=params, headers=headers, **kwargs)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\requests\\api.py\", line 73, in get\n    return request(\"get\", url, params=params, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\requests\\api.py\", line 59, in request\n    return session.request(method=method, url=url, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\requests\\sessions.py\", line 589, in request\n    resp = self.send(prep, **send_kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\requests\\sessions.py\", line 703, in send\n    r = adapter.send(request, **kwargs)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\requests\\adapters.py\", line 667, in send\n    resp = conn.urlopen(\n           ^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 789, in urlopen\n    response = self._make_request(\n               ^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 466, in _make_request\n    self._validate_conn(conn)\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py\", line 1095, in _validate_conn\n    conn.connect()\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\connection.py\", line 615, in connect\n    self.sock = sock = self._new_conn()\n                       ^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\connection.py\", line 196, in _new_conn\n    sock = connection.create_connection(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\projects-new\\qa-ui-dmweb\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py\", line 73, in create_connection\n    sock.connect(sa)\n"}, "start": 1754144531441, "stop": 1754144531639}], "start": 1754144531441, "stop": 1754144532176}