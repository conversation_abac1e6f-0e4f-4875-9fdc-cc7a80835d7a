import allure
import pytest

from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_login_and_signin import LoginAndSignin


@allure.story("首页登陆加购或先加购再登陆")
class TestLoginAndSignin:
    pytestmark = [pytest.mark.coreflow, pytest.mark.dweb_regression]

    @pytest.fixture(scope='function')
    def setup(self, not_login_page: dict, pc_autotest_header):
        lp = LoginAndSignin(not_login_page["page"], pc_autotest_header)
        yield lp

    # @pytest.mark.repeat(15)
    def test_home_login_and_place_order(self, setup, not_login_trace):
        setup.login_and_place_order()

    def test_home_place_order_then_login(self, setup, not_login_trace):
        setup.place_order_then_login()

    # @pytest.mark.repeat(15)
    def _test_home_signin(self, setup):
        setup.signin()


