import allure
import pytest

from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.Dweb.EC.dweb_pages.dweb_page_home.dweb_page_home import DWebHomePage
from playwright.sync_api import Page
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("【109702】 PC首页右上角小购物车-小购物车的交互")
class TestDWebMiniGroceryCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.dweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【109702】 PC首页右上角小购物车-小购物车的交互")
    def test_109702_dWeb_mini_grocery_cart_ui_ux(self, page: dict, pc_autotest_header, login_trace):
        """
        【109702】 PC首页右上角小购物车-小购物车的交互
        """
        p: Page = page.get("page")
        c = page.get("context")
        # 构造的首页页面
        home_page = DWebHomePage(p, pc_autotest_header, browser_context=c)
        # 清空购物车
        try:
            empty_cart(pc_autotest_header)
            # 清空购物车之后刷新页面
            p.reload()
            p.wait_for_timeout(2000)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 鼠标悬停又上角的mini购物车
        p.get_by_test_id("wid-mini-cart").hover()
        p.wait_for_timeout(2000)
        # 断言mini 空购物车img存在
        assert home_page.FE.ele(dweb_cart_ele.ele_mini_cart_img).is_visible()
        # 断言mini空购物车文案存在
        assert home_page.FE.ele(dweb_cart_ele.ele_mini_cart_text).is_visible()

        # 构造的分类页面
        category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c)
        # 去分类页加购Local类型的商品进购物车
        category_page.add_to_local_product_cart_from_category()
        p.reload()
        p.wait_for_timeout(2000)
        # 断言购物车商品数字大于0
        cart_num = p.get_by_test_id("wid-mini-cart").locator(u"//span[contains(@class,'MiniCart_cartIconCountQty')]")
        assert cart_num, f'购物车商品是null'

        # 鼠标悬停又上角的mini购物车
        p.get_by_test_id("wid-mini-cart").hover()
        p.wait_for_timeout(2000)
        # 断言progress bar 进度条模块
        progress_tip_bar = home_page.FE.ele(dweb_cart_ele.ele_mini_progress_tip_bar).get_attribute("class")
        if "success" in progress_tip_bar:
            # 进度条满了
            assert "unlocked. Nicely done!" in home_page.FE.ele(dweb_cart_ele.ele_mini_progress_tip_copy)
        else:
            # 未满
            assert "Add" in home_page.FE.ele(dweb_cart_ele.ele_mini_progress_tip_copy).text_content()

        # 断言mini local delivery 的img存在
        assert home_page.FE.ele(dweb_cart_ele.ele_mini_cart_grocery_img).is_visible()
        # 断言mini 购物车有商品
        assert home_page.FE.ele(dweb_cart_ele.ele_mini_items_num)

        mini_items_list = home_page.FE.eles(dweb_cart_ele.ele_mini_items_list)

        for item in mini_items_list:
            # 验证mini 购物车商品image 存在
            item_img = item.query_selector(u"//div[@data-component='CroppedImage']/img[@src]")
            assert item_img.is_visible()
            # 验证mini 购物车商品title 存在
            item_title = item.query_selector(u"//div[contains(@class,'GoodsInMiniCart_nam')]")
            assert item_title.is_visible()
            # 验证mini 购物车商品x数量存在
            item_x = item.query_selector(u"//div[contains(@class,'GoodsInMiniCart_qty')]").text_content()
            assert item_x

        # 点击第一个商品title位置进入pdp
        mini_items_list[0].query_selector(u"//div[contains(@class,'GoodsInMiniCart_nam')]").click()

        # 鼠标悬停又上角的mini购物车
        p.get_by_test_id("wid-mini-cart").hover()
        p.wait_for_timeout(2000)
        # 断言goto cart 按钮存在
        goto_cart_button = home_page.FE.ele(dweb_cart_ele.ele_mini_goto_cart_button)
        assert goto_cart_button.is_visible()
        # 点击goto cart button 进入购物车
        goto_cart_button.click()
