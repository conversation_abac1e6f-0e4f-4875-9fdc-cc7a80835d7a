"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112719_mweb_mo_cart_ui_ux.py
@Description    :
@CreateTime     :  2025/4/10 14:33
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/10 14:33
"""

import allure
import pytest
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_cart import dweb_cart_ele
from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_pages.dweb_page_cart.dweb_page_cart import DWebCartPage
from src.Dweb.EC.dweb_pages.dweb_page_category.dweb_page_category import DWebCategorypage
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("PC购物车-MO购物车UI/UX验证")
class TestDwebMoCartUIUX:
    pytestmark = [pytest.mark.pccart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.zhuli]

    @allure.title("PC购物车-MO购物车UI/UX验证")
    def test_112719_DWeb_mo_cart_ui_ux(self, page: dict, pc_autotest_header):
        """
        【112719】 PC购物车-MO购物车UI/UX验证
        """
        p: Page = page.get("page")
        c = page.get("context")
        switch_zipcode(pc_autotest_header, '99348')
        p.wait_for_timeout(2000)
        cart_page = DWebCartPage(p, pc_autotest_header, browser_context=c,
                                 page_url="/cart")

        # 2.清除购物车
        empty_cart(pc_autotest_header)

        p.reload()
        p.wait_for_timeout(3000)
        # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购MO Local Delivery商品"):
            # 创建分类页面对象
            category_page = DWebCategorypage(p, pc_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选模块是否存在
            filter_button = category_page.page.get_by_test_id("wid-filters-container")
            log.info(f"筛选模块是否可见: {filter_button.is_visible(timeout=1000)}")

            # 尝试直接使用XPath定位mo Local Delivery选项
            local_filter_id = category_page.page.get_by_test_id(dweb_category_ele.ele_mo_delivery_test_id)

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Fulfilled by Weee",
                filter_id=local_filter_id,
                count=3,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"
        # 滚动到指定位置-购物车顶部
        # scroll_one_page_until(p, dweb_cart_ele.ele_cart_normal_name)
        with allure.step("验证购物车UI"):
            log.info(f"开始验证购物车ui")
            # 创建购物车页面对象
            dwebcart_page = DWebCartPage(p, pc_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)
            # 关闭可能出现的广告弹窗
            if dwebcart_page.page.locator("//img[contains(@aria-label, 'close button')]").all():
                dwebcart_page.page.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")
            # scroll_one_page_until(p, dweb_cart_ele.ele_cart_normal_name)
            assert p.get_by_test_id(dweb_cart_ele.ele_cart_normal_name).is_visible(), "直邮购物车不存在"

            # 判断直邮购物车的标题=Direct mail
            assert "Direct mail" == p.get_by_test_id(dweb_cart_ele.ele_cart_normal_name).text_content()
            # # 判断只有购物车标题下面的文案显示正确
            assert "Shipping via FedEx, UPS, etc." == p.locator(dweb_cart_ele.ele_grocery_mkpl_text).text_content()

            # 判断shipping_fee中有美元符号存在或为free
            shipping_fee = p.get_by_test_id(dweb_cart_ele.ele_cart_mo_shipping_fee_price).all()
            for sf in shipping_fee:
                log.info("shipping_fee的content===>" + sf.text_content())
                assert "$" in sf.text_content() or 'Free shipping' == sf.text_content()
            # 获取所有的items total
            # items_total = p.get_by_test_id(dweb_cart_ele.ele_cart_subtotal).all()
            # assert items_total, f"Subtotal={items_total}"
            #
            # # 判断items_total中有美元符号存在
            # for item in items_total:
            #     log.info("item.text_content===>" + item.text_content())
            #     assert "$" in item.text_content()
            #     p.wait_for_timeout(2000)
        
        # 验证购物车商品
        with allure.step("验证购物车商品"):
            # 使用合并后的方法验证购物车商品
            assert cart_page.verify_cart_items(cart_type="normal"), "MO购物车商品验证失败"
        
        # # 执行稍后再买操作
        with allure.step("执行稍后再买操作"):
            assert cart_page.save_for_later_operations(cart_type="normal"), "MO稍后再买操作失败"
        p.wait_for_timeout(2000)

        # 执行购物车商品删除操作
        with allure.step("执行商品删除操作"):
            assert cart_page.remove_cart_item(cart_type="normal"), "MO购物车商品删除操作失败"
        log.info("H5购物车-MO购物车UI/UX----验证通过")
