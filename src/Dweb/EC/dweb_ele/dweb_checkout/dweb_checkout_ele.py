"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_checkout_ele.py
@Description    :  PC结算页元素定位
@CreateTime     :  2025/6/10 14:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 14:30
"""

# 结算页标题
ele_checkout_title = 'wid-logo-img'
ele_checkout_title_message_text = 'wid-checkout-header-message-text'

# -------------------loytal------------------------
ele_checkout_loytal_vip_title='wid-checkout-rewards-header-title'
ele_checkout_loytal_vip_icon = 'wid-checkout-rewards-header-icon'
ele_checkout_loytal_vip_content = 'wid-checkout-rewards-header-content'
# 右上角的loyalty的模块
ele_checkout_loytal_button='wid-checkout-rewards-header-button'
ele_checkout_loytal_button_text = 'wid-checkout-rewards-header-button-text'

# 配送地址信息区域
ele_address = 'wid-checkout-address'
# ele_delivery_time = u"//div[@data-testid='wid-checkout-delivery-time']"
# # ele_address_name = u"//div[contains(@class, 'address-name')]"
# ele_address_detail = u"//div[contains(@class, 'address-detail')]"
# ele_delivery_date = u"//div[contains(@class, 'delivery-date')]"
# ele_delivery_window = u"//div[contains(@class, 'delivery-window')]"
# 地址区域
ele_checkout_address_info = 'wid-checkout-address-info'
ele_checkout_address_info_title = 'wid-checkout-address-info-title'
# 地址模块的箭头
ele_checkout_address_arrow ='wid-checkout-address-info-arrow'
# 这个需要将地址展开才有
ele_checkout_address_card = 'wid-address-card'
ele_checkout_address_card_icon = 'wid-address-card-location-icon'
ele_address_name = 'wid-address-card-name'
ele_address_address = 'wid-address-card-address'
ele_address_city = 'wid-address-card-city'
ele_address_edit = 'wid-address-card-edit'
# 点开地址的右上角箭头后展示添加新地址按钮
ele_checkout_address_info_address_bt = 'wid-checkout-address-info-add-address-btn'
# 没有地址的时候显示save按钮
ele_address_form_save ='btn-address-form-save'


# ---------------------------------支付方式模块--------------------------------------------
ele_payment_method_section = 'wid-checkout-payment-panel'
ele_payment_method_title = 'wid-checkout-payment-panel-title'
ele_payment_method_card_type = 'wid-checkout-payment-method-card-type'
ele_payment_card_number = 'wid-checkout-payment-method-card-number'
ele_payment_method_point = 'wid-checkout-payment-points'
ele_checkout_points_title = 'wid-checkout-payment-points-title'
ele_checkout_points_content = 'wid-checkout-payment-points-content'
# 积分选项开关
ele_checkout_points_switch = 'wid-checkout-payment-points-switch'
# 支付方式p=paypal,b=信用卡，v=venmo,L=applepay,M=ebt,i=微信，H=cashapp
ele_checkout_payment_method_p='wid-checkout-payment-method-p'
ele_checkout_payment_method_b='wid-checkout-payment-method-b'
ele_checkout_payment_method_i='wid-checkout-payment-method-i'
ele_checkout_payment_method_m='wid-checkout-payment-method-m'
ele_checkout_payment_method_h='wid-checkout-payment-method-h'
ele_checkout_payment_method_v='wid-checkout-payment-method-v'
# 支付方式下面的描述
ele_checkout_payment_method_item_desc = 'wid-checkout-payment-method-item-desc'
# -----------cvc----------------
ele_checkout_cvc_input = 'wid-checkout-payment-method-cvc-input'
ele_checkout_cvc_confirm_cvc  ='wid-checkout-payment-method-confirm-cvc'
ele_checkout_cvc_error = 'wid-checkout-payment-method-cvc-error'



# ------------------------------review order--------------------------------------------
ele_review_order = 'wid-checkout-review-order'
ele_review_order_title = 'wid-checkout-review-order-title'
# 小汽车icon
ele_review_order_shipping_icom = 'wid-checkout-review-order-card-shipping-icon'
# 生鲜购物车的标题
ele_review_order_shipping_type_desc = 'wid-checkout-review-order-card-shipping-type-desc'
# 生鲜购物车的配送描述
ele_review_order_shipping_desc = 'wid-checkout-review-order-card-shipping-desc'
# 生鲜购物车的配送时间
ele_review_order_shipping_date= 'wid-checkout-review-order-card-change-date-btn'
# 生鲜购物车商品数量
ele_review_order_shipping_total = 'wid-checkout-review-order-card-sub-total-qty'
# 生鲜购物车商品总金额
ele_review_order_shipping_total_price = 'wid-checkout-review-order-card-sub-total-price'
# delivery window窗口
ele_review_order_delivery_window_item = 'wid-review-order-deliver-windows-item'
# delivery window的标题
ele_review_order_delivery_window_item_desc  = 'wid-review-order-deliver-windows-item-desc'
ele_review_order_delivery_window_item_checkbox = 'wid-review-order-deliver-windows-item-checkbox'
ele_review_order_delivery_window_item_arrival_before = 'wid-review-order-deliver-windows-item-arrival-before'
ele_review_order_delivery_window_item_context_text = 'wid-review-order-deliver-windows-item-content-text'
ele_review_order_delivery_window_item_price = 'wid-review-order-deliver-windows-item-price'
#mkpl购物车预计送达文案
ele_checkout_shipping_type_desc ='wid-checkout-review-order-card-shipping-type-desc'
# 生鲜购物车的配送费
# ele_wid_review_order_card_normal_shipping_fee ='wid-review-order-card-normal-shipping-fee'
# 商品图片
ele_review_order_product_image = 'wid-checkout-review-order-card-product-panel-hide-item-img'
# 卡片右箭头
ele_rebiew_order_arrow_right = 'btn-checkout-review-order-card-product-panel-show-more'
# 选购完成了吗？文案
ele_checkout_panel_title = 'wid-checkout-purchase-panel-title'
ele_checkout_panel_amount = 'wid-checkout-purchase-panel-amount'
# 商品图片下面的配送文案
ele_review_order_delivery_window_content = 'wid-checkout-right-checkout-place-warning'
# 订单总结下面的weee相关条款
ele_checkout_order_terms='wid-checkout-right-checkout-place-order-terms'





# ----------------------------------------结算页右侧部分元素----------------------------------------
ele_checkout_right = 'wid-checkout-right'
# 实际支付
ele_subtotal = 'wid-checkout-right-final-payment-amount-title'
ele_subtotal_amount = 'wid-checkout-right-final-payment-amount'

# 订单总结
ele_checkout_right_summary_title = 'wid-checkout-right-purchase-summary-title'
# 总计
ele_checkout_right_summary_subtotal_title = 'wid-checkout-right-purchase-summary-item-subtotal-title'
ele_checkout_right_summary_subtotal_amount = 'wid-checkout-right-purchase-summary-item-amount'
# 税
ele_checkout_summary_taxes_title = 'wid-checkout-right-purchase-summary-item-taxes-title'
ele_checkout_summary_taxes_amount = 'wid-checkout-right-purchase-summary-item-amount'

# service fee的i标签
ele_order_service_fee_title='wid-checkout-right-purchase-summary-item-service_fee-title'
ele_order_service_fee_icon = 'wid-checkout-right-purchase-summary-item-service-icon'
ele_order_service_fee_amount = 'wid-checkout-right-purchase-summary-item-amount'
ele_order_service_fee_base_amount = 'wid-checkout-right-purchase-summary-item-base-amount'

# delivery fee
ele_order_delivery_fee_title='wid-checkout-right-purchase-summary-item-delivery_fee-title'
ele_order_delivery_fee_icon = 'wid-checkout-right-purchase-summary-item-service-icon'
ele_order_delivery_fee_amount = 'wid-checkout-right-purchase-summary-item-amount'
ele_order_delivery_fee_base_amount = 'wid-checkout-right-purchase-summary-item-base-amount'

# tip
ele_order_summary_tip_title  = 'wid-order-summary-item-tip-title'
ele_order_summary_tip_amount  = 'wid-order-summary-item-tip-amount'
ele_delivery_fee = u"//div[@data-testid='wid-checkout-delivery-fee']"
ele_tax = u"//div[@data-testid='wid-checkout-tax']"
ele_total = u"//div[@data-testid='wid-checkout-total']"
ele_order_summary_title = u"//div[contains(@class, 'summary-title')]"
ele_order_summary_items = u"//div[contains(@class, 'summary-items')]"
ele_order_summary_divider = u"//div[contains(@class, 'summary-divider')]"
ele_checkout_right_summary_item_amount='wid-checkout-right-purchase-summary-item-amount'

# --------------------优惠券--------------------------
ele_coupon_section = 'wid-checkout-right-apply-coupon-content'
ele_coupon_title = 'wid-checkout-right-apply-coupon-title-text'
ele_coupon_apply_coupon = 'wid-checkout-right-apply-coupon'

# 商品列表
ele_items = u"//div[@data-testid='wid-checkout-items']"
ele_item = u"//div[@data-testid='wid-checkout-item']"
ele_item_name = u"//div[@data-testid='wid-checkout-item-name']"
ele_item_price = u"//div[@data-testid='wid-checkout-item-price']"
ele_item_quantity = u"//div[@data-testid='wid-checkout-item-quantity']"
ele_item_image = u"//img[contains(@class, 'item-image')]"

# 下单按钮
ele_place_order = u"(//button[@data-testid='wid-checkout-btn'])[2]"


# 凑单banner
ele_free_shipping_banner = u"//div[@data-testid='wid-checkout-free-shipping-banner']"

# 小费区域
ele_tip_section = u"//div[@data-testid='wid-checkout-delivery-tip']"
ele_tip_options = u"//div[contains(@class, 'tip-options')]//button"
ele_tip_custom = u"//input[contains(@class, 'tip-custom')]"


# 订单备注区域
ele_order_note = u"//textarea[contains(@placeholder, 'note') or contains(@placeholder, 'Note')]"

# 结算页顶部的weeelogo
ele_checkout_top_logo = u"//div[@role='heading']"

# 结算页顶部的提示文案
ele_checkout_top_text = u"//span[text()='Your information is secured with encryption.']"

# 结算页小费标题
ele_checkout_tip_title = u"//div[@data-testid='wid-checkout-delivery-tip']/div/span[1]"

# 最终总计下面的文案
ele_checkout_final_text = u"//div[@class='mt-1']/div[1]/span[1]"
ele_checkout_final_submit_text = u"//div[@class='mt-1']/div[2]/span[1]"

# 错误提示信息
ele_error_message = u"//div[contains(@class, 'error-message')]"

# 加载状态指示器
ele_loading_indicator = u"//div[contains(@class, 'loading') or contains(@class, 'spinner')]"

# 返回购物车按钮
ele_back_to_cart = u"//a[contains(text(), 'Back to Cart')]"

# 地址选择下拉菜单
ele_address_dropdown = u"//div[contains(@class, 'address-dropdown')]"

# 配送时间选择下拉菜单
ele_delivery_time_dropdown = u"//div[contains(@class, 'delivery-time-dropdown')]"

# 支付方式选择下拉菜单
ele_payment_method_dropdown = u"//div[contains(@class, 'payment-method-dropdown')]"

# 订单确认复选框
ele_order_confirmation_checkbox = u"//input[@type='checkbox' and contains(@id, 'confirmation')]"

# 订单条款和条件链接
ele_terms_conditions_link = u"//a[contains(text(), 'Terms') or contains(text(), 'Conditions')]"

# 隐私政策链接
ele_privacy_policy_link = u"//a[contains(text(), 'Privacy') or contains(text(), 'Policy')]"

# 帮助/支持链接
ele_help_support_link = u"//a[contains(text(), 'Help') or contains(text(), 'Support')]"

# 页面底部版权信息
ele_copyright_info = u"//div[contains(@class, 'copyright') or contains(text(), '©')]"

# 安全支付图标
ele_secure_payment_icons = u"//div[contains(@class, 'secure-payment') or contains(@class, 'payment-icons')]"

# 页面底部导航链接
ele_footer_navigation = u"//div[contains(@class, 'footer-navigation') or contains(@class, 'footer-links')]"

# 页面底部社交媒体链接
ele_social_media_links = u"//div[contains(@class, 'social-media') or contains(@class, 'social-links')]"

# 页面底部语言选择器
ele_language_selector = u"//div[contains(@class, 'language-selector')]"

# 页面底部货币选择器
ele_currency_selector = u"//div[contains(@class, 'currency-selector')]"

ele_upsell_continue_checkout_btn='wid-upsell-continue-to-checkout-btn'
