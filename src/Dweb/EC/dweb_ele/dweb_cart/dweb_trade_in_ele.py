# 换购页
ele_trade_in = u"//div[contains(@class,'DisAccountDeal_list')]"
ele_trade_in_header = u"//div[contains(@class,'DisAccountDeal_headerDescWrapper')]"
ele_trade_in_products = ele_trade_in + u"//div[contains(@class,'DisAccountDeal_item')]"
ele_trade_in_product_content = u"//div[contains(@class,'mini_content')]"
ele_trade_in_product_price =  u"//div[contains(@class,'mini_price')]"
ele_trade_in_product_select = u"//button[contains(@class,'DisAccountDeal_select')]"
ele_trade_in_product_count = u"//p[contains(@class,'DisAccountDeal_count')]"
ele_trade_in_product_sold_count = u"//span[contains(@class,'weekly_sold_count')]"

# 换购页关闭按钮
ele_trade_in_close = u"//button[@class='ant-drawer-close']"


