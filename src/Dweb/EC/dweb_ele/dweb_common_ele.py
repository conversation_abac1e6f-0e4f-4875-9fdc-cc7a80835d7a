
#  切换日期pop
# 切换日期pop

# 切换日期选择日期弹窗
ele_delivery_date_popup =  u"//div[@class='ant-modal-content']"
# 选择日期弹窗里日期
ele_delivery_date = u"//div[contains(@data-testid,'wid-change-date')]//div[contains(@class,'Date_container')]"
# 商品卡片公共信息
ele_product_card_container = "wid-product-card-container"
# title
ele_product_card_title = "wid-product-card-title"
# image
ele_product_card_image = "wid-product-card-image"
# price
ele_product_card_price = "wid-product-card-price"
# base_price
ele_product_card_base_price = "wid-product-card-base-price"
# 加购按钮
ele_add_to_cart = 'btn-atc-plus'
# 售罄提示按钮  --待补充
ele_notify_me = "unAvailableButton"
# 切换日期按钮 --待补充
ele_change_date = "wid-change-date"
ele_mkpl_eta = ""
ele_product_card_brand = ""
ele_product_card_tag = ""


