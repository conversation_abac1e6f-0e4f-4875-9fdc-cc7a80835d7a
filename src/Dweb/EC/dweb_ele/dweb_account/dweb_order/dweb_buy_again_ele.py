
# 再来一单页面
buy_again_page_close = "btn-drawer-close"
# 切换日期入口
# 切换日期相关元素
buy_again_date_info_value = "wid-account-buy-again-date-info-value"
change_date_popup = "chenge_data_pop"
change_date_first_week = "wid-change-date-0"

# PC
buy_again_page_info = "wid-account-buy-again-date-info"
buy_again_page_title = "wid-account-buy-again-title"
# buy_again_page_content = "wid-order-buy-again-content"
buy_again_available = "wid-order-buy-again-content-available"
# 再来一单页面-商品列表
# H5
buy_again_available_product = "wid-order-buy-again-product-item"
# PC
buy_again_available_item = "wid-account-buy-again-product-item"

buy_again_available_item_content = "wid-order-buy-again-product-item-content"
buy_again_unavailable = "wid-order-buy-again-content-inavailable"
buy_again_item_checkbox = "wid-order-buy-again-product-item-checkbox"
# 再来一单页面-加入购物车按钮

buy_again_add_cart_button_count = "wid-order-buy-again-submit-count"
# pc
buy_again_add_cart_btn= "btn-account-buy-again-add-cart"
# 再来一单页面-全选按钮
# H5
buy_again_chose_all = "wid-order-buy-again-chose-all"
# pc
buy_again_select_all = "wid-account-buy-again-select-all"
buy_again_select_all_checkbox = "wid-account-buy-again-select-all-checkbox"

# 再来一单弹窗
buy_again_popup = "wid-popup-order-buy-again"
buy_again_page_content = "wid-account-buy-again-drawer-content"

# 再来一单页面标题和内容
buy_again_title = "wid-account-buy-again-title"
buy_again_unavailable_title = "wid-account-buy-again-unavailable-title"

# 有效商品相关元素
buy_again_product_available_item = "wid-account-buy-again-product-available-item"
buy_again_product_available_item_checkbox = "wid-account-buy-again-product-available-item-checkbox"

# 无效商品相关元素
buy_again_product_unavailable_item = "wid-account-buy-again-product-unavailable-item"
buy_again_product_unavailable_item_checkbox = "wid-account-buy-again-product-unavailable-item-checkbox"

# 有效商品内容元素
buy_again_product_item_content_title = "wid-account-buy-again-product-available-item-name"
buy_again_product_item_content_image = "wid-account-buy-again-product-available-item-image"
buy_again_product_item_content_price = "wid-account-buy-again-product-available-item-price"

# 无效商品内容元素
buy_again_product_unavailable_item_name = "wid-account-buy-again-product-unavailable-item-name"
buy_again_product_unavailable_item_price = "wid-account-buy-again-product-unavailable-item-price"
buy_again_product_unavailable_item_image = "wid-account-buy-again-product-unavailable-item-image"



