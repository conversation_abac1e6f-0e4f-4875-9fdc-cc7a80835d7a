"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_order_confirmation_ele.py
@Description    :  订单确认页面元素定义
@CreateTime     :  2025/6/8 11:31
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:31
"""
# 订单成功页弹窗元素
ele_share_popup = 'wid-order-share-popup'

# 弹窗关闭按钮元素
ele_modal_close = 'wid-modal-btn-close'

# 开始赚取积分按钮
start_earning = 'btn-order-share-start-earning'

# 弹窗中分享信息link
ele_share_terms = 'link-order-share-terms'

# 订单成功页弹窗页面标题
ele_order_popup_title = u"//p[contains(text(), 'Order confirmed')]"

# 订单成功页操作按钮
ele_order_details = 'btn-order-success-view-order'
ele_continue_shopping = 'btn-order-success-continue-shopping'


# 订单确认页面元素
ele_confirmation_title = 'wid-order-success-title'
ele_confirmation_success_delivery_icon = 'wid-order-success-delivery-icon'
ele_confirmation_delivery_window= 'wid-order-success-delivery-mode-value'
ele_confirmation_order_success_address= 'wid-order-success-address'
ele_confirmation_order_success_lable= 'wid-order-success-address-label'
ele_confirmation_order_success_value= 'wid-order-success-address-value'


