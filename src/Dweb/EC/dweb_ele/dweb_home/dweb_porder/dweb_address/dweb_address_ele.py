# PC端地址相关元素定位

# 销售组织地址定义
# 地址格式：街道，城市，州，zipcode
import json

# 销售组织地址JSON
SALES_ORG_ADDRESSES = {
    "ORG1": {
        "street": "1251 Peralta Blvd",
        "city": "Fremont",
        "state": "CA",
        "zipcode": "94536",
        "full_address": "1251 Peralta Blvd, Fremont, CA 94536"
    },
    "ORG2": {
        "street": "1020 E Florence Ave",
        "city": "Los Angeles",
        "state": "CA",
        "zipcode": "",
        "full_address": "1020 E Florence Ave, Los Angeles, CA"
    },
    "ORG3": {
        "street": "3004 Andrade Rd",
        "city": "Sunol",
        "state": "CA",
        "zipcode": "94586",
        "full_address": "3004 Andrade Rd, Sunol, CA 94586"
    },
    "ORG4": {
        "street": "18607 Bothell Way NE",
        "city": "Bothell",
        "state": "WA",
        "zipcode": "98011",
        "full_address": "18607 Bothell Way NE, Bothell, WA 98011"
    }
}

# JSON字符串格式
SALES_ORG_ADDRESSES_JSON = json.dumps(SALES_ORG_ADDRESSES, indent=4)

# 首页地址按钮
ele_home_address_button = u"//div[@id='changeZipCode']"
ele_home_zipcode = "wid-modal-zip-code"
# ########################################################################
# 首页修改您的地址pop元素
# zipcode 弹窗确认按钮
zipcode_pop_btn = "wid-btn-zipcode-btn"
# 地址卡片
address_card = "wid-address-card"
address_card_location_icon = "wid-address-card-location-icon"
address_card_name = "wid-address-card-name"
address_card_address = "wid-address-card-address"
address_card_city = "wid-address-card-city"
address_card_edit = "wid-address-card-edit"

# 查看更多按钮
zipcode_pop_more_btn = "wid-btn-more-address"
# 添加地址按钮
zipcode_pop_add_btn = "wid-btn-add-address"
# zipcode 弹窗x按钮
zipcode_pop_close_btn  = "wid-modal-btn-close"
# 新增地址按钮
ele_add_new_address_button = u"//button[text()='Add new address']"
# ########################################################################
# 新增地址表单元素
address_first_matched = u"#streetAddressList span"
# 输入姓名
address_first_name = "wid-input-first-name"
address_last_name = "wid-input-last-name"
# 输入电话
address_phone = "wid-input-phone"
# 输入街道地址
address_street = "wid-input-street"
# 输入公寓号
address_apt = "wid-input-flats"
# 输入城市
address_city = u"input[placeholder='City']"
# 输入州
address_state = "text-filed-input"
# 输入邮编
address_zipcode = "wid-input-zipcode"
# 输入备注
address_note = "wid-input-notes"
# 保存地址
address_save_button = "btn-save-address"
# 取消保存
address_cancel_button = u"button[type='button']"
# 删除按钮
address_delete_button = "btn-delete-address"

# 地址簿中的地址
ele_address_book_item = u"//div[contains(@class, 'AddressCard_addressCard')]"
ele_address_book_name = u"//div[contains(@class, 'AddressCard_addressCard')]//strong"

# 账户页面元素
ele_account_button = u"//div[@data-testid='wid-account-menu']"
ele_account_addresses = u"//a[contains(@href, '/account/addresses')]"

# 订单详情页元素
ele_order_detail_address_section = u"//div[contains(@class, 'OrderDetail_addressSection')]"
ele_order_detail_change_address = u"//span[text()='Change']"

# Checkout页面元素
ele_checkout_address_section = u"//div[@data-testid='wid-checkout-address-selector']"
ele_checkout_add_address = u"//button[text()='Add new address']"

# 删除地址
ele_address_delete_button = u"//i[contains(@class, 'iconDelete')]"
ele_address_confirm_delete = u"//button[text()='Remove']"

# 测试用地址名称
ele_test_address_name = u"//strong[text()='Test Automation']"

# PC端首页元素
home_address_selector = "wid-home-address-selector"
delivery_address_modal = "wid-delivery-address-modal"
edit_address_button = "btn-edit-address"
close_delivery_dialog = "wid-modal-btn-close"
home_main_banner = "wid-main-banner"

# 地址弹窗元素
change_address_dialog = "wid-dialog-change-zipcode"
address_list = "wid-dialog-change-zipcode-address-list"
address_list_item = "wid-dialog-change-zipcode-address-list-item"

# PC端用户账户元素
user_profile_dropdown = "wid-user-profile-dropdown"
user_settings = "wid-user-settings"
address_book_menu = "wid-address-book-menu"
address_item = "wid-address-item"
btn_edit_address = "btn-edit-address"
confirm_modal = "wid-modal-confirm"
btn_confirm_close = "btn-confirm-close"
btn_confirm_ok = "btn-confirm-ok"

# 账户页面地址簿元素
account_settings_menu_item = "wid-account-menu-list-item-settings-label"
account_address_book = "wid-account-setting-sections-me_delivery_address"
change_zipcode_content = "wid-dialog-change-zipcode-content"
change_zipcode_title = "wid-dialog-change-zipcode-title"
address_account_module = "wid-address-account-module"

# PC端购物车元素
cart_icon = "wid-mini-cart"
cart_empty = "wid-cart-empty-cart-content"
cart_preference = "wid-cart-preference"
btn_atc_plus = "btn-atc-plus"
cart_checkout_btn = "wid-cart-summary-checkout"
cart_select_dialog = "wid-cart-select-cart-dialog"
btn_select_all_carts = "btn-select-all-carts"
cart_select_checkout_btn = "wid-cart-select-checkout-btn"

# upsell弹窗元素
upsell_title = "wid-upsell-title"
upsell_continue_checkout_btn = "wid-upsell-continue-to-checkout-btn"

# 结算页地址元素
checkout_address_form_firstname_label = "wid-address-form-name-input-firstname-label"
checkout_address_info = "wid-checkout-address-info"
checkout_address_card = "wid-address-card"
checkout_add_address_btn = "wid-checkout-address-info-add-address-btn"

# PC端zipcode添加地址流程元素
zipcode_modal = "wid-modal-zip-code"
change_zipcode_dialog = "wid-dialog-change-zipcode"
add_address_btn = "btn-add-address"

# 新增地址页面元素
address_account_module = "wid-address-account-module"
address_module_title = "wid-address-account-module-title"
contact_title = "wid-address-form-contact-title"
contact_desc = "wid-address-form-contact-desc"
address_title = "wid-address-form-address-title"
address_combine = "wid-address-form-address-combine"
option_title = "wid-address-form-option-title"
option_desc = "wid-address-form-option-desc"
security_info = "wid-address-form-security"
security_text = "wid-address-form-security-text"

# 表单输入元素
firstname_input = "wid-address-form-name-input-firstname"
firstname_input_field = "wid-address-form-name-input-firstname-input"
lastname_input = "wid-address-form-name-input-lastname"
lastname_input_field = "wid-address-form-name-input-lastname-input"
phone_input = "wid-address-form-phone-input"
street_input = "wid-address-form-street-address-input"
apt_input = "wid-address-form-apt-input"
city_input = "wid-address-form-city-input"
state_input = "wid-address-form-state-input"
zipcode_input = "wid-address-form-zipcode-input"
note_textarea = "wid-address-form-option-textarea"

# 表单按钮
delete_btn = "btn-address-form-delete"
cancel_btn = "btn-address-form-cancel"
save_btn = "btn-address-form-save"

# 更新地址确认弹窗
update_address_modal = "wid-modal-update-address"
update_now_btn = "btn-update-now"
