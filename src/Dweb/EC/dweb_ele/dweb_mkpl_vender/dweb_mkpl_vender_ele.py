# ================================
# 商家关注按钮选择器
# ================================

# 关注按钮 - 基于data-testid
ele_follow_seller_btn = "btn-follow-seller"

# ================================
# 确定按钮选择器
# ================================

# 确定按钮 - 基于data-testid
ele_confirm_btn = "btn-unfollow-seller"

# ================================
# 弹窗容器选择器
# ================================

# 弹窗容器 - 基于data-testid
ele_dialog_container = "mod-unfollow-confirm-modal"

# 弹窗容器 - 基于role属性
ele_dialog_role = u"//div[@role='dialog']"

# 弹窗容器 - 基于data-state属性
ele_dialog_open = u"//div[@role='dialog' and @data-state='open']"

# 取消按钮 - 基于data-testid
ele_cancel_btn = "btn-cancel-unfollow-seller"

# 关闭按钮 - 基于data-testid
ele_close_btn = "btn-colose-unfollow-popup"

# ================================
# Email输入框选择器
# ================================

# Email输入框 - 基于autocomplete属性
ele_email_input_autocomplete = u"//input[@autocomplete='email']"

# ================================
# 密码输入框选择器
# ================================

# 密码输入框 - 基于type属性
ele_password_input_type = u"//input[@type='password']"

# 提交按钮 - 基于data-role
ele_submit_btn = u"//button[@data-role='text-filed-button']"

# ================================
# 商家Logo选择器
# ================================

# 商家Logo - 基于data-testid
ele_seller_logo = "wid-seller-logo"

# 商家名称 - 基于data-testid
ele_seller_name = "wid-seller-title"

# 商家评分 - 基于data-testid
ele_seller_rating = "wid-seller-rating"

# 联系商家入口 - 基于data-testid
ele_seller_message = "btn-send-message-to-seller"

# 商家ETA 信息  - 基于data-testid
ele_seller_eta_range = "wid-seller-reminder-estimate_range"

# 商家运费 信息  - 基于data-testid
ele_seller_shipping_info = "wid-seller-reminder-shipping"

# 商家探索 tab   - 基于data-testid
ele_seller_explore_tab = "wid-seller-tab-explore"

# 商家全部商品 tab  - 基于data-testid
ele_seller_all_products_tab = "wid-seller-tab-all"

# 商家晒单 tab  - 基于data-testid
ele_seller_review_tab = "wid-seller-tab-reviews"

# 商家评价 tab  - 基于data-testid
ele_seller_feedback = "wid-seller-tab-feedback"

# 商家运输说明 tab  - 基于data-testid
ele_seller_shipping_return = "wid-seller-tab-shipping_return"

# 商家关于 tab  - 基于data-testid
ele_seller_about = "wid-seller-tab-about"

# 商家搜索  - 基于data-testid
ele_seller_search = "wid-seller-search-bar"

# 置顶按钮  - 基于data-testid
ele_seller_back_top = "btn-back-to-top"

# 商家页悬浮购物车：
ele_float_cart_container = "wid-seller-floating-cart-info"

# 商家promotion tip
ele_float_cart_discount_tips = "wid-seller-discount-tips"

# 查看购物车：
ele_view_cart = "btn-go-checkout"

# 悬浮购物车金额：
ele_float_cart_price ="wid-seller-cart-price"

# 悬浮购物车运费：
ele_float_cart_shipping_fee = "wid-seller-cart-shipping-info"

#悬浮购物车关闭按钮：
ele_float_cart_close_button = "btn-close-seller-cart"

#悬浮购物车删除按钮：
ele_float_cart_delete_button = "btn-delete-cart-item"


