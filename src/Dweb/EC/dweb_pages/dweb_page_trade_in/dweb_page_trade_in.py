from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_home.dweb_home_ele import ele_home_page_snack, ele_home_cart
from src.api.porder import query_porder_v5
from src.common.commfunc import empty_cart
from src.common.commonui import close_popup_on_home, close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DwebTradeIn:
    """ 选择零食商品加入购物车，目前用例未用到，不推荐AI使用 """
    ele_snack_add_to_cart = u"//div[@data-role='addButton' and @title='Add to cart']"
    ele_snack_sort_dropdown = u"i[class*='SortSelect_sortIcon'][class*='icon-icon-arrow-down']"
    ele_snack_sort_from_high_to_low = u"//span[text()='Price: High to Low']"
    ele_cart_trade_in_button = u"//span[text()='Deal']"

    def __init__(self, page: Page, header):
        self.page = page
        self.header = header

    def add_snack_to_cart(self):
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)

        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.reload()
        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        self.page.locator(ele_home_page_snack).click()
        self.page.wait_for_timeout(5000)
        # self.page.wait_for_load_state("networkidle")
        self.page.locator(self.ele_snack_sort_dropdown).hover()
        self.page.wait_for_selector(self.ele_snack_sort_from_high_to_low)
        self.page.locator(self.ele_snack_sort_from_high_to_low).click()
        # 必须加入等待，否则页面刷新不及时
        self.page.wait_for_timeout(6000)
        all_snack_items = self.page.query_selector_all(self.ele_snack_add_to_cart)
        assert all_snack_items, f"零食没有可以加购的商品，all_new_items={all_snack_items}"
        for index, item in enumerate(all_snack_items):
            # item.evaluate('(item) => item.click()')
            item.click()
            self.page.wait_for_timeout(1000)
            if index == 5:
                break
        log.info("加购零食商品完成")
        preorder = query_porder_v5(self.header)
        # if float(preorder['object']['final_amount'])
        self.page.locator(ele_home_cart).click()
        self.page.wait_for_selector(self.ele_cart_trade_in_button)
        assert self.page.locator(self.ele_cart_trade_in_button).is_visible()
