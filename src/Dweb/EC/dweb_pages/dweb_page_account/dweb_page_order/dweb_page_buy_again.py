
from playwright.sync_api import Page

from src.Dweb.EC.dweb_ele.dweb_account.dweb_order import dweb_buy_again_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebBuyAgainPage(DWebCommonPage):
    """
    这个类主要封装了PC 再来一单页面的操作
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):

        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
        self.page.goto(TEST_URL + page_url)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        # 获取顶部语言
        self.home_page_switch_lang(lang="English")
        # 获取顶部zipocde
        self.home_page_switch_zipcode(zipcode)


    def verify_select_all_state(self,expected_state):
        select_class_attribute = self.page.locator(dweb_buy_again_ele.buy_again_select_all_ele).get_attribute("class")
        if expected_state:
            assert "buy-again_selected" in select_class_attribute, "Missing 'selected' class"
        else:
            assert "buy-again_disabledSelect" in select_class_attribute, "Missing 'disabledSelect' class"

    def verify_product_item_state(self,expected_state):
        # 获取第一个商品的元素
        product_item_class_attribute = self.page.locator(dweb_buy_again_ele.buy_again_product_card_ele).first.get_attribute("class")
        if expected_state:
            assert " buy-again_selected" in product_item_class_attribute, "Missing 'selected' class"
        else:
            assert "buy-again_selected" not in product_item_class_attribute, "Missing 'disabledSelect' class"


    def verify_add_cart_btn_state(self,expected_state):
        add_cart_class_attribute = self.page.locator(dweb_buy_again_ele.buy_again_add_cart_button_ele).get_attribute("class")
        if expected_state:
            assert "disabled" not in add_cart_class_attribute, "Missing 'selected' class"
        else:
            assert "disabled" in add_cart_class_attribute, "Missing 'disabledSelect' class"

