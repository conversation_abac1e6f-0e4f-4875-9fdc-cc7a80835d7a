# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/6/17
@Software       :  PyCharm
------------------------------------
"""
from playwright.sync_api import Page
from src.config.weee.log_help import log
class DWebTimeBannerPage:
    """Time Banner and FTU Popup handling """

    def __init__(self, page: Page, header, browser_context=None):
        self.page = page
        self.header = header
        self.browser_context = browser_context
    def p_verify_ftu_visibility(self):
        """验证 ftu popupr 是否可见"""
        log.info("Ftu Popup 可见")
        return self.page.get_by_test_id("wid-ftu-dialog-content").is_visible()
    def p_close_ftu(self):
        """关闭 Ftu Popup"""
        self.page.get_by_test_id("wid-modal-btn-close").click()
        self.page.wait_for_timeout(5000)  # 等待关闭动画完成
        assert not self.page.get_by_test_id("wid-ftu-dialog-content").is_visible()
        log.info("关闭 Ftu Popup 成功")
    def p_verify_time_banner_visibility(self):
        """验证 Time Banner 是否可见"""
        is_visible = self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        if is_visible:
            log.info("Time Banner 可见")
        else:
            log.info("Time Banner 不可见")
            raise AssertionError("Time Banner 不可见")
        return is_visible
    def p_click_time_banner(self):
        """点击 Time Banner"""
        self.page.get_by_test_id("wid-top-promotion-bar").click()
        self.page.wait_for_timeout(5000)
        log.info("点击 Time Banner 成功")

    def p_close_time_banner(self):
        """关闭 Time Banner"""
        self.page.get_by_test_id("wid-top-promotion-bar-close-cta-icon").click()
        self.page.wait_for_timeout(10000)  # 等待关闭动画完成
        assert not self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        log.info("关闭 Time Banner 成功")

    def p_verify_time_banner_closed(self):
        """验证 Time Banner 是否已关闭"""
        assert not self.page.get_by_test_id("wid-top-promotion-bar").is_visible()
        log.info("验证 Time Banner 已关闭成功")
    def p_click_email_signup(self):
        "点击邮箱注册"
        self.page.get_by_test_id("wid-ftu-login-method-dialog-login-email-text").click()
        self.page.wait_for_timeout(5000)
        log.info("点击邮箱输入框成功")
