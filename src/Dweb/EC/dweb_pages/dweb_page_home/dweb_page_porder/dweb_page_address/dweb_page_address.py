from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebPageAddress(DWebCommonPage):
    """
    这个类主要封装了PC web端对地址页面的操作
    """
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入地址页面
        2. 如果zipcode不是98011，则切换zipcode
        3. 调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        if page_url:
            self.page.goto(TEST_URL + page_url)
        else:
            self.page.goto(TEST_URL)
            self.page.wait_for_timeout(3000)
            close_advertise_on_home(self.page)

    def add_new_address(self):
        """
        添加新地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 点击新增地址按钮
        self.page.get_by_test_id("wid-btn-add-address").click()
        self.page.wait_for_timeout(3000)

        # 填写地址表单
        self._fill_address_form()
        log.info("添加新地址成功")

    def edit_address(self, index=0):
        """
        编辑地址
        
        Args:
            index (int): 要编辑的地址索引，默认为第一个地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 获取所有地址
        address_items = self.page.get_by_test_id("wid-address-item").all()
        if not address_items or index >= len(address_items):
            log.warning(f"没有找到索引为{index}的地址")
            return False

        # 点击编辑按钮
        address_items[index].get_by_test_id("wid-address-edit").click()
        self.page.wait_for_timeout(2000)

        # 修改地址表单
        self._fill_address_form(is_edit=True)
        log.info(f"编辑第{index+1}个地址成功")
        return True

    def delete_address(self, index=0):
        """
        删除地址
        
        Args:
            index (int): 要删除的地址索引，默认为第一个地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 获取所有地址
        address_items = self.page.get_by_test_id("wid-address-item").all()
        if not address_items or index >= len(address_items):
            log.warning(f"没有找到索引为{index}的地址")
            return False

        # 点击编辑按钮
        address_items[index].get_by_test_id("wid-address-edit").click()
        self.page.wait_for_timeout(2000)

        # 点击删除按钮
        self.page.locator("//button[text()='Delete the address']").click()
        self.page.wait_for_timeout(2000)
        log.info(f"删除第{index+1}个地址成功")
        return True

    def select_address(self, index=0):
        """
        选择地址
        
        Args:
            index (int): 要选择的地址索引，默认为第一个地址
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 获取所有地址
        address_items = self.page.get_by_test_id("wid-address-item").all()
        if not address_items or index >= len(address_items):
            log.warning(f"没有找到索引为{index}的地址")
            return False

        # 点击选择按钮
        address_items[index].click()
        self.page.wait_for_timeout(2000)
        log.info(f"选择第{index+1}个地址成功")
        return True

    def verify_address_list(self):
        """
        验证地址列表
        
        Returns:
            dict: 包含地址列表信息的字典
        """
        # 点击首页地址按钮
        self.page.get_by_test_id("wid-modal-zip-code").click()
        self.page.wait_for_timeout(2000)

        # 获取所有地址
        address_items = self.page.get_by_test_id("wid-address-item").all()
        log.info(f"找到{len(address_items)}个地址")

        addresses = []
        for i, item in enumerate(address_items):
            # 获取地址名称
            name = item.get_by_test_id("wid-address-name")
            name_text = name.text_content() if name.is_visible() else ""

            # 获取地址详情
            detail = item.get_by_test_id("wid-address-detail")
            detail_text = detail.text_content() if detail.is_visible() else ""

            addresses.append({
                "name": name_text,
                "detail": detail_text
            })
            log.info(f"地址{i+1}: {name_text}, {detail_text}")

        return {
            "count": len(address_items),
            "addresses": addresses
        }

    def _fill_address_form(self, is_edit=False):
        """
        填写地址表单
        
        Args:
            is_edit (bool): 是否是编辑模式
        """
        # 输入姓名
        self.page.locator("//label[text()='First name']//following-sibling::input").fill("Bill")
        self.page.locator("//label[text()='Last name']//following-sibling::input").fill("Smitch")

        # 输入电话
        self.page.locator("//label[text()='Phone number']//following-sibling::input").fill("5555456985")

        # 输入街道地址
        self.page.locator("#rc_select_0").fill("15006 104th Ave NE")
        self.page.locator("//label[text()='City']//following-sibling::input").fill("Bothell")
        self.page.locator("#rc_select_1").fill("WA")
        self.page.locator("//div[text()='WA']").click()
        self.page.locator("//label[text()='Zip code']//following-sibling::input").fill("98011")

        # 输入备注
        self.page.locator("//textarea").fill("PC UI自动化测试")
        
        # 点击保存按钮
        self.page.locator("//button[text()='Save']").click()
        self.page.wait_for_timeout(3000)
        
        # 处理可能出现的"Not now"按钮
        if self.page.locator("//button[text()='Not now']").all():
            self.page.locator("//button[text()='Not now']").click()
            self.page.wait_for_timeout(2000)