from faker import Faker
from playwright._impl._element_handle import <PERSON><PERSON><PERSON><PERSON><PERSON>
from playwright.sync_api import Page, Locator

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commfunc import empty_cart
from src.common.commonui import close_advertise_on_home, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class LoginAndSignin(DWebCommonPage):
    """
    该类描述了2个流程：
    1. 先登陆，然后从首页加购商品，进入购物车结算并生成订单
    2. 先从首页不同合集加购商品，进入购物车，点击checkout按钮，然后登陆，生成订单
    """
    def __init__(self, page: Page, header):
        super().__init__(page, header)

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)
        close_advertise_on_home(self.page)
        if self.page.locator("div[id='onetrust-close-btn-container']").all():
            self.page.locator("div[id='onetrust-close-btn-container']").click()

    def login_and_place_order(self):
        """
        该方法包含以下功能：
        1. 从首页点击登陆，进入登陆流程
        2. 登陆成功后，从首页加购商品
        3. 进入购物车，checkout，生成订单
        """

        self.login()
        self.add_product_on_home()

    def place_order_then_login(self):
        """
        该方法包含以下功能：
        1. 从首页不同合集加购商品
        2. 进入购物车，checkout
        3. 登陆成功后，生成订单
        """
        if self.page.locator("//div[@aria-label='first user login modal']").all():
            self.page.locator("//div[@class='ant-modal-body']/i").click()

        # 1. scroll to the element "featured reviews"
        scroll_one_page_until(self.page, "div[data-testid='mod-reviews-carousel-Featured-Reviews']")

        # 2. add each collections' products to cart
        for coll_name in ["mod-item-carousel-Trending-Store-Favorites", "mod-item-carousel-Editor's-Pick", "mod-lighting-deal-carousel-Lightning-Deals", r"mod-item-carousel-New-Arrivals", "mod-item-carousel-Bestsellers",
                          "mod-tab-item-carousel-Global+-Top-charts", "mod-tab-item-carousel-Fresh-Daily", "mod-item-carousel-Everyday-deals", "mod-item-carousel-Recommended-For-You"]:
            self._add_products_to_cart_for_all_collections(coll_name)

        # 3. 点击购物车并校验
        self.page.get_by_test_id("wid-mini-cart").click()
        self._cart_page_check()
        self._checkout_with_login()
        self.place_order()

    def login(self):
        """
        该方法包含以下功能：
        1. 从首页点击登陆，进入登陆流程，使用邮箱和密码登陆
        2. 登陆成功后，校验登陆成功
        """
        if self.page.locator("//div[@aria-label='first user login modal']").is_visible():
            self.page.locator("//span[text()='Continue with email']").click()
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your email here").fill("<EMAIL>")
            self.page.wait_for_timeout(1200)
            self.page.locator("//button[@data-role='text-filed-button']").click()
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your password").fill("A1234567")
            self.page.wait_for_timeout(1200)
            self.page.locator("//span[text()='Log in']").click()
            log.info("==== 登陆成功 ====")
            self.page.wait_for_timeout(10000)
            if len(self.page.locator("//img[@Alt='Close']").all()) == 2:
                # self.page.locator("//div[@class='w-full h-full']/img[@Alt='Close']").click()
                self.page.locator("//img[@Alt='Close']").all()[1].click()
            if len(self.page.locator("//img[@Alt='Close']").all()) == 1:
                # self.page.locator("//div[@class='w-full h-full']/img[@Alt='Close']").click()
                self.page.locator("//img[@Alt='Close']").all()[0].click()

            assert self.page.locator("//div[text()='pcautotest']").is_visible()
            self.page.wait_for_timeout(2000)

            if self.page.locator("//div[@id='changeZipCode']/div[position()=1]").text_content() != '98011':
                self.home_page_switch_zipcode()

        else:
            self.page.locator("//div[text()='Log in']").click()
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your email").fill("<EMAIL>")
            self.page.wait_for_timeout(1200)
            self.page.locator("//button[@data-role='text-filed-button']").click()
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your password").fill("A1234567")
            self.page.wait_for_timeout(1200)
            self.page.locator("//span[text()='Log in']").click()
            log.info("==== 登陆成功 ====")
            self.page.wait_for_timeout(10000)
            if self.page.locator("//img[@Alt='Close']").all():
                self.page.locator("//img[@Alt='Close']").click()
            self.page.locator("//div[text()='pcautotest']").is_visible()
            self.page.wait_for_timeout(2000)

            if self.page.locator("//div[@id='changeZipCode']/div[position()=1]").text_content() != '98011':
                self.home_page_switch_zipcode()

    def add_product_on_home(self):
        """
        该方法包含以下功能：
        1. 从首页不同合集加购商品
        2. 进入购物车，checkout
        3. 登陆成功后，生成订单
        """
        # 1. scroll to the element "featured reviews"
        scroll_one_page_until(self.page, "div[data-testid='mod-reviews-carousel-Featured-Reviews']")

        # 2. add each collections' products to cart
        for coll_name in ["mod-item-carousel-Editor's-Pick", "mod-item-carousel-New-Arrivals", "mod-item-carousel-Bestsellers", r"mod-tab-item-carousel-Global+-Top-charts", "mod-tab-item-carousel-Fresh-Daily",
                          "mod-item-carousel-Everyday-deals", "mod-item-carousel-Recommended-For-You"]:
            self._add_products_to_cart_for_all_collections(coll_name)

        # 6. click cart button to navigate to cart page
        self.page.get_by_test_id("wid-mini-cart").click()

        # 7. 购物车校验
        self._cart_page_check()

        # 8. checkout校验
        self._checkout()

        # 9. place order
        self.place_order()

    def _add_products_to_cart_for_all_collections(self, collections_name):
        """
        该方法包含以下功能：
        1. 根据传入的collections_name, 滑动到合集，点击加购按钮，加购5个商品
        """
        # 3. goto the section collection presents
        if self.page.get_by_test_id(collections_name).all():
            self.page.get_by_test_id(collections_name).scroll_into_view_if_needed()
            # 4. find add_to_cart buttons belongs to the collection
            add_to_cart_list = self.page.get_by_test_id(collections_name).get_by_test_id("wid-atc-plus").element_handles()
            product_card_list = self.page.get_by_test_id(collections_name).get_by_test_id("wid-product-card-container").all()
            # 5. click add_to_cart button 5 times for each collection
            for index, item in enumerate(product_card_list):
                if item.locator("//span[text()='Pre-sale']").all():
                    continue
                if item.get_by_test_id("btn-atc-plus").all():
                    item.get_by_test_id("btn-atc-plus").click()
                    self.page.wait_for_timeout(1000)
                if index == 4:
                    break
            self.page.wait_for_timeout(3000)

    def _cart_page_check(self):
        """
        该方法包含以下功能：
        1. 进入购物车页面，校验购物车的UI样式
        2. 获取不同类型的购物车，并对每种购物车中的商品进行校验，包含商品名称，价格，缩略图的校验
        """
        self.page.wait_for_timeout(10000)
        self.page.get_by_test_id("btn-main-search").hover()
        self.cart_page_common_check()
        assert self.page.get_by_test_id("wid-cart-summary-normal-0").is_visible()
        # 1. 获取所有的cart
        all_cart_locators = self.page.locator('#cart-main [data-testid^="wid-cart-summary"]').all()
        for index, cart in enumerate(all_cart_locators):
            cart.scroll_into_view_if_needed(timeout=2000)
            if index > 0:
                self.page.evaluate('window.scrollBy(0, 700)')
            cart_goods_cards = cart.get_by_test_id("wid-cart-section-goods").all()
            self.page.wait_for_timeout(2000)
            for card in cart_goods_cards:
                if card.locator("//span[text()='Gift']").all():
                    continue
                # 断言缩略图可显示
                assert card.locator("div[data-component='CroppedImage']").is_visible(), f"card={card}"
                log.info("products name===>" + card.locator("span[class='align-middle']").text_content())
                assert card.locator("span[class='align-middle']").text_content(), f"card={card}"
                log.info("products price===>" + card.locator("span[class^='GoodsInCart_priceUsed']").text_content())
                assert "$" in card.locator("span[class^='GoodsInCart_priceUsed']").text_content(), f"card={card}"
                # if card.get_by_test_id("btn-atc-plus").all():
                #     card.get_by_test_id("btn-atc-plus").click()
                #     self.page.wait_for_timeout(500)
                # if card.get_by_test_id("btn-atc-minus").all():
                #     card.get_by_test_id("btn-atc-minus").click()
                #     self.page.wait_for_timeout(500)

        # 2. 校验右边的summary

    def _checkout(self):
        """
        该方法包含以下功能：
        1. 点击购物车的checkout按钮，进入checkout页面
        """
        self.page.get_by_test_id("wid-cart-summary-checkout").click()
        # select all carts to checkout
        if self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").all():
            self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").click()
            self.page.wait_for_selector(selector='button[data-testid="wid-cart-select-modal-checkout-btn"]', state="attached").is_enabled()
            self.page.get_by_test_id("wid-cart-select-modal-checkout-btn").click()
            self.page.wait_for_timeout(10000)

        self.page.wait_for_timeout(3000)

    def _checkout_with_login(self):
        """
        该方法包含以下功能：
        1. 点击购物车的checkout按钮，进入checkout页面, 此时由于未登陆，需要先登陆
        2. 登陆成功后，点击checkout按钮，进入checkout页面
        """
        self.page.get_by_test_id("wid-cart-summary-checkout").click()
        self.page.wait_for_selector("div[class='ant-modal-content']")
        if self.page.locator("div[class='ant-modal-content']").is_visible():
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your email").fill("<EMAIL>")
            self.page.wait_for_timeout(1200)
            self.page.locator("//button[@data-role='text-filed-button']").click()
            self.page.wait_for_timeout(1200)
            self.page.get_by_placeholder("Enter your password").fill("A1234567")
            self.page.wait_for_timeout(1200)
            self.page.locator("//span[text()='Log in']").click()
            print("==== 登陆成功 ====")
            self.page.wait_for_timeout(10000)
            if len(self.page.locator("//img[@Alt='Close']").all()) == 2:
                # self.page.locator("//div[@class='w-full h-full']/img[@Alt='Close']").click()
                self.page.locator("//img[@Alt='Close']").all()[1].click()
            self.page.locator("//div[text()='pcautotest']").is_visible()
            self.page.wait_for_timeout(2000)
            self.page.get_by_test_id("wid-cart-summary-checkout").click()
            if self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").all():
                self.page.get_by_test_id("wid-cart-select-modal-select-all-btn").click()
                self.page.wait_for_selector(selector='button[data-testid="wid-cart-select-modal-checkout-btn"]', state="attached").is_enabled()
                self.page.get_by_test_id("wid-cart-select-modal-checkout-btn").click()
                self.page.wait_for_timeout(10000)
            self.page.wait_for_timeout(3000)

    def place_order(self):
        """
        该方法包含以下功能：
        1. 点击place order按钮，产生订单
        """
        self.page.wait_for_timeout(3000)
        if self.page.get_by_test_id("wid-upsell-continue-to-checkout").all():
            self.page.get_by_test_id("wid-upsell-continue-to-checkout").click()
            self.page.wait_for_timeout(3000)
        self.page.get_by_test_id("wid-checkout-btn").all()[0].click()
        log.info("checkout button length" + str(len(self.page.get_by_test_id("wid-checkout-btn").all())))
        self.page.wait_for_timeout(3000)
        if self.page.get_by_test_id("wid-checkout-full-name-modal-confirm-checkbox").all():
            self.page.get_by_test_id("wid-checkout-full-name-modal-confirm-checkbox").click()
            self.page.wait_for_timeout(1500)
            self.page.get_by_test_id("wid-checkout-full-name-modal-btn-place").click()
            log.info("reach this condition")
        else:
            log.info("reach else condition")


    def signin(self):
        """
        从首页点击注册按钮，进入注册流程
        """
        if self.page.locator("//div[@aria-label='first user login modal']").is_visible():
            self.page.locator("//span[text()='Continue with email']").click()
            self.page.wait_for_timeout(1200)
            email = Faker(locale='en').name().replace(" ", "_")
            self.page.get_by_placeholder("Enter your email here").fill(f"auto_{email}@qq.com")
            self.page.wait_for_timeout(1200)
            self.page.locator("//button[@data-role='text-filed-button']").click()
            self.page.wait_for_timeout(4000)
            if self.page.locator("//div[text()='Where will you shop today?']").is_visible():
                self.page.locator("//span[text()='Chinese']").click()
            self.page.wait_for_timeout(3000)
            if self.page.locator("//span[text()='See availability']").is_visible():
                self.page.locator("//span[text()='See availability']").click()
            self.page.wait_for_timeout(3000)



