from playwright.sync_api import Page

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebOrdersPage(DWebCommonPage):
    """
    这个类主要封装了PC web端对订单页面的操作
    """
    def __init__(self, page: Page, header, browser_context):
        """
        构造方法
        """
        super().__init__(page, header)
        self.bc = browser_context
    
    def navigate_to_orders_page(self):
        """
        从任意页面导航到订单页面
        """
        # 先进入首页
        self.page.goto(TEST_URL + "/account/my_orders")
        self.page.wait_for_timeout(5000)
        close_advertise_on_home(self.page)
        # 验证成功进入订单页面

        log.info("成功导航到订单页面")
    
    def switch_to_tab(self, tab_name):
        """
        切换到指定的订单状态标签
        
        参数:
            tab_name: 标签名称，可选值为 "All", "Pending", "Unshipped", "Shipped", "To Review", "Cancelled"
        """
        # 定义标签名称与测试ID的映射
        # 目前开发未就此页面添加id
        tab_test_ids = {
            "All": "//li[text()='All']",
            "Pending": "//li[text()='Pending']",
            "Unshipped": "//li[text()='Unshipped']",
            "Shipped": "//li[text()='Shipped']",
            "Review": "//li[text()='Review']",
            "Cancelled": "//li[text()='Cancelled']"
        }
        
        # 获取对应的测试ID
        test_id = tab_test_ids.get(tab_name)
        if not test_id:
            raise ValueError(f"无效的标签名称: {tab_name}")
        
        # 点击标签
        self.page.locator(test_id).click()
        self.page.wait_for_timeout(2000)

        if tab_name == "Cancelled":
            assert (self.page.locator("div[class*='OrderCard_orderCard']").all() and
                    self.page.locator("//div[text()='Canceled']").all() and
                    self.page.locator("//a[text()='Details']").all()), f"{tab_name} 标签未被选中"

        # 验证标签被选中
        assert self.page.locator("//div[text()='No recent orders']").all() or self.page.locator("div[class*='OrderCard_orderCard']").all(), f"{tab_name} 标签未被选中"
        log.info(f"成功切换到 {tab_name} 标签")
    
    def get_orders_count(self):
        """
        获取当前标签下的订单数量
        
        返回:
            int: 订单数量
        """
        orders = self.page.get_by_test_id("wid-order-item").all()
        return len(orders)
    
    def has_empty_state(self):
        """
        检查当前标签是否显示空状态
        
        返回:
            bool: 是否显示空状态
        """
        empty_states = self.page.get_by_test_id("wid-orders-empty-state").all()
        return len(empty_states) > 0 and empty_states[0].is_visible()
    
    def get_first_order_details(self):
        """
        获取第一个订单的详细信息
        
        返回:
            dict: 包含订单号、日期、状态等信息的字典
        """
        orders = self.page.get_by_test_id("wid-order-item").all()
        if not orders:
            return None
        
        first_order = orders[0]
        return {
            "order_number": first_order.get_by_test_id("wid-order-number").text_content(),
            "order_date": first_order.get_by_test_id("wid-order-date").text_content(),
            "order_status": first_order.get_by_test_id("wid-order-status").text_content(),
            "order_total": first_order.get_by_test_id("wid-order-total").text_content() if first_order.get_by_test_id("wid-order-total").all() else None
        }
    
    def click_first_order(self):
        """
        点击第一个订单，进入订单详情页
        
        返回:
            bool: 是否成功点击订单
        """
        orders = self.page.get_by_test_id("wid-order-item").all()
        if not orders:
            log.warning("没有可点击的订单")
            return False
        
        # 记录订单号，用于验证
        order_number = orders[0].get_by_test_id("wid-order-number").text_content()
        
        # 点击订单
        orders[0].click()
        self.page.wait_for_timeout(3000)
        
        # 验证是否进入订单详情页
        assert self.page.get_by_test_id("wid-order-detail-page").is_visible(), "未成功进入订单详情页"
        assert order_number in self.page.get_by_test_id("wid-order-detail-number").text_content(), "订单详情页显示的订单号不匹配"
        
        log.info(f"成功进入订单 {order_number} 的详情页")
        return True
