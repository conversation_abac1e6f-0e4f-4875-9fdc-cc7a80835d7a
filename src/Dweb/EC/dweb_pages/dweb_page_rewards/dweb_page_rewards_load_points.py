from playwright.sync_api import Page, TimeoutError
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_rewards import dweb_rewards_ele
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class RewardsLoadPointsPage(DWebCommonPage):
    """积分升级页面操作类"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "account/my_rewards"):
        """
        初始化积分升级页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: rewards页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入rewards页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入rewards页面: {TEST_URL}/{page_url}")

    def click_upgrade_now(self):
        """
        点击Upgrade now按钮
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_upgrade_now).click()
        log.info("成功点击Upgrade now按钮")

    def select_gold_upgrade(self):
        """
        选择黄金升级模块
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_gold_upgrade).click()
        log.info("成功选择黄金升级模块")

    def select_payment_method(self):
        """
        点击支付方式选择框
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_payment_method).click()
        log.info("成功点击支付方式选择框")

    def select_paypal(self):
        """
        选择PayPal支付方式
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_paypal).click()
        log.info("成功选择PayPal支付方式")

    def click_load_and_upgrade(self):
        """
        点击Load & upgrade now按钮
        """
        self.FE.ele(dweb_rewards_ele.ele_rewards_load_upgrade_now).click()
        log.info("成功点击Load & upgrade now按钮")

   