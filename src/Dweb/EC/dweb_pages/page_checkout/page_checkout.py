"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   page_checkout.py
@Description    :  
@CreateTime     :  2025/3/10 14:17
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/10 14:17
"""
import allure
from playwright.async_api import Page
from playwright.sync_api import expect

from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class CheckoutPage(DWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011"):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入结算页
        self.page.goto(TEST_URL + "/order/checkout?cart_domain=grocery")
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(home_elements.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        # self.page.wait_for_timeout(10000)
        # close_advertise_on_home(self.page)

    def check_checkout_free_shipping_banner_ui(self):
        # 定位不了这种会消失的元素
        with allure.step("验证凑单banner"):
            try:
                banner = self.page.get_by_test_id('wid-checkout-free-shipping-banner')
                expect(banner).to_be_visible()
                # 验证banner文案包含凑单金额信息
                banner_text = banner.text_content()
                assert "$35" in banner_text, "凑单banner金额不正确"
                log.info("凑单banner验证成功")
            except Exception as e:
                log.error(f"验证凑单banner失败: {str(e)}")
                raise
