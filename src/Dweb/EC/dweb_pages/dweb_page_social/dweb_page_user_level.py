from playwright.sync_api import Page

from src.common.commfunc import empty_cart
from src.common.commonui import close_popup_on_home, close_advertise_on_home
from src.config.base_config import TEST_URL



class BuyPoints:
    """ 购买积分 """
    ele_account = u"//div[contains(@class, 'accountMenu_name__')]"
    ele_update_prompt = u"(//div[@role='button' and text()='立即升级'])[1]"
    ele_150_point = u"//div[text()=150]"
    ele_payment = u"//div[@role='payment-method-box']"
    ele_pay_paypal = u"//div[@data-name='P']"
    ele_recharge_and_update = u"//button[text()='充值并升级']"

    # paypal支付页面元素
    ele_email_next = u"//button[text()='Next']"
    ele_finish_shopping = u"//button[text()='完成购物']"



    def __init__(self, page: Page, header):
        self.page = page
        self.header = header

    def buy_150_points(self):
        self.page.goto(TEST_URL)
        self.page.wait_for_timeout(10000)

        close_popup_on_home(self.page)
        close_advertise_on_home(self.page)

        self.page.locator(self.ele_account).hover()
        self.page.wait_for_selector(self.ele_update_prompt, timeout=5000).click()
        self.page.wait_for_selector(self.ele_150_point, timeout=5000).click()
        self.page.wait_for_selector(self.ele_payment, timeout=5000).click()
        self.page.wait_for_selector(self.ele_pay_paypal, timeout=5000).click()
        self.page.wait_for_selector(self.ele_recharge_and_update, timeout=5000).click()

        # 进入paypal支付页面
        self.page.wait_for_selector('#email', timeout=5000).fill('<EMAIL>')
        self.page.wait_for_selector(self.ele_email_next, timeout=5000).click()
        self.page.wait_for_selector('#password', timeout=5000).fill('********')
        self.page.wait_for_selector("#btnLogin", timeout=5000).click()
        self.page.wait_for_selector(self.ele_finish_shopping, timeout=5000).click()

