"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_order_confirmation.py
@Description    :  
@CreateTime     :  2025/6/8 11:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:52
"""
from playwright.sync_api import Page, expect

from src.Dweb.EC.dweb_ele import dweb_order_confirmation
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_ele.dweb_order_confirmation import dweb_order_confirmation_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebOrderComfirmationPage(DWebCommonPage):
    """订单确认页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入订单成功页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入订单成功页
        self.page.goto(TEST_URL + page_url)
        # # 获取顶部语言
        # if not self.page.get_by_test_id("wid-language").locator(
        #         "//span[text()='English'and contains(@class,'Header')]").all():
        #     # 切换为英文
        #     pass
        # # 获取顶部zipocde
        # page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        # self.page.wait_for_timeout(5000)
        # close_advertise_on_home(self.page)

    def verify_order_confirmation_title(self):
        """验证订单确认页标题"""
        title = self.FE.ele(dweb_order_confirmation_ele.ele_order_confirmation_title)
        assert title.is_visible(), "订单确认页标题不可见"
        title_text = title.text_content()
        log.info(f"订单确认页标题: {title_text}")
        return title_text

    def start_earning(self):
        """
        点击开始赚取积分按钮，打开分享弹窗
        """
        # 点击开始赚取积分按钮
        start_button = self.FE.ele(dweb_order_confirmation_ele.start_earning)
        assert start_button.is_visible(), "开始赚取积分按钮不可见"
        start_button.click()
        self.page.wait_for_timeout(2000)
        
        # 验证弹窗已打开
        modal = self.page.locator("//div[contains(@class, 'ant-modal-content')]").first
        assert modal.is_visible(), "分享弹窗未打开"
        log.info("分享弹窗已打开")
        return modal



    def verify_order_confirmation_basic_elements(self):
        """
        验证订单成功页的基本元素和功能
        
        验证点:
        1. 小车元素存在
        2. 顶部标题存在
        3. 点击订单详情按钮能跳到订单详情页并且返回
        4. 点击继续购物按钮能跳到首页去
        
        Returns:
            dict: 包含验证结果的字典
        """
        p = self.page
        result = {
            "success": True,
            "details": {}
        }
        
        try:
            # 1. 验证小车元素存在
            cart_icon = p.get_by_test_id(dweb_order_confirmation_ele.ele_confirmation_success_delivery_icon)
            cart_visible = cart_icon.is_visible(timeout=3000)
            result["details"]["cart_icon_visible"] = cart_visible
            
            if not cart_visible:
                log.warning("订单成功页小车元素不可见")
                result["success"] = False
            else:
                log.info("订单成功页小车元素可见")
            
            # 2. 验证顶部标题存在
            title = p.get_by_test_id(dweb_order_confirmation_ele.ele_confirmation_title)
            title_visible = title.is_visible(timeout=3000)
            result["details"]["title_visible"] = title_visible
            
            if title_visible:
                title_text = title.text_content()
                result["details"]["title_text"] = title_text
                log.info(f"订单成功页标题: {title_text}")
            else:
                log.warning("订单成功页标题不可见")
                result["success"] = False
            
            # 3. 验证点击订单详情按钮能跳到订单详情页并且返回
            order_details_btn = p.get_by_test_id(dweb_order_confirmation_ele.ele_order_details)
            if order_details_btn.is_visible(timeout=3000):
                # 记录当前URL
                current_url = p.url
                
                # 点击订单详情按钮
                log.info("点击订单详情按钮")
                order_details_btn.click()
                p.wait_for_timeout(2000)
                
                # 验证是否跳转到订单详情页
                new_url = p.url
                result["details"]["order_details_navigation"] = {
                    "clicked": True,
                    "original_url": current_url,
                    "new_url": new_url,
                    "url_changed": current_url != new_url
                }
                
                # 验证是否进入订单详情页（通过URL检查而不是test-id）
                is_detail_page = "/order/detail/" in new_url
                result["details"]["order_details_navigation"]["page_visible"] = is_detail_page
                
                if is_detail_page:
                    log.info("成功跳转到订单详情页")
                else:
                    log.warning(f"URL不是订单详情页: {new_url}")
                    result["success"] = False
                
                # 返回订单成功页
                log.info("返回订单成功页")
                p.go_back()
                p.wait_for_timeout(3000)
                # 验证是否返回订单成功页
                back_url = p.url
                result["details"]["order_details_navigation"]["back_url"] = back_url
                result["details"]["order_details_navigation"]["back_success"] = back_url == current_url
                
                if back_url == current_url:
                    log.info("成功返回订单成功页")
                    p.get_by_test_id(dweb_order_confirmation_ele.ele_modal_close).click()
                    p.wait_for_timeout(2000)
                else:
                    log.warning(f"返回后URL不匹配，期望: {current_url}, 实际: {back_url}")
                    result["success"] = False
            else:
                log.warning("订单详情按钮不可见")
                result["details"]["order_details_navigation"] = {"clicked": False}
                result["success"] = False
            
            # 4. 验证点击继续购物按钮能跳到首页
            continue_shopping_btn = p.get_by_test_id(dweb_order_confirmation_ele.ele_continue_shopping)
            if continue_shopping_btn.is_visible(timeout=3000):
                # 记录当前URL
                current_url = p.url
                
                # 点击继续购物按钮
                log.info("点击继续购物按钮")
                continue_shopping_btn.click()
                p.wait_for_timeout(3000)
                
                # 验证是否跳转到首页
                new_url = p.url
                result["details"]["continue_shopping_navigation"] = {
                    "clicked": True,
                    "original_url": current_url,
                    "new_url": new_url,
                    "url_changed": current_url != new_url
                }
                
                # 验证首页元素
                home_banner = p.get_by_test_id("btn-main-banner-img-0")
                home_visible = home_banner.is_visible(timeout=5000)
                result["details"]["continue_shopping_navigation"]["home_page_visible"] = home_visible
                
                if home_visible:
                    log.info("成功跳转到首页")
                else:
                    log.warning("首页元素未找到，但URL已变更")
                    if "/" != new_url and "/en" != new_url and not new_url.endswith("/en/"):
                        log.warning(f"URL可能不是首页: {new_url}")
                        result["success"] = False
            else:
                log.warning("继续购物按钮不可见")
                result["details"]["continue_shopping_navigation"] = {"clicked": False}
                result["success"] = False
        
        except Exception as e:
            result["success"] = False
            result["details"]["error"] = str(e)
            import traceback
            log.error(f"验证订单成功页基本元素时出错: {str(e)}")
            log.error(traceback.format_exc())
        
        return result

    def verify_order_confirmation_popup(self):
        """
        验证订单成功页弹窗元素和功能
        
        验证点:
        1. 弹窗存在并关闭
        2. 顶部标题存在
        3. 点击分享按钮验证分享链接格式正确
        4. 点击Learn More链接能跳转到帮助页面(/help/detail/1720)并返回
        5. 点击弹窗关闭按钮
        
        Returns:
            dict: 包含验证结果的字典
        """
        p = self.page
        result = {
            "success": True,
            "details": {}
        }
        
        try:
            # 检查弹窗是否存在
            popup = p.get_by_test_id(dweb_order_confirmation_ele.ele_share_popup)
            popup_visible = popup.is_visible(timeout=5000)
            
            if not popup_visible:
                log.warning("订单成功页弹窗不可见")
                result["success"] = False
                return result
            
            log.info("订单成功页弹窗可见")
            
            # 1. 验证顶部标题存在
            title = p.locator(dweb_order_confirmation_ele.ele_order_popup_title).is_visible()
            if not title:
                log.warning("订单成功页弹窗标题验证失败")
                result["success"] = False
            
            # 2. 先点击分享按钮验证分享功能
            start_earning_btn = p.get_by_test_id(dweb_order_confirmation_ele.start_earning)
            if start_earning_btn.is_visible(timeout=3000):
                # 记录当前URL
                current_url = p.url
                
                # 点击分享按钮
                log.info("点击开始赚取积分按钮")
                start_earning_btn.click()
                p.wait_for_timeout(5000)
                
                # 验证点击后的跳转情况
                new_url = p.url
                url_changed = current_url != new_url
                
                # 判断跳转结果
                if "/order/share/grocery/create" in new_url:
                    # 正常订单 - 跳转到分享页面
                    log.info(f"成功跳转到分享页面: {new_url}")
                    result["details"]["share_action"] = {
                        "clicked": True,
                        "redirected": True,
                        "share_url": new_url,
                        "success": True,
                        "order_status": "normal"
                    }
                elif "/en" == new_url :
                    # 已取消订单 - 跳转到首页
                    log.info(f"订单可能已取消，跳转到首页: {new_url}")
                    result["details"]["share_action"] = {
                        "clicked": True,
                        "redirected": True,
                        "share_url": new_url,
                        "success": True,  # 仍然视为成功，因为这是已取消订单的预期行为
                        "order_status": "cancelled"
                    }
                else:
                    # 其他情况 - 未知跳转
                    log.warning(f"未跳转到预期页面，当前URL: {new_url}")
                    result["details"]["share_action"] = {
                        "clicked": True,
                        "redirected": url_changed,
                        "share_url": new_url,
                        "success": False,
                        "order_status": "unknown"
                    }
                    result["success"] = False
                
                # 返回订单成功页
                p.go_back()
                p.wait_for_timeout(2000)
            else:
                log.warning("分享按钮不可见")
                result["details"]["share_action"] = {
                    "clicked": False,
                    "success": False
                }
                result["success"] = False
            
            # 验证弹窗是否重新出现
            popup_after_share = p.get_by_test_id(dweb_order_confirmation_ele.ele_share_popup)
            popup_after_share_visible = popup_after_share.is_visible(timeout=5000)
            result["details"]["popup_after_share_visible"] = popup_after_share_visible
            
            if not popup_after_share_visible:
                log.warning("分享后，订单成功页弹窗不可见")
                result["success"] = False
                return result
            
            # 3. 点击Learn More链接跳转到帮助页面
            learn_more_link = p.get_by_test_id(dweb_order_confirmation_ele.ele_share_terms)
            if learn_more_link.is_visible(timeout=3000):
                # 记录当前URL
                current_url = p.url
                
                # 点击Learn More链接
                log.info("点击Learn More链接")
                learn_more_link.click()
                p.wait_for_timeout(2000)
                
                # 验证是否跳转到帮助页面
                new_url = p.url
                result["details"]["learn_more_navigation"] = {
                    "clicked": True,
                    "original_url": current_url,
                    "new_url": new_url,
                    "url_changed": current_url != new_url
                }
                
                # 验证是否进入帮助页面
                is_help_page = "/help/detail/1720" in new_url
                result["details"]["learn_more_navigation"]["help_page_visible"] = is_help_page
                
                if is_help_page:
                    log.info("成功跳转到帮助页面")
                else:
                    log.warning(f"URL不是帮助页面: {new_url}")
                    result["success"] = False
                
                # 返回订单成功页
                log.info("返回订单成功页")
                p.go_back()
                p.wait_for_timeout(5000)
                
                # 验证是否返回订单成功页
                back_url = p.url
                result["details"]["learn_more_navigation"]["back_url"] = back_url
                result["details"]["learn_more_navigation"]["back_success"] = back_url == current_url
                
                if back_url == current_url:
                    log.info("成功返回订单成功页")
                else:
                    log.warning(f"返回后URL不匹配，期望: {current_url}, 实际: {back_url}")
                    result["success"] = False
                
                # 验证弹窗是否重新出现
                popup_after_help = p.get_by_test_id(dweb_order_confirmation_ele.ele_share_popup)
                popup_after_help_visible = popup_after_help.is_visible(timeout=5000)
                result["details"]["popup_after_help_visible"] = popup_after_help_visible
                
                if not popup_after_help_visible:
                    log.warning("从帮助页面返回后，订单成功页弹窗未重新出现")
                    result["success"] = False
            else:
                log.warning("Learn More链接不可见")
                result["details"]["learn_more_navigation"] = {"clicked": False}
                result["success"] = False
            
            # 4. 关闭弹窗
            close_btn = p.get_by_test_id(dweb_order_confirmation_ele.ele_modal_close)
            if close_btn.is_visible(timeout=3000):
                log.info("点击关闭弹窗按钮")
                close_btn.click()
                p.wait_for_timeout(2000)
                
                # 验证弹窗是否关闭
                popup_closed = not popup.is_visible(timeout=2000)
                result["details"]["popup_closed"] = popup_closed
                
                if popup_closed:
                    log.info("成功关闭订单成功页弹窗")
                else:
                    log.warning("点击关闭按钮后，订单成功页弹窗仍然可见")
                    result["success"] = False
            else:
                log.warning("弹窗关闭按钮不可见")
                result["details"]["close_button_visible"] = False
                result["success"] = False
        
        except Exception as e:
            result["success"] = False
            result["details"]["error"] = str(e)
            import traceback
            log.error(f"验证订单成功页弹窗时出错: {str(e)}")
            log.error(traceback.format_exc())
        
        return result
