from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_mkpl_vender.dweb_mkpl_vender_ele import (
    ele_follow_seller_btn,
    ele_email_input_autocomplete,
    ele_submit_btn,
    ele_password_input_type,
    ele_confirm_btn,
    ele_dialog_container
)
from src.config.weee.log_help import log


class DWebMkplVendorFollowPage(DWebCommonPage):
    """
    桌面端商家关注页面操作类
    """
    
    def __init__(self, page: Page, header):
        super().__init__(page, header)
    
    def goto_vendor_page(self):
        """访问商家页面"""
        self.page.goto("https://www.sayweee.com/zh/mkpl/vendor/6887")
        self.page.wait_for_timeout(4000)
        log.info(f"访问商家页面: {self.page.url}")
    
    def click_follow_button_if_exists(self):
        """如果关注按钮存在则点击"""
        follow_btn = self.page.get_by_test_id(ele_follow_seller_btn)
        if follow_btn.count() > 0:
            initial_text = follow_btn.inner_text().strip()
            log.info(f"点击前按钮文字: {initial_text}")
            
            follow_btn.click()
            log.info("点击关注按钮成功")
            return initial_text
        else:
            log.info("未找到关注按钮")
            return None
    
    def handle_login_flow(self):
        """处理登录流程"""
        # 输入邮箱
        email_input = self.FE.ele(ele_email_input_autocomplete, timeout=3000)
        if email_input:
            log.info("输入邮箱")
            email_input.fill("<EMAIL>")
            
            # 点击提交按钮
            submit_btn = self.FE.ele(ele_submit_btn, timeout=3000)
            if submit_btn:
                submit_btn.click()
                log.info("点击邮箱提交按钮")
                
                # 等待3秒后输入密码
                self.page.wait_for_timeout(3000)
                password_input = self.FE.ele(ele_password_input_type, timeout=3000)
                if password_input:
                    log.info("输入密码")
                    password_input.fill("123456")
                    
                    # 点击密码提交按钮
                    submit_btn = self.FE.ele(ele_submit_btn, timeout=3000)
                    if submit_btn:
                        submit_btn.click()
                        log.info("点击密码提交按钮")
                        self.page.wait_for_timeout(3000)
                        return True
        return False
    
    def check_dialog_elements(self):
        """检查弹窗元素是否存在"""
        dialog = self.page.get_by_test_id(ele_dialog_container)
        count = dialog.count()
        log.info(f"弹窗容器元素数量: {count}, testid: {ele_dialog_container}")
        return count > 0
    
    def click_confirm_button_in_dialog(self):
        """在弹窗容器中查找并点击确定按钮"""
        confirm_btn = self.page.get_by_test_id(ele_confirm_btn)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("在弹窗中点击确定按钮成功")
            return True
        else:
            log.info("在弹窗中未找到确定按钮")
            return False
