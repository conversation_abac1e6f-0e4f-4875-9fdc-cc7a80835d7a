from playwright.sync_api import Page
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.Dweb.EC.dweb_ele.dweb_mkpl_vender.dweb_mkpl_vender_ele import *
from src.config.weee.log_help import log


class DWebMkplVendorHomepage(DWebCommonPage):
    """
    桌面端商家主页操作类
    """
    
    def __init__(self, page: Page, header):
        super().__init__(page, header)
    
    def navigate_to_vendor_page(self):
        """访问商家页面"""
        self.page.goto("https://www.sayweee.com/en/mkpl/vendor/6887")
        self.page.wait_for_timeout(3000)
        log.info("访问商家页面完成")
    
    def check_required_elements_exist(self):
        """检查页面必须存在的元素"""
        required_elements = [
            ele_seller_logo,
            ele_seller_name,
            ele_seller_message,
            ele_seller_eta_range,
            ele_seller_shipping_info,
            ele_seller_all_products_tab,
            ele_seller_about,
            ele_seller_search
        ]
        
        for element in required_elements:
            locator = self.page.get_by_test_id(element)
            if locator.count() == 0:
                raise AssertionError(f"必需元素不存在: {element}")
            log.info(f"元素检查成功: {element}")
    
    def check_explore_tab_and_click_all_products(self):
        """检查探索标签页是否存在，如果存在则点击全部商品标签页"""
        explore_tab = self.page.get_by_test_id(ele_seller_explore_tab)
        if explore_tab.count() > 0:
            all_products_tab = self.page.get_by_test_id(ele_seller_all_products_tab)
            if all_products_tab.count() > 0:
                all_products_tab.click()
                log.info("点击全部商品标签页成功")
                return True
        return False
    
    def scroll_product_list(self):
        """滚动商品列表"""
        self.page.mouse.wheel(0, 1000)
        log.info("滚动商品列表完成")
    
    def click_back_to_top(self):
        """点击置顶按钮"""
        back_top_btn = self.page.get_by_test_id(ele_seller_back_top)
        if back_top_btn.count() > 0:
            back_top_btn.click()
            log.info("点击置顶按钮成功")
            return True
        return False
    
    def check_and_click_tabs_sequence(self):
        """按顺序检查并点击标签页"""
        tabs_to_check = [
            (ele_seller_review_tab, "晒单标签页", "?tab=reviews"),
            (ele_seller_feedback, "评价标签页", "?tab=feedback"),
            (ele_seller_shipping_return, "运输说明标签页", "?tab=shipping_return"),
            (ele_seller_about, "关于标签页", "?tab=about")
        ]
        
        clicked_tabs = []
        for tab_id, tab_name, expected_url_param in tabs_to_check:
            self.page.wait_for_timeout(5000)
            tab = self.page.get_by_test_id(tab_id)
            if tab.count() > 0:
                tab.click()
                self.page.wait_for_timeout(2000)  # 等待URL更新
                current_url = self.page.url
                
                # 检查URL是否包含预期参数
                if expected_url_param in current_url:
                    log.info(f"点击{tab_name}成功，URL包含{expected_url_param}")
                    clicked_tabs.append((tab_name, expected_url_param, True))
                else:
                    log.warning(f"点击{tab_name}成功，但URL不包含{expected_url_param}，当前URL: {current_url}")
                    clicked_tabs.append((tab_name, expected_url_param, False))
            else:
                log.info(f"{tab_name}不存在，继续检查下一个")
        
        return clicked_tabs
