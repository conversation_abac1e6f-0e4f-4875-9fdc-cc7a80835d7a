"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   dweb_page_checkout.py
@Description    :  PC结算页面对象
@CreateTime     :  2025/6/10 14:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 14:30
"""
import allure
from playwright.sync_api import Page, expect

from src.Dweb.EC.dweb_ele.dweb_category import dweb_category_ele
from src.Dweb.EC.dweb_ele.dweb_checkout import dweb_checkout_ele
from src.Dweb.EC.dweb_ele.dweb_home import dweb_home_ele
from src.Dweb.EC.dweb_pages.dweb_common_page import DWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class DWebCheckoutPage(DWebCommonPage):
    """结算页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入结算页面
        2. 如果zipcode不是98011，则切换zipcode
        3. 调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入结算页
        if page_url:
            self.page.goto(TEST_URL + page_url)
        else:
            self.page.goto(TEST_URL + "/order/checkout?cart_domain=grocery")
        
        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        
        # 获取顶部zipocde
        # page_zipcode = self.page.locator(dweb_home_ele.ele_zipcode).text_content()
        # if page_zipcode != zipcode:
        #     switch_zipcode(headers=self.header)
        #     self.page.reload()
        #
        # self.page.wait_for_timeout(5000)
        # close_advertise_on_home(self.page)

    def verify_checkout_title(self):
        ele_checkout_title= self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_title)
        assert ele_checkout_title.is_visible(), "顶部标题不可见"
        ele_checkout_title_message_text = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_title_message_text)
        assert ele_checkout_title_message_text.is_visible(), "顶部标题下面的文案不可见"
        """验证结算页标题"""
        with allure.step("验证会员等级显示"):
            try:
                # 获取会员等级信息
                member_level = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_loytal_vip_content)
                level_text = member_level.text_content()

                # 根据不同等级验证对应的图标和文案
                if "Bronze" in level_text.upper():
                    # p.get_by_test_id('wid-checkout-rewards-header')
                    self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_loytal_vip_icon).is_visible()
                    expect(self.page.locator("text=Bronze Rewards member")).to_be_visible()
                    self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_loytal_button).is_hidden()
                elif "Silver" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Silver Rewards member")).to_be_visible()
                    self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_loytal_button).is_visible()

                elif "Gold" in level_text.upper():
                    expect(self.page.get_by_test_id('wid-checkout-rewards-header').locator('img')).to_be_visible()
                    expect(self.page.locator("text=Gold Rewards member")).to_be_visible()
                    self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_loytal_button).is_visible()
                log.info(f"会员等级验证成功: {level_text}")
            except Exception as e:
                log.error(f"验证会员等级显示失败: {str(e)}")
                raise

    def verify_shipping_section(self):
        """验证配送信息区域"""
        with allure.step("验证配送信息区域"):
            save= self.page.get_by_test_id(dweb_checkout_ele.ele_address_form_save)
            if save.is_visible():
                log.info("地位为空,请选择地址")
            # 验证配送信息标题
            shipping_section = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_address_info_title)
            assert shipping_section.is_visible(), "配送标题不可见"
            log.info("配送信息区域可见")

            self.page.wait_for_timeout(2000)

            # 验证增加地址按钮存在
            add_address = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_address_info_address_bt)
            assert add_address.is_visible(), "新增地址按钮不可见"
            log.info("增加地址按钮可见")

            # 验证配送地址
            address_info = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_address_info)
            assert address_info.is_visible(), "配送地址区域不可见"
            address_text = address_info.text_content()
            log.info(f"配送地址: {address_text}")

            # 获取所有地址卡片
            address_cards = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_address_card).all()
            log.info(f"找到 {len(address_cards)} 个地址卡片")
            self.page.wait_for_timeout(2000)

            # 先验证所有地址卡片的元素
            for i, card in enumerate(address_cards):
                log.info(f"验证第 {i + 1} 个地址卡片")

                # 验证地址图标存在
                location_icon = card.get_by_test_id(dweb_checkout_ele.ele_checkout_address_card_icon)
                assert location_icon.is_visible(), f"第 {i + 1} 个地址卡片的图标不可见"
                log.info(f"第 {i + 1} 个地址卡片的图标可见")

                # 验证地址标题存在
                address_title = card.get_by_test_id(dweb_checkout_ele.ele_address_name)
                assert address_title.is_visible(), f"第 {i + 1} 个地址标题不可见"
                title_text = address_title.text_content()
                log.info(f"第 {i + 1} 个地址卡片的标题: {title_text}")

                # 验证地址city存在
                address_detail = card.get_by_test_id(dweb_checkout_ele.ele_address_city)
                assert address_detail.is_visible(), f"第 {i + 1} 个地址卡片的city不可见"
                detail_text = address_detail.text_content()
                log.info(f"第 {i + 1} 个地址卡片的city: {detail_text}")

                # 验证编辑按钮存在
                edit_button = card.get_by_test_id(dweb_checkout_ele.ele_address_edit)
                assert edit_button.is_visible(), f"第 {i + 1} 个地址卡片的编辑按钮不可见"
                log.info(f"第 {i + 1} 个地址卡片的编辑按钮可见")

            # 验证完所有卡片后，再选择第一个地址卡片
            if len(address_cards) > 0:
                log.info("选择第一个地址卡片")
                address_cards[0].click()
                self.page.wait_for_timeout(2000)

                # 验证选择地址后，地址卡片列表不可见
                address_list = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_address_card)
                assert not address_list.is_visible(), "选择地址后，地址卡片列表仍然可见"
                log.info("选择地址后，地址卡片列表已正确隐藏")

            self.page.wait_for_timeout(2000)

            # 返回地址信息
            return {
                "address_text": address_text,
                "address_cards_count": len(address_cards)
            }

    def verify_payment_section(self):
        """验证支付信息区域"""
        with allure.step("验证支付信息区域"):
            try:
                # 验证支付信息区域存在
                payment_section = self.page.get_by_test_id(dweb_checkout_ele.ele_payment_method_section)
                assert payment_section.is_visible(), "支付信息区域不可见"
                log.info("支付信息区域可见")
                
                # 检查是否存在积分模块
                rewards_section = self.page.get_by_test_id(dweb_checkout_ele.ele_payment_method_point)
                has_rewards = rewards_section.is_visible()
                
                if has_rewards:
                    log.info("积分模块存在")
                    # 验证积分应用按钮
                    rewards_apply_btn = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_points_switch)
                    assert rewards_apply_btn.is_visible(), "积分应用按钮不可见"
                    
                    # 检查积分开关状态
                    is_points_enabled = rewards_apply_btn.is_checked()
                    log.info(f"积分开关状态: {'开启' if is_points_enabled else '关闭'}")
                    
                    if is_points_enabled:
                        # 积分开启状态，检查是否能全部支付订单
                        points_content = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_points_content)
                        points_text = points_content.text_content() if points_content.is_visible() else ""
                        log.info(f"积分内容: {points_text}")
                        
                        # 如果积分能全部支付，不应显示其他支付方式
                        payment_methods = [
                            dweb_checkout_ele.ele_checkout_payment_method_p,  # PayPal
                            dweb_checkout_ele.ele_checkout_payment_method_b,  # 信用卡
                            dweb_checkout_ele.ele_checkout_payment_method_i,  # 微信
                            dweb_checkout_ele.ele_checkout_payment_method_m,  # EBT
                            dweb_checkout_ele.ele_checkout_payment_method_h,  # CashApp
                            dweb_checkout_ele.ele_checkout_payment_method_v   # venmo
                        ]
                        
                        other_payment_visible = any(
                            self.page.get_by_test_id(method).is_visible() 
                            for method in payment_methods
                        )
                        
                        if not other_payment_visible:
                            log.info("积分开启且能全部支付，其他支付方式正确隐藏")
                        else:
                            log.info("积分开启但仍显示其他支付方式")
                    else:
                        # 积分关闭状态，应显示其他主支付方式
                        log.info("积分关闭状态，验证其他支付方式")
                        self._verify_other_payment_methods()
                else:
                    log.info("积分模块不存在，验证其他支付方式")
                    self._verify_other_payment_methods()
                
                return {
                    "payment_section_visible": True,
                    "has_rewards": has_rewards,
                    "points_enabled": is_points_enabled if has_rewards else False
                }
            except Exception as e:
                log.error(f"验证支付信息区域失败: {str(e)}")
                raise
    
    def _verify_other_payment_methods(self):
        """验证其他支付方式"""
        payment_methods = {
            "PayPal": dweb_checkout_ele.ele_checkout_payment_method_p,
            "信用卡": dweb_checkout_ele.ele_checkout_payment_method_b,
            "微信支付": dweb_checkout_ele.ele_checkout_payment_method_i,
            "EBT": dweb_checkout_ele.ele_checkout_payment_method_m,
            "CashApp": dweb_checkout_ele.ele_checkout_payment_method_h,
            "venmo": dweb_checkout_ele.ele_checkout_payment_method_v
        }
        
        visible_methods = []
        for method_name, method_id in payment_methods.items():
            method_element = self.page.get_by_test_id(method_id)
            if method_element.is_visible():
                visible_methods.append(method_name)
                log.info(f"{method_name}支付方式可见")
                
                try:
                    # 点击支付方式进行选择
                    method_element.click()
                    self.page.wait_for_timeout(1000)
                    
                    # 验证支付方式选择成功 - 检查支付区域是否仍然存在
                    payment_section = self.page.get_by_test_id(dweb_checkout_ele.ele_payment_method_section)
                    assert payment_section.is_visible(), f"选择{method_name}后支付区域不存在"
                    log.info(f"成功选择{method_name}支付方式")
                    break  # 只测试第一个可见的支付方式
                except Exception as e:
                    log.warning(f"选择{method_name}支付方式时出现异常: {str(e)}，继续尝试下一个")
                    continue
        
        assert len(visible_methods) > 0, "没有找到可用的支付方式"
        log.info(f"找到可用支付方式: {', '.join(visible_methods)}")

    def verify_order_summary(self):
        """验证订单总结区域"""
        summary_section = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_right)
        assert summary_section.is_visible(), "订单总结区域不可见"
        log.info("订单摘要区域可见")
        
        # 验证实际支付
        subtotal = self.page.get_by_test_id(dweb_checkout_ele.ele_subtotal)
        subtotal_price  = self.page.get_by_test_id(dweb_checkout_ele.ele_subtotal_amount)
        # subtotal_text = subtotal.text_content()
        assert subtotal.is_visible(), "实际支付不可见"
        log.info(f"实际支付: {subtotal_price}")
        assert "$" in subtotal_price, "实际支付金额格式不正确"

        # 验证订单总结
        summary_title = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_right_summary_title)
        assert summary_title.is_visible(), "订单总结不可见"


        # 验证配送费
        delivery_fee = self.page.get_by_test_id(dweb_checkout_ele.ele_order_delivery_fee_title)
        assert delivery_fee.is_visible(), "配送费信息不可见"
        delivery_fee_text = delivery_fee.text_content()
        log.info(f"配送费: {delivery_fee_text}")

        # 验证配送费
        delivery_fee = self.page.get_by_test_id(dweb_checkout_ele.ele_order_service_fee_title)
        assert delivery_fee.is_visible(), "服务费信息不可见"
        delivery_fee_text = delivery_fee.text_content()
        log.info(f"服务费: {delivery_fee_text}")
        
        # 验证税费
        tax = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_summary_taxes_title)
        assert tax.is_visible(), "税费信息不可见"
        tax_text = tax.text_content()
        log.info(f"税费: {tax_text}")
        
        # 验证总计
        total = self.page.get_by_test_id(dweb_checkout_ele.ele_checkout_right_summary_subtotal_amount)
        assert total.is_visible(), "总计信息不可见"
        total_text = total.text_content()
        log.info(f"订单总计: {total_text}")
        assert "$" in total_text, "总计金额格式不正确"

        # 验证优惠券模块存在
        coupon= self.page.get_by_test_id(dweb_checkout_ele.ele_coupon_title)
        assert coupon.is_visible(), "coupon区域不可见"


        return {
            "subtotal": subtotal_text,
            "delivery_fee": delivery_fee_text,
            "tax": tax_text,
            "total": total_text
        }

    def verify_cart_items(self):
        """验证购物车商品列表"""
        # 验证商品列表区域
        items_section = self.page.get_by_test_id("wid-checkout-items")
        assert items_section.is_visible(), "商品列表区域不可见"
        log.info("商品列表区域可见")
        
        # 获取所有商品
        items = self.page.get_by_test_id("wid-checkout-item").all()
        assert len(items) > 0, "商品列表为空"
        log.info(f"商品列表中有 {len(items)} 个商品")
        
        items_info = []
        for i, item in enumerate(items):
            # 获取商品名称
            item_name = item.get_by_test_id("wid-checkout-item-name")
            assert item_name.is_visible(), f"第 {i+1} 个商品名称不可见"
            name_text = item_name.text_content()
            
            # 获取商品价格
            item_price = item.get_by_test_id("wid-checkout-item-price")
            assert item_price.is_visible(), f"第 {i+1} 个商品价格不可见"
            price_text = item_price.text_content()
            assert "$" in price_text, f"第 {i+1} 个商品价格格式不正确"
            
            # 获取商品数量
            item_quantity = item.get_by_test_id("wid-checkout-item-quantity")
            assert item_quantity.is_visible(), f"第 {i+1} 个商品数量不可见"
            quantity_text = item_quantity.text_content()
            
            log.info(f"商品 {i+1}: 名称={name_text}, 价格={price_text}, 数量={quantity_text}")
            items_info.append({
                "name": name_text,
                "price": price_text,
                "quantity": quantity_text
            })
        
        return items_info

    def verify_place_order_button(self):
        """验证下单按钮"""
        place_order_btn = self.page.locator(dweb_checkout_ele.ele_place_order)
        assert place_order_btn.is_visible(), "下单按钮不可见"
        # assert place_order_btn.is_enabled(), "下单按钮未启用"
        button_text = place_order_btn.text_content()
        log.info(f"下单按钮文本: {button_text}")
        assert "Place order" in button_text, "下单按钮文本不正确"
        return place_order_btn

    def verify_order_confirmation_section(self):
        """验证订单确认模块"""
        with allure.step("验证订单确认模块"):
            try:
                # 验证订单确认标题存在
                confirmation_title = self.page.get_by_test_id("wid-checkout-order-confirmation-title")
                assert confirmation_title.is_visible(), "订单确认标题不可见"
                title_text = confirmation_title.text_content()
                log.info(f"订单确认标题: {title_text}")

                # 验证购物车图标存在
                cart_icon = self.page.get_by_test_id("wid-checkout-cart-icon")
                assert cart_icon.is_visible(), "购物车图标不可见"
                log.info("购物车图标可见")

                # 验证本地配送标题存在
                local_delivery_title = self.page.get_by_test_id("wid-checkout-local-delivery-title")
                assert local_delivery_title.is_visible(), "本地配送标题不可见"
                delivery_title_text = local_delivery_title.text_content()
                log.info(f"本地配送标题: {delivery_title_text}")

                # 验证配送描述存在
                delivery_description = self.page.get_by_test_id("wid-checkout-delivery-description")
                assert delivery_description.is_visible(), "配送描述不可见"
                description_text = delivery_description.text_content()
                log.info(f"配送描述: {description_text}")

                # 验证日期切换功能
                date_selector = self.page.get_by_test_id("wid-checkout-date-selector")
                assert date_selector.is_visible(), "日期选择器不可见"

                # 记录当前日期
                current_date = date_selector.text_content()
                log.info(f"当前选择日期: {current_date}")

                # 点击日期选择器
                date_selector.click()
                self.page.wait_for_timeout(1000)

                # 验证日期选择弹窗出现
                date_popup = self.page.get_by_test_id("wid-checkout-date-popup")
                assert date_popup.is_visible(), "日期选择弹窗不可见"
                log.info("日期选择弹窗已打开")

                # 获取可选日期选项
                date_options = self.page.get_by_test_id("wid-checkout-date-option").all()
                if len(date_options) > 1:
                    # 选择第二个日期选项
                    date_options[1].click()
                    self.page.wait_for_timeout(1000)

                    # 验证日期已切换
                    new_date = date_selector.text_content()
                    date_changed = new_date != current_date
                    log.info(f"日期切换{'成功' if date_changed else '失败'}, 新日期: {new_date}")
                else:
                    log.info("只有一个日期选项，无法测试切换功能")

                # 验证商品展开功能
                expand_arrow = self.page.get_by_test_id("wid-checkout-expand-arrow")
                assert expand_arrow.is_visible(), "商品展开箭头不可见"

                # 点击箭头展开商品列表
                expand_arrow.click()
                self.page.wait_for_timeout(1000)

                # 验证商品列表展开
                product_list = self.page.get_by_test_id("wid-checkout-product-list")
                assert product_list.is_visible(), "商品列表未展开"
                log.info("商品列表已展开")

                # 获取所有商品项
                product_items = self.page.get_by_test_id("wid-checkout-product-item").all()
                assert len(product_items) > 0, "商品列表为空"
                log.info(f"找到 {len(product_items)} 个商品")

                # 验证每个商品的详细信息
                for i, item in enumerate(product_items):
                    log.info(f"验证第 {i + 1} 个商品")

                    # 验证商品图片
                    product_image = item.get_by_test_id("wid-checkout-product-image")
                    assert product_image.is_visible(), f"第 {i + 1} 个商品图片不可见"
                    log.info(f"第 {i + 1} 个商品图片可见")

                    # 验证商品标题
                    product_title = item.get_by_test_id("wid-checkout-product-title")
                    assert product_title.is_visible(), f"第 {i + 1} 个商品标题不可见"
                    title_text = product_title.text_content()
                    log.info(f"第 {i + 1} 个商品标题: {title_text}")

                    # 验证商品价格
                    product_price = item.get_by_test_id("wid-checkout-product-price")
                    assert product_price.is_visible(), f"第 {i + 1} 个商品价格不可见"
                    price_text = product_price.text_content()
                    assert "$" in price_text, f"第 {i + 1} 个商品价格格式不正确"
                    log.info(f"第 {i + 1} 个商品价格: {price_text}")

                    # 验证商品数量
                    product_quantity = item.get_by_test_id("wid-checkout-product-quantity")
                    assert product_quantity.is_visible(), f"第 {i + 1} 个商品数量不可见"
                    quantity_text = product_quantity.text_content()
                    log.info(f"第 {i + 1} 个商品数量: {quantity_text}")

                log.info("订单确认模块验证完成")
                return {
                    "title_text": title_text,
                    "delivery_title": delivery_title_text,
                    "description": description_text,
                    "product_count": len(product_items),
                    "date_changed": date_changed if 'date_changed' in locals() else False
                }

            except Exception as e:
                log.error(f"验证订单确认模块失败: {str(e)}")
                raise