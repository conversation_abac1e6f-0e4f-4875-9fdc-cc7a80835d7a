import allure
import pytest
from playwright.sync_api import Page, expect
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


@allure.story("搜索功能验证")
class TestSearchFunction:
    @pytest.mark.search
    @allure.title("验证搜索联想关键词功能")
    def test_search_with_suggestions(self, page: Page):
        """
        测试步骤：
        1. 访问首页
        2. 点击搜索框
        3. 输入关键词
        4. 从联想结果中选择第一个关键词
        5. 验证搜索结果页
        """
        # 元素定位
        SEARCH_INPUT = "wid-main-search-no-fouce"
        SEARCH_FOCUS = "wid-main-search-fouced"

        # 1. 访问首页
        page.goto(TEST_URL)
        page.wait_for_load_state("networkidle")
        log.info("首页加载完成")

        # 2. 点击搜索框
        page.get_by_test_id(SEARCH_INPUT).click()
        expect(page.get_by_test_id(SEARCH_FOCUS)).to_be_visible()
        log.info("搜索框获得焦点")

        # 3. 输入关键词"app"
        test_keyword = "app"
        page.locator(f'[data-testid="{SEARCH_FOCUS}"] input').fill(test_keyword)
        page.wait_for_timeout(500)  # 等待联想结果加载
        log.info(f"已输入关键词: {test_keyword}")

        # 4. 选择第一个联想结果（通过CSS选择器定位）
        first_suggestion = page.locator(".search-suggestion-item:first-child")
        expect(first_suggestion).to_be_visible()
        suggestion_text = first_suggestion.text_content()
        first_suggestion.click()
        log.info(f"已选择联想建议: {suggestion_text}")

        # 5. 验证搜索结果页
        page.wait_for_load_state("networkidle")
        expect(page).to_have_url(contains=f"keyword={suggestion_text.lower()}")

        # 验证结果标题
        results_title = page.locator(".search-results-title")
        expect(results_title).to_contain_text(suggestion_text, ignore_case=True)

        # 验证商品卡片
        product_card = page.locator("[data-testid='wid-product-card-container']").first
        expect(product_card).to_be_visible()
        expect(product_card.locator(f":text('{suggestion_text}')")).to_be_visible()

        log.info("搜索结果验证通过")

