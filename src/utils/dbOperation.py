import os

from utils.DatabaseUtil import DatabaseUtil


class DatabaseOperation:
    """
    数据库增删改查
    """
    # jar包路径
    current_path = os.path.dirname(__file__)
    mysql_path = current_path + '/jar/mysql-connector-j-8.0.31.jar'

    # mysql数据库的配置
    mysql_config = {
        "url": "*******************************************",
        "user": "root",
        "password": "123456",
        "driver": "com.mysql.cj.jdbc.Driver",
        "path": mysql_path
    }

    def get_info_by_name(self, name):
        sql = "select * from emp where name = '{}'".format(name)
        database = DatabaseUtil(self.mysql_config)
        data: str = database.get_data(sql)[0]
        database.close_connection()
        return data

    def get_info_by_job(self, job):
        sql = "select * from emp where job = '{}'".format(job)
        database = DatabaseUtil(self.mysql_config)
        data: str = database.get_data(sql)
        database.close_connection()
        return data


if __name__ == '__main__':
    # print(DatabaseOperation().get_info_by_name('金庸'))
    for i in DatabaseOperation().get_info_by_job('开发'):
        print(i)
