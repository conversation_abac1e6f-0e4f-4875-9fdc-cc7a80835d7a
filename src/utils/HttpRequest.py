import requests
import json
import pprint


class HttpRequest:
    r""" has not been tested
    """

    def __init__(self):
        pass

    @classmethod
    def request(cls, data, params_type="json", **kwargs):
        if data.get("method").lower() == "post":
            if params_type == "form":
                response = cls.__post(url=data.get("path"), params=data.get("param"), data=data.get("request_body"),
                                      headers=data.get("headers"), **kwargs)
                return response
            elif params_type == "multipart":
                # 这个貌似不能带header，不知道为啥
                response = cls.__post(url=data.get("path"), files=data.get("request_body"),
                                      **kwargs)
                return response
            elif params_type == "json":
                response = cls.__post(url=data.get("path"), params=data.get("param"), json=data.get("request_body"),
                                      headers=data.get("headers"), **kwargs)
                return response

        elif data.get("method").lower() == "get":
            response = cls.__get(url=data.get("path"), params=data.get("param"), headers=data.get("headers"), **kwargs)
            return response

        elif data.get("method").lower() == "put":
            response = cls.__put(url=data.get("path"), params=data.get("param"), json=data.get("request_body"),
                                 headers=data.get("headers"), **kwargs)
            return response

        elif data.get("method").lower() == "delete":
            response = cls.__delete(url=data.get("path"), params=data.get("param"), json=data.get("request_body"),
                                    headers=data.get("headers"), **kwargs)
            return response

    @staticmethod
    def __post(url, params=None, data=None, json=None, headers=None, **kwargs):
        response = requests.post(url=url, params=params, data=data, json=json, headers=headers, **kwargs)
        return response

    @staticmethod
    def __get(url, params=None, headers=None, **kwargs):
        response = requests.get(url, params=params, headers=headers, **kwargs)
        return response

    @staticmethod
    def __put(url, params=None, data=None, json=None, headers=None, **kwargs):
        response = requests.put(url=url, params=params, data=data, json=json, headers=headers, **kwargs)
        return response

    @staticmethod
    def __delete(url, params=None, headers=None, json=None, **kwargs):
        response = requests.delete(url=url, params=params, headers=headers, json=json, **kwargs)
        return response


if __name__ == "__main__":
    res = HttpRequest.request(
        data={
            "path": "http://clickhouse-http-endpoint-1303890441.us-west-2.elb.amazonaws.com:8123/",
            "method": "post",
            "headers": {
                "Content-Type": r"text/plain;charset=UTF-8",
                "Authorization": r"Basic ZGF0YV9wbGF0Zm9ybTo3dlNpRVcyZTJkMzJwNTZN"
            },
            "param": {
                "add_http_cors_header": 1,
                "default_format": "JSONCompact",
                "max_result_rows": 1000,
                "max_result_bytes": 10000000,
                "result_overflow_mode": "break"
            },

            "request_body": r"select * from weee_data.data_tracking_all where l0_user_id ='7790952' and l0_event_type ='t2_banner_imp' order by __time desc limit 100"
        },
        params_type="form"
    )
    print("res.json===>",res.json())




