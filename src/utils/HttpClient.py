import requests
import json


class HttpClient(object):
    def __init__(self):
        pass

    def request(self, requestMethod, requestUrl, paramsType,
                requestData, headers=None, **kwargs):
        if requestMethod.lower() == "post" or requestMethod == "POST":
            print("---", requestData, type(requestData))
            if paramsType == "form":
                response = self.__post(url=requestUrl, data=json.dumps(eval(requestData)),
                                       headers=headers, **kwargs)
                return response
            elif paramsType == "json":
                response = self.__post(url=requestUrl, json=requestData,
                                       headers=headers, **kwargs)
                return response
        elif requestMethod.lower() == "get":
            # request_url = requestUrl + "?"
            if paramsType == "url":
                # count = 0
                # for(k,v) in requestData.items():
                #     print("count===>",count)
                #     print("requestdata.length-1",len(requestData)-1 )
                #     if count == (len(requestData)-1):
                #         request_url = request_url + k + "=" + v
                #         break
                #     else:
                #         request_url = request_url + k + "=" + v + "&"
                #     count = count + 1
                # request_url = "%s%s" % (requestUrl, requestData)
                response = self.__get(url=requestUrl, params=requestData,headers=headers, **kwargs)
                return response

        elif requestMethod.lower() == "put":
            response = self.__put(url=requestUrl, json=requestData,
                                   headers=headers, **kwargs)
            return response

    def __post(self, url, data=None, json=None, headers=None, **kwargs):
        print("----post requests----")
        response = requests.post(url=url, data=data, json=json, headers=headers)
        return response

    def __get(self, url, params=None, headers=None, **kwargs):
        response = requests.get(url, params=params, headers=headers, **kwargs)
        return response

    def __put(self, url, data=None, json=None, headers=None, **kwargs):
        response = requests.put(url=url, data=data, json=json, headers=headers)
        return response




if __name__ == "__main__":
    hc = HttpClient()
    res = hc.request("get", "http://39.106.41.11:8080/getBlogContent/", "url", '2')
    print(res.json())
