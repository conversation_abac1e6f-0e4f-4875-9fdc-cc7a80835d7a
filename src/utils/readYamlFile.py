import os

import yaml


class ReadYamlFile:
    """
    读取yaml数据
    """

    @classmethod
    def read_yaml(cls, filename):
        # 文件路径
        filePath = os.path.abspath(os.path.dirname(os.path.dirname(__file__))) + '/data' + '/' + filename
        # 打开yaml文件
        with open(filePath, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return data

    @classmethod
    def write_yaml(cls, filename, data):
        # 文件路径
        filePath = os.path.abspath(os.path.dirname(os.path.dirname(__file__))) + '/data' + '/' + filename
        with open(filePath, 'w', encoding='utf-8') as f:
            yaml.safe_dump(data, f, allow_unicode=True, encoding='utf-8')

    @classmethod
    def read(cls, filename):
        """
        遍历数据，先获取单组数据，再分别存储参数名称keyTemp和参数值valueTemp
        因为参数名称固定，所以将其赋值给paramName
        由于参数值可能存在多组，所以将valueTemp追加到paramValue列表
        返回参数名paramName和参数值paramValue
            :param filename: yaml文件名
        """
        # 创建参数名称和参数值列表
        paramName = []
        paramValue = []
        data = cls.read_yaml(filename)
        # 将数据转化为dict
        for case in data:
            dataTemp = data[case]
            # print('单组数据:', dataTemp)
            keyTemp = []
            valueTemp = []
            for key in dataTemp:
                keyTemp.append(key)
                valueTemp.append(dataTemp[key])
            paramName = keyTemp
            paramValue.append(valueTemp)
            # print('key:', paramName)
            # print('value:', paramValue)
        return paramName, paramValue


if __name__ == '__main__':
    # Name, Value = ReadYamlFile().read('case_001.yaml')
    # print('最终name:', Name)
    # print('最终value:', Value)
    filePath = os.path.dirname(os.path.dirname(__file__))
    print(filePath)
