"""
1、数据库相对应的driver
oracle("oracle.adbc.driver.OracleDriver"),
mysql("com.mysql.jdbc.Driver"),
oceanbase("comm.alipay.oceanbase.jdbc.Driver")
2、完整的url
****************************************************************************************************
***************************************
jdbc:oceanbase://************.2883/CFMS
3、查看数据库版本
# mysql 8.0.31  select version()  查看mysql版本
# oracle  select * from v$version
"""

import jaydebeapi


class DatabaseUtil:
    """
    封装数据库处理方法
    """
    # 连接对象
    __conn = None
    # 游标
    __cursor = None
    # 查询结果的数据集
    __data = None
    # 数据库参数
    __config = None

    # 打开连接，确定是连接哪个数据库，在setup中调用，调用示例--> DatabaseUtil(config, 'mysql')
    def __init__(self, conf):
        self.__config = conf
        self.__open_connection()

    # 执行sql，查询数据
    def get_data(self, sql_str):
        self.__cursor.execute(sql_str)
        __data_temp = self.__cursor.fetchall()
        return __data_temp

    # 执行sql，更新数据
    def update_data(self, sql_str):
        self.__cursor.execute(sql_str)

    # 打开数据库连接
    def __open_connection(self):
        self.__conn = jaydebeapi.connect(self.__config['driver'], self.__config['url'],
                                         [self.__config['user'], self.__config['password']],
                                         self.__config['path'])
        self.__cursor = self.__conn.cursor()

    # 关闭数据库连接（可以在teardown中调用）
    def close_connection(self):
        self.__cursor.close()
        self.__conn.close()
