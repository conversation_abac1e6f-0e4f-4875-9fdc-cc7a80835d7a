import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl.mweb_page_mkpl_all_store.mweb_page_mkpl_all_store import MWebMkplAllStorePage
from src.config.weee.log_help import log


@allure.story("H5-商品加购功能")
class TestSellerProductAddToCart:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【101495】商品加购减购数量验证测试")
    @pytest.mark.h5home
    def test_101495_seller_product_add_to_cart(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        商品加购减购数量验证测试
        1. 访问全球购页面
        2. 检查商品卡片和加购按钮
        3. 多次点击加购按钮，验证数量增加
        4. 多次点击减购按钮，验证数量递减
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        # 创建页面操作实例
        all_store_page = MWebMkplAllStorePage(_page, h5_autotest_header, _context)
        
        with allure.step("访问全球购页面"):
            all_store_page.navigate_to_global_page()
            log.info("访问全球购页面完成")

        _page.wait_for_timeout(3000)
        
        with allure.step("检查商品卡片是否存在"):
            product_exists = all_store_page.check_product_card_exists()
            assert product_exists, "商品卡片不存在"
            log.info("商品卡片存在")
        
        with allure.step("多次点击加购按钮并验证数量"):
            # 第1次点击
            with allure.step("第1次点击加购按钮"):
                success = all_store_page.click_atc_and_verify_count(1)
                assert success, "第1次加购失败或数量不正确"
                log.info("第1次加购成功，数量为1")

            _page.wait_for_timeout(3000)
            
            # 第2次点击
            with allure.step("第2次点击加购按钮"):
                success = all_store_page.click_atc_and_verify_count(2)
                assert success, "第2次加购失败或数量不正确"
                log.info("第2次加购成功，数量为2")

            _page.wait_for_timeout(3000)
            
            # 第3次点击
            with allure.step("第3次点击加购按钮"):
                success = all_store_page.click_atc_and_verify_count(3)
                assert success, "第3次加购失败或数量不正确"
                log.info("第3次加购成功，数量为3")
        
        with allure.step("多次点击减购按钮并验证数量"):
            _page.wait_for_timeout(3000)
            
            # 第1次减购
            with allure.step("第1次点击减购按钮"):
                success = all_store_page.click_minus_and_verify_count(2)
                assert success, "第1次减购失败或数量不正确"
                log.info("第1次减购成功，数量为2")
            
            _page.wait_for_timeout(3000)
            
            # 第2次减购
            with allure.step("第2次点击减购按钮"):
                success = all_store_page.click_minus_and_verify_count(1)
                assert success, "第2次减购失败或数量不正确"
                log.info("第2次减购成功，数量为1")
            
            _page.wait_for_timeout(3000)
            
            # 第3次减购
            with allure.step("第3次点击减购按钮"):
                success = all_store_page.click_minus_and_verify_count(0)
                assert success, "第3次减购失败或数量不正确"
                log.info("第3次减购成功，数量为0")
        
        log.info("商品加购和减购数量验证测试完成")
