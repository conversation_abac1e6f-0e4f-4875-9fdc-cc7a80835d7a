import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl_vender.mweb_page_vendor_homepage import VendorHomepage
from src.config.weee.log_help import log


@allure.story("H5-商家主页功能")
class TestVendorHomepage:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【商家主页】商家主页操作测试")
    @pytest.mark.h5home
    def test_101493_mweb_vendor_homepage(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """商家主页操作流程测试"""
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        vendor_page = VendorHomepage(_page)
        
        with allure.step("访问商家页面"):
            vendor_page.navigate_to_vendor()
            log.info("访问商家页面完成")
        
        with allure.step("检查页面必须存在的元素"):
            vendor_page.check_required_elements_exist()
            log.info("页面元素检查完成")
        
        with allure.step("点击全部标签页"):
            result = vendor_page.click_all_tab_if_exists()
            log.info(f"全部标签页操作结果: {result}")
            
            # 断言URL包含?tab=all
            if result:
                _page.wait_for_timeout(2000)  # 等待URL更新
                current_url = _page.url
                assert "?tab=all" in current_url, f"URL不包含?tab=all，当前URL: {current_url}"
                log.info("URL包含?tab=all验证成功")
            
            log.info("全部标签页操作完成")
        
        with allure.step("滚动列表并检查一键置顶"):
            result = vendor_page.scroll_and_check_back_to_top()
            log.info(f"一键置顶操作结果: {result}")
            log.info("滚动列表并检查一键置顶完成")
        
        with allure.step("等待9秒结束测试"):
            _page.wait_for_timeout(9000)
            log.info("测试结束")
            
        log.info("商家主页操作测试完成")