import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import *
from src.config.weee.log_help import log


@allure.story("H5-商家详情页功能")
class TestSellerDetailPage:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【商家详情页】商家详情页操作测试")
    @pytest.mark.h5home
    def test_101492_mweb_seller_detail_page(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """商家详情页操作流程测试"""
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        
        with allure.step("访问商家页面"):
            _page.goto("https://www.sayweee.com/en/mkpl/vendor/6887")
            _page.wait_for_timeout(3000)
            log.info("✅ 访问商家页面完成")
        
        with allure.step("点击访问商家详情按钮"):
            visit_detail_btn = _page.get_by_test_id(ele_visit_seller_detail)
            if visit_detail_btn.count() > 0:
                visit_detail_btn.click()
                log.info("✅ 点击访问商家详情按钮成功")
                _page.wait_for_timeout(3000)  # 等待页面加载
            else:
                log.error("访问商家详情按钮不存在")
                assert False, "访问商家详情按钮不存在"
        
        with allure.step("等待5秒，点击运输标签页并验证URL"):
            _page.wait_for_timeout(5000)
            shipping_tab = _page.get_by_test_id(ele_seller_shipping_return)
            if shipping_tab.count() > 0:
                shipping_tab.click()
                log.info("✅ 点击运输标签页成功")
                
                # 等待URL更新并验证
                _page.wait_for_timeout(2000)
                current_url = _page.url
                assert "?tab=shipping_return" in current_url, f"URL不包含?tab=shipping_return，当前URL: {current_url}"
                log.info("✅ URL包含?tab=shipping_return验证成功")
            else:
                log.warning("运输标签页不存在，跳过")
        
        with allure.step("等待5秒，点击关于标签页并验证URL"):
            _page.wait_for_timeout(5000)
            about_tab = _page.get_by_test_id(ele_seller_about_tab)
            if about_tab.count() > 0:
                about_tab.click()
                log.info("✅ 点击关于标签页成功")
                
                # 等待URL更新并验证
                _page.wait_for_timeout(2000)
                current_url = _page.url
                assert "?tab=about" in current_url, f"URL不包含?tab=about，当前URL: {current_url}"
                log.info("✅ URL包含?tab=about验证成功")
            else:
                log.warning("关于标签页不存在，跳过")
        
        with allure.step("点击评价标签页并验证URL"):
            feedback_tab = _page.get_by_test_id(ele_seller_detail_feedback)
            if feedback_tab.count() > 0:
                feedback_tab.click()
                log.info("✅ 点击评价标签页成功")
                
                # 等待URL更新并验证
                _page.wait_for_timeout(2000)
                current_url = _page.url
                assert "?tab=feedback" in current_url, f"URL不包含?tab=feedback，当前URL: {current_url}"
                log.info("✅ URL包含?tab=feedback验证成功")
            else:
                log.warning("评价标签页不存在，跳过")
            
        log.info("商家详情页操作测试完成")
