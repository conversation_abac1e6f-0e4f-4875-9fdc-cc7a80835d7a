import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_mkpl_vender.mweb_page_vendor_group_order import MWebVendorGroupOrderPage
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    ele_group_order_pop_up_title,
    ele_group_order_pop_up_subtitle,
    ele_group_order_pop_up_desc,
    ele_group_order_share_image,
    ele_group_order_share_title,
    ele_group_order_share_desc
)
from src.config.weee.log_help import log


@allure.story("移动端商家好友拼单功能")
class TestMWebHostCreateGroupOrder:
    pytestmark = [pytest.mark.mweb_regression]

    @allure.title("【101470】移动端商家好友拼单测试")
    @pytest.mark.present
    def test_101470_host_create_group_order(self, phone_page: dict, h5_autotest_header):
        """
        移动端商家好友拼单测试
        1. 访问商家页面
        2. 检查好友拼单按钮是否存在
        3. 点击好友拼单按钮并验证弹窗内容
        4. 关闭弹窗后再次点击拼单按钮
        5. 点击邀请好友按钮
        6. 验证分享弹窗内容并复制链接
        7. 点击删除按钮并确认取消
        8. 验证页面跳转
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 构造商家拼单页面操作实例
        vendor_page = MWebVendorGroupOrderPage(p, h5_autotest_header, browser_context=c)

        # 执行好友拼单流程测试
        with allure.step("执行好友拼单流程测试"):
            # 访问页面并点击拼单按钮
            vendor_page.navigate_to_vendor_page()

            p.wait_for_timeout(5000)
            
            #if not vendor_page.check_group_order_button_exists():
                #pytest.skip("好友拼单按钮不存在，跳过测试")
            
            vendor_page.click_group_order_button()
            
            # 等待3秒后检查弹窗
            p.wait_for_timeout(6000)
            assert vendor_page.check_popup_exists(), "拼单弹窗未出现"
            
            # 验证弹窗内容不为空
            title_element = p.get_by_test_id(ele_group_order_pop_up_title)
            subtitle_element = p.get_by_test_id(ele_group_order_pop_up_subtitle)
            desc_element = p.get_by_test_id(ele_group_order_pop_up_desc)
            
            title_text = title_element.text_content() if title_element.count() > 0 else ""
            subtitle_text = subtitle_element.text_content() if subtitle_element.count() > 0 else ""
            desc_text = desc_element.text_content() if desc_element.count() > 0 else ""
            
            assert title_text.strip(), "弹窗标题内容为空"
            assert subtitle_text.strip(), "弹窗副标题内容为空"
            assert desc_text.strip(), "弹窗描述内容为空"
            
            log.info(f"弹窗标题: {title_text}")
            log.info(f"弹窗副标题: {subtitle_text}")
            log.info(f"弹窗描述: {desc_text}")
            
            # 等待3秒后关闭弹窗
            p.wait_for_timeout(3000)
            assert vendor_page.close_popup(), "关闭弹窗失败"
            
            # 再次点击拼单按钮并确认
            vendor_page.click_group_order_button()
            p.wait_for_timeout(3000)
            vendor_page.click_confirm_button()
            
            # 等待5秒后检查分享弹窗
            p.wait_for_timeout(5000)
            if vendor_page.check_share_popup_exists():
                # 验证分享弹窗内容不为空
                share_image = p.get_by_test_id(ele_group_order_share_image)
                share_title = p.get_by_test_id(ele_group_order_share_title)
                share_desc = p.get_by_test_id(ele_group_order_share_desc)
                
                image_exists = share_image.count() > 0
                title_text = share_title.text_content() if share_title.count() > 0 else ""
                desc_text = share_desc.text_content() if share_desc.count() > 0 else ""
                
                assert image_exists, "分享弹窗图片不存在"
                assert title_text.strip(), "分享弹窗标题内容为空"
                assert desc_text.strip(), "分享弹窗描述内容为空"
                
                log.info(f"分享弹窗标题: {title_text}")
                log.info(f"分享弹窗描述: {desc_text}")
                
                # 点击复制链接
                p.wait_for_timeout(3000)
                vendor_page.click_copy_link_button()


                # 点击全部商品Tab
                p.wait_for_timeout(3000)
                vendor_page.check_and_click_seller_tab_all()

                # 点击删除按钮
                p.wait_for_timeout(3000)
                vendor_page.click_delete_button()
                
                # 检查取消弹窗并确认
                p.wait_for_timeout(3000)
                if vendor_page.check_cancel_popup_exists():
                    p.wait_for_timeout(3000)
                    vendor_page.click_cancel_confirm_button()
                    
                    # 验证页面跳转
                    p.wait_for_timeout(3000)
                    assert vendor_page.check_page_url("/mkpl/vendor/6887"), "页面未跳转到预期URL"
                    log.info("页面跳转验证成功")
            
            log.info("好友拼单流程测试成功")

        log.info("商家好友拼单测试完成")
