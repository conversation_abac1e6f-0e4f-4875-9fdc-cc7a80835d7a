"""
<AUTHOR>  AI Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   test_114507_mweb_rewards_load_points.py
@Description    :  MWeb积分升级-购买流程验证
@CreateTime     :  2025/07/10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/07/10
"""
import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_ele.mweb_rewards.mweb_rewards_ele import *
from src.Mweb.EC.mweb_pages.mweb_page_rewards.mweb_page_rewards_load_points import MWebRewardsLoadPointsPage
from src.config.weee.log_help import log
from src.common.commonui import scroll_one_page_until


@allure.story("积分升级-购买流程")
class TestMWebRewardsLoadPoints:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("积分升级-购买流程验证")
    def test_114507_load_points(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试积分升级购买流程
        步骤：
        1. 进入rewards页面
        2. 点击立即升级按钮
        3. 在弹窗中选择黄金升级模块
        4. 选择支付方式为PayPal
        5. 点击充值并且升级按钮
        6. 验证跳转到PayPal页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        try:
            # 初始化rewards页面
            rewards_page = MWebRewardsLoadPointsPage(p, h5_autotest_header, browser_context=c)
            log.info("成功初始化rewards页面")

            # 等待页面完全加载
            p.wait_for_load_state("load")
            log.info("页面加载完成")

            # 1. 等待并点击立即升级按钮
            p.wait_for_selector(ele_rewards_upgrade_button, state="visible", timeout=30000)
            rewards_page.click_upgrade_now()
            log.info("成功点击立即升级按钮")
            p.wait_for_timeout(2000)

            # 2. 等待黄金升级模块按钮可见
            p.wait_for_selector(ele_rewards_upgrade_purchase_pop_option_desc, state="visible", timeout=30000)
            rewards_page.select_gold_upgrade()
            log.info("成功选择黄金升级模块")
            p.wait_for_timeout(2000)

            # 3. 滚动到支付方式选择区域并等待可见
            log.info("开始处理支付方式选择...")
            scroll_one_page_until(p, ele_payment_box)
            
            # 等待支付方式选择框可见并点击
            p.wait_for_selector(ele_payment_box, state="visible", timeout=30000)
            rewards_page.select_payment_method()
            log.info("成功点击支付方式选择框")
            p.wait_for_timeout(2000)

            # 4. 等待并选择PayPal支付方式
            scroll_one_page_until(p, ele_paypal_payment)
            p.wait_for_selector(ele_paypal_payment, state="visible", timeout=30000)
            rewards_page.select_paypal()
            log.info("成功选择PayPal支付方式")
            p.wait_for_timeout(2000)

            # 4.5. 点击支付方式确认按钮
            p.wait_for_selector(ele_pay_method_confirm_btn, state="visible", timeout=10000)
            rewards_page.click_pay_method_confirm()
            log.info("成功点击支付方式确认按钮")
            p.wait_for_timeout(2000)

            # 这里我们严格按照步骤执行，不再提前判断是否已经跳转到PayPal页面
            # 5. 等待并点击充值并且升级按钮
            try:
                p.wait_for_selector(ele_rewards_upgrade_btn, state="visible", timeout=10000)
                log.info(f"点击充值并且升级按钮前的URL: {p.url}")
                rewards_page.click_load_and_upgrade()
                log.info("成功点击充值并且升级按钮")
                
                # 等待一下让跳转开始
                p.wait_for_timeout(3000)
                log.info(f"点击按钮后3秒的URL: {p.url}")
            except TimeoutError:
                log.error("❌ 等待充值升级按钮超时")
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                # 检查是否已经跳转到PayPal
                if "paypal.com" in p.url:
                    log.info("✅ 虽然超时，但页面已跳转到PayPal")
                else:
                    raise

            # 6. 等待URL变化并验证
            log.info(f"开始验证PayPal页面跳转，当前URL: {p.url}")
            
            # 如果已经跳转到PayPal页面，直接验证成功
            if "paypal.com" in p.url:
                log.info("✅ 已成功跳转到PayPal支付页面")
                log.info(f"当前URL: {p.url}")
            else:
                # 等待URL变化并验证
                try:
                    log.info("开始等待PayPal页面跳转...")
                    
                    # 方法1：等待URL变化（不等待页面完全加载）
                    p.wait_for_url("**/paypal.com/*", timeout=30000, wait_until="domcontentloaded")
                    log.info(f"检测到URL变化: {p.url}")
                    
                    # 方法2：验证当前URL包含paypal.com
                    if "paypal.com" in p.url:
                        log.info("✅ 成功验证跳转到PayPal支付页面")
                        log.info(f"当前URL: {p.url}")
                    else:
                        log.error(f"❌ URL验证失败，当前URL: {p.url}")
                        raise AssertionError(f"未成功跳转到PayPal支付页面，当前URL: {p.url}")
                    
                    # 方法3：等待PayPal页面特征元素（可选）
                    try:
                        # 等待PayPal页面的一些特征元素
                        paypal_selectors = [
                            "//div[contains(@class, 'paypal')]",
                            "//img[contains(@src, 'paypal')]",
                            "//div[contains(text(), 'PayPal')]",
                            "//button[contains(text(), 'Pay')]"
                        ]
                        
                        for selector in paypal_selectors:
                            try:
                                p.wait_for_selector(selector, timeout=5000)
                                log.info(f"✅ 找到PayPal页面特征元素: {selector}")
                                break
                            except TimeoutError:
                                continue
                        
                    except Exception as e:
                        log.warning(f"PayPal页面特征元素验证失败，但不影响主要功能: {str(e)}")
                    
                except TimeoutError:
                    log.error("等待跳转到PayPal页面超时")
                    log.info(f"当前URL: {p.url}")
                    # 即使超时，也检查当前URL
                    if "paypal.com" in p.url:
                        log.info("✅ 虽然等待超时，但URL已成功跳转到PayPal")
                    else:
                        raise
                except Exception as e:
                    log.error(f"PayPal页面验证失败: {str(e)}")
                    raise

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            # 添加调试信息
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                # 截图保存
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise
