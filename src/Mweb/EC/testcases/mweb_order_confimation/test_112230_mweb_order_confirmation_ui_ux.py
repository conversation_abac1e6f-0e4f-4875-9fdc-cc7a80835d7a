"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_112230_mweb_order_confirmation_ui_ux.py
@Description    :  Mobile订单成功页UI/UX验证
@CreateTime     :  2025/6/10 15:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/10 15:30
"""
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_ele.mweb_order_confirmation import mweb_order_confirmation_ele
from src.Mweb.EC.mweb_pages.mweb_page_order_confirmation.mweb_page_order_confirmation import MWebOrderComfirmationPage
from src.config.weee.log_help import log


@allure.story("Mobile-订单成功页样式验证")
class TestMWebOrderConfirmationUIUX:
    pytestmark = [pytest.mark.mweb_todo, pytest.mark.zhuli]
    @allure.title("Mobile-订单成功页样式验证")
    @pytest.mark.present
    def test_112230_mweb_order_confirmation_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112230】 Mobile-订单成功页样式验证
        
        测试步骤:
        1. 打开订单成功页
        2. 验证页面标题和订单号
        3. 验证订单状态信息
        4. 点击开始赚取积分按钮并验证分享功能
        5. 验证订单详情按钮并点击跳转
        6. 验证推荐商品区域
        这个账号暂时没有订单号没办法测试
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 1. 创建订单成功页面对象
        order_confirmation_page = MWebOrderComfirmationPage(p,h5_autotest_header,browser_context=c,page_url="/order/success/v2/69581331")
        log.info("成功加载订单确认页面")
        p.wait_for_timeout(3000)

        with allure.step("验证推荐商品区域"):
            # 使用封装的方法验证推荐商品区域
            recommendations_result = order_confirmation_page.verify_recommendations_section()

            # 记录验证结果
            log.info(f"推荐商品区域验证结果: {recommendations_result}")

            # 断言验证成功
            assert recommendations_result[
                "success"], f"推荐商品区域验证失败: {recommendations_result.get('details', {}).get('error', '未知错误')}"

            # 断言有推荐商品
            assert recommendations_result["details"].get("has_items", False), "推荐商品区域没有商品"

            # 记录详细信息
            if "section_title" in recommendations_result["details"]:
                log.info(f"推荐商品区域标题: {recommendations_result['details']['section_title']}")

            if "items_count" in recommendations_result["details"]:
                log.info(f"推荐商品数量: {recommendations_result['details']['items_count']}")

            if "first_item_name" in recommendations_result["details"]:
                log.info(f"第一个推荐商品名称: {recommendations_result['details']['first_item_name']}")

            if "first_item_price" in recommendations_result["details"]:
                log.info(f"第一个推荐商品价格: {recommendations_result['details']['first_item_price']}")
                assert recommendations_result["details"].get("price_has_dollar_sign", False), "商品价格格式不正确"

            if recommendations_result["details"].get("add_to_cart_visible", False):
                log.info("加入购物车按钮可见")

        p.wait_for_timeout(2000)

        with allure.step("点击开始赚取积分按钮并验证分享功能"):
            # 验证开始赚取积分按钮
            start_button =order_confirmation_page.page.get_by_test_id(mweb_order_confirmation_ele.ele_start_earning)
            expect(start_button).to_be_visible()
            button_text = start_button.text_content()
            log.info(f"开始赚取积分按钮: {button_text}")
            
            # 点击开始赚取积分按钮
            share_result = order_confirmation_page.start_earning()
            log.info(f"分享结果: {share_result}")
            
            # 验证分享结果
            assert share_result["success"], "分享功能验证失败"
            
            # 验证分享URL
            assert "/order/share/grocery/view/" in share_result.get("share_url", ""), "分享URL格式不正确"
            
            # 如果跳转到了分享页面，返回订单确认页
            if share_result.get("redirected", False):
                p.go_back()
                log.info("已从分享页面返回订单确认页")
                p.wait_for_timeout(2000)
        
        with allure.step("验证订单详情按钮并点击跳转"):
            # 验证订单详情按钮
            order_detail_btn = order_confirmation_page.page.get_by_test_id(mweb_order_confirmation_ele.ele_order_details)
            expect(order_detail_btn).to_be_visible()
            detail_btn_text = order_detail_btn.text_content()
            log.info(f"订单详情按钮: {detail_btn_text}")
            
            # 记录当前URL
            current_url = order_confirmation_page.page.url
            log.info(f"点击前URL: {current_url}")
            
            # 点击订单详情按钮
            order_detail_btn.click()
            p.wait_for_timeout(3000)
            
            # 验证是否跳转到订单详情页
            detail_page_url = order_confirmation_page.page.url
            log.info(f"点击后URL: {detail_page_url}")
            assert "/order/detail" in detail_page_url, "未跳转到订单详情页"
            
            # 验证订单详情页元素
            order_detail_page = p.get_by_test_id("order-detail-page")
            if order_detail_page.is_visible():
                log.info("成功跳转到订单详情页")
            else:
                log.info("订单详情页元素未找到，但URL已变更")
            
            # 返回订单成功页
            p.go_back()
            p.wait_for_timeout(3000)
            
            # 验证是否返回订单成功页
            assert p.url == current_url, "未成功返回订单成功页"
            log.info("成功返回订单成功页")
        

        log.info("Mobile订单确认页UI/UX验证测试完成")