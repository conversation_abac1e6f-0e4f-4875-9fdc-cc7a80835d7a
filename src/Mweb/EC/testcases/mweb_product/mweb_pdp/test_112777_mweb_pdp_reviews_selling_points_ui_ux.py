import allure
import pytest
import re

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【MWeb PDP】Reviews栏和卖点关键词UI/UX验证")
class TestMWebPDPReviewsSellingPointsUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【MWeb PDP】Reviews栏总review数显示验证")
    def test_112777_mweb_pdp_reviews_total_count_display(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        验证Reviews栏展示总review数, 本用例与IOS显示不同，没有ai, 热词，亮点这些部分
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 进入有reviews的PDP页面
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,
                               page_url="/product/DuoDuo-Vegetarian-Rice-Mushroom-Buns-Frozen-1/34460")
        p.wait_for_timeout(3000)

        if p.locator("//button[text()='Continue']").all():
            p.locator("//button[text()='Continue']").click()

        # 滚动到reviews模块
        scroll_one_page_until(p, mweb_pdp_ele.ele_mod_review)
        
        # 验证reviews总数显示
        review_count_text = pdp_page.check_reviews_total_count_display()
        
        # 验证总数格式正确（包含数字）
        match = re.search(r'(\d+)', review_count_text)
        assert match, f"Reviews总数格式不正确: {review_count_text}"
        
        review_count = int(match.group(1))
        assert review_count > 50, f"Reviews总数应该大于50: {review_count}"

        # 点击具体的review card
        p.get_by_test_id("wid-pdp-review-card").all()[0].click()
        p.wait_for_timeout(2000)
        assert p.locator("//div[text()='Add to cart']").all(), f"review list page should have add to cart button"

