import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【108701】 PDP-分享-商品图片分享UI/UX验证")
class TestMWebPDPProductImgShareUIUX:
    pytestmark = [pytest.mark.h5pdp,pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108701】 PDP-分享-商品图片分享UI/UX验证")
    def test_112063_mWeb_pdp_product_group_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108701】 PDP-分享-商品图片分享UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定pdp页面
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,
                               page_url="/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        if p.locator(u"//button[contains(text(), 'Continue')]").all():
            p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        scroll_one_page_until(p, mweb_pdp_ele.ele_share)
        # 点击分享按钮
        pdp_page.FE.ele(mweb_pdp_ele.ele_share).click()
        p.wait_for_timeout(3000)
        # 断言分享pop 弹出成功
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop), 'pdp点击分享未弹出pop'
        # 点击分享图片按钮
        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_img).click()
        p.wait_for_timeout(6000)
        # 断言分享图片pop 弹出成功
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop), 'pdp点击分享图片未弹出pop'
        # 断言分享图片中内容
        # 断言img pop title = Press and hold screen to save image
        img_header_title = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + "//span").text_content()
        assert img_header_title == "Press and hold screen to save image"

        # 断言头像信息
        image_avatar = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_avatar_')]")
        assert image_avatar
        # 断言name 信息
        img_name = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_avatarMessage')]/p[1]")
        assert img_name
        img_title = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_avatarMessage')]/p[2]").text_content()
        assert img_title == '"I found this great item on Weee!"'
        # 断言商品图片
        share_img = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//img[contains(@class,'shareImage_productImage')]")
        assert share_img
        # 断言销量
        weekly_sold = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_productWeeklySold')]//span")
        assert weekly_sold
        # 断言img product title
        product_title = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//h2[contains(@class,'shareImage_productTitle')]")
        assert product_title
        # 断言img product price
        price = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + "//span[contains(@class,'shareImage_price')]")
        assert price
        # 断言img product base price
        base_price = pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + "//span[contains(@class,'shareImage_basePrice')]")
        if base_price:
            # 断言off 标签存在
            img_off = pdp_page.FE.ele(
                mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_productDiscount')]")

            assert img_off
        # 断言二维码
        product_qrcode = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_productQRCode_')]//img")
        assert product_qrcode
        # 断言底部footer logo 信息
        weee_logo = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_footerLogo')]//img")
        assert weee_logo
        # 断言底部footer 信息
        weee_title = pdp_page.FE.ele(
            mweb_pdp_ele.ele_share_img_pop + "//div[contains(@class,'shareImage_bottomFooter')]//p").text_content()
        assert weee_title == "Asian Groceries"

        # 关闭img pop
        pdp_page.FE.ele(mweb_pdp_ele.ele_share_img_pop + "//i").click()

        # 关闭分享pop
        pdp_page.FE.ele(mweb_pdp_ele.ele_share_pop_close).click()
