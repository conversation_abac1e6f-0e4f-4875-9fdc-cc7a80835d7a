import allure
import pytest

from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证")
class TestMWebPDPSameVendorUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("[101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证")
    def test_mweb_pdp_same_vendor_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        [101480][mweb]-Global+商品-验证商品PDP-店铺推荐模块UI/UX验证
        测试步骤：
        1. 访问指定商品PDP页面
        2. 校验页面基本元素
        3. 滚动到店铺推荐模块
        4. 校验店铺推荐模块元素
        5. 验证商品信息（名称、价格）
        6. 测试收藏按钮功能
        7. 测试加购按钮功能
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 直接进入指定pdp页面
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,
                               page_url="/product/SK-II-Skinpower-Airy-Milky-Lotion/2038708")

        p.wait_for_timeout(5000)

        # 关闭可能的弹窗
        if p.locator("//button[contains(text(), 'Continue')]").all():
            p.locator("//button[contains(text(), 'Continue')]").click()

        # 2. 滚动到店铺推荐模块
        scroll_one_page_until(p, mweb_pdp_ele.ele_pdp_same_vendor_card)

        # 3. 校验店铺推荐模块存在
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), "店铺推荐模块不可见"

        # 4. 校验店铺推荐标题
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), "店铺推荐标题不可见"

        # 5. 校验查看全部按钮
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_view_all).is_visible(), "查看全部按钮不可见"

        # 6. 获取店铺推荐商品列表
        same_vendor_products = pdp_page.FE.eles(mweb_pdp_ele.ele_pdp_same_vendor_product_card)
        assert len(same_vendor_products) > 0, "店铺推荐商品列表为空"

        # 7. 验证每个商品的基本信息
        for index, product in enumerate(same_vendor_products[:3]):  # 只验证前3个商品
            # 7.1 验证商品名称
            product_name_ele = product.query_selector("div[data-testid='wid-product-card-title'] div")
            assert product_name_ele, f"第{index + 1}个商品名称元素不存在"
            product_name = product_name_ele.text_content()
            assert product_name, f"第{index + 1}个商品名称为空"

            # 7.2 验证商品价格
            product_price_ele = product.query_selector("div[data-testid='wid-product-card-price'] div div")
            assert product_price_ele, f"第{index + 1}个商品价格元素不存在"
            product_price = product_price_ele.text_content()
            assert product_price and product_price.startswith(
                "$"), f"第{index + 1}个商品价格格式不正确: {product_price}"

            # 7.3 验证商品图片
            product_img = product.query_selector("img[data-role='product-image']")
            assert product_img, f"第{index + 1}个商品图片不存在"

            # 7.4 验证配送信息
            ships_from = product.query_selector("div[data-testid='wid-product-card-ships-from']")
            assert ships_from, f"第{index + 1}个商品配送信息不存在"

            print(f"商品{index + 1} - 名称: {product_name}, 价格: {product_price}")

        # 8. 测试收藏按钮功能
        first_product = same_vendor_products[0]
        favorite_btn = first_product.query_selector("div[data-testid='btn-favorite']")
        assert favorite_btn, "收藏按钮不存在"
        assert favorite_btn.is_visible(), "收藏按钮不可见"

        # 9. 测试加购按钮功能
        atc_btn = first_product.query_selector("div[data-testid='btn-atc-plus']")
        assert atc_btn, "加购按钮不存在"

        # 点击加购按钮
        atc_btn.click()
        p.wait_for_timeout(2000)

        # 验证加购成功（可以通过购物车数量变化或其他方式验证）
        print("店铺推荐商品加购测试完成")

        # 10. 测试轮播功能
        swiper_container = pdp_page.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_swiper)
        assert swiper_container.is_visible(), "轮播容器不可见"

        # 简化的轮播测试 - 只验证容器存在和商品数量
        if len(same_vendor_products) > 1:
            print(f"轮播容器包含 {len(same_vendor_products)} 个商品，轮播功能可用")
            print("轮播功能验证完成")