import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
from src.Mweb.EC.mweb_pages.mweb_page_pdp.mweb_page_pdp import MWebPDPPage
from src.common.commonui import scroll_one_page_until


@allure.story("【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证")
class TestMWebPDPProductGroupUIUX:
    pytestmark = [pytest.mark.h5pdp, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证")
    def test_112063_mWeb_pdp_product_image_group_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                       h5_open_and_close_trace):
        """
        【112063】 H5-PDP-商品组-只有一个属性模块UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定pdp页面
        pdp_page = MWebPDPPage(p, h5_autotest_header, browser_context=c,
                               page_url="/product/Vita-Honey-Chrysanthemum-Tea-250ml-6/106422")
        p.wait_for_timeout(3000)
        # 关闭pop
        if p.locator(u"//button[contains(text(), 'Continue')]").all():
            p.locator(u"//button[contains(text(), 'Continue')]").click()

        # 滚动到指定位置
        scroll_one_page_until(p, mweb_pdp_ele.ele_product_group)
        # 断言product group 文案存在
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_select).is_visible()
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_title).text_content()
        # 获取product group 组件商品list
        product_group_list = pdp_page.FE.eles(mweb_pdp_ele.ele_product_group_img)
        len1 = len(product_group_list)
        print(len1)
        for index, item in enumerate(product_group_list):
            # 验证product_group 商品图片
            assert item.query_selector(u"//img[@alt='product group img']")

        # 点击product group，弹出pop
        pdp_page.FE.ele(mweb_pdp_ele.ele_product_group).click()
        assert pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_pop).is_visible()

        # 获取product group pop 里商品list
        pop_item_list = pdp_page.FE.eles(mweb_pdp_ele.ele_product_group_item_list)
        for index2, item2 in enumerate(pop_item_list):
            assert item2.query_selector(
                u"//div[contains(@class,'imageCard_top')]//img[@alt='image for unlogged in users']")
            assert item2.query_selector(u"//div[contains(@class,'imageCard_title')]")
            if item2.get_attribute('class') == 'swiper-slide swiper-slide-next':
                item2.click()
        # 关闭pop
        pdp_page.FE.ele(mweb_pdp_ele.ele_product_group_pop_close).click()
