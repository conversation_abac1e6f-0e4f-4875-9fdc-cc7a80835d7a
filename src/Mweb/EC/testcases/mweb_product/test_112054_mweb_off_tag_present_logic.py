import allure
import pytest

from playwright.sync_api import Page
from src.config.base_config import TEST_URL


@allure.story("【112054】 OFF 标签显示逻辑")
class TestWebOffTagPresentLogic:
    # pytestmark = [pytest.mark.mweb_regression]
    pytestmark = [pytest.mark.h5product, pytest.mark.regression, pytest.mark.transaction, pytest.mark.zhongyuan]

    @allure.title("【112054】 OFF 标签显示逻辑")
    @pytest.mark.h5home
    def test_112054_mweb_off_tag_present_logic(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【112054】 OFF 标签显示逻辑
        """
        p:Page = phone_page.get("page")
        p.goto(TEST_URL + "?joinEnki=true")
        p.wait_for_timeout(5000)
        if p.locator("//img[contains(@aria-label, 'close button')]").all():
            p.locator("//img[contains(@aria-label, 'close button')]").click()

        p.wait_for_timeout(2000)
        p.locator("//span[text()='Deals']").click()
        p.wait_for_timeout(3000)
        # deals分类里面大部分都是off标签和everyday value的商品
        all_off_tags = p.get_by_test_id("wid-product-card-container").locator("//span[contains(text(), 'off')]").all()
        assert all_off_tags, f"deals分类里面没有off标签的商品"
        # 进入商品详情页， 并判断该商品存在off标签
        all_off_tags[0].click()
        p.wait_for_timeout(3000)
        if p.locator("//button[text()='Continue']").all():
            p.locator("//button[text()='Continue']").click()
        assert p.locator("//span[contains(text(), 'off')]").all(), f"商品详情页没有off标签"
        # 将商品加入购物车
        if p.get_by_test_id("btn-add-cart").all():
            p.get_by_test_id("btn-add-cart").click()











