import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_product import mweb_topx_ele
from src.Mweb.EC.mweb_pages.mweb_page_topx.mweb_page_topx import MWebTOPXPage
from src.common.commonui import scroll_one_page_until


@allure.story("【106444】 topx分享流程验证")
class TestMWebTopxShareUIUX:
    pytestmark = [pytest.mark.h5topx, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【106444】 topx分享流程验证")
    def test_106444_mWeb_topx_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【106444】 topx分享流程验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 直接进入指定topx页面
        top_page = MWebTOPXPage(p, h5_autotest_header, browser_context=c,
                                page_url="/promotion/top-x/chart")
        p.wait_for_timeout(3000)
        # 关闭Continue pop
        # p.locator(u"//button[contains(text(), 'Continue')]").click()
        # 滚动到指定位置-分享按钮
        scroll_one_page_until(p, mweb_topx_ele.ele_share_topx)

        # 点击分享按钮
        p.get_by_test_id("btn-share").click()
        p.wait_for_timeout(3000)
        # 断言分享pop 弹出成功
        assert top_page.FE.ele(mweb_topx_ele.ele_share_topx_pop).is_visible(), 'topx点击分享未弹出pop'
        # 断言分享pop title
        assert top_page.FE.ele(mweb_topx_ele.ele_share_pop_title).text_content() == "Share"
        # 断言分享商品小图
        assert top_page.FE.ele(mweb_topx_ele.ele_share_pop_topx_img + "//img").is_visible()
        # 断言分享pop 商品title
        pop_product_title = top_page.FE.ele(
            mweb_topx_ele.ele_share_pop_topx_img + "//following-sibling::div//div[1]").text_content()
        assert pop_product_title
        # 断言分享pop 商品子title
        pop_product_sub_title = top_page.FE.ele(
            mweb_topx_ele.ele_share_pop_topx_img + "//following-sibling::div//div[2]").text_content()
        assert pop_product_sub_title == "Ranking charts updated daily"
        # 断言分享语言

        share_lang_text = p.locator(mweb_topx_ele.ele_share_pop_topx_lan).all_text_contents()
        assert {"English", "简", "繁", "한국어", "日本語", "Tiếng Việt"} == set(share_lang_text)
        # 切换分享语言
        share_lang = top_page.FE.eles(mweb_topx_ele.ele_share_pop_topx_lan)
        for item in share_lang:
            # 切换语言
            item.click()
        # 断言分享方式
        assert top_page.FE.ele(mweb_topx_ele.ele_share_pop_copy_link).is_visible()
        assert top_page.FE.ele(mweb_topx_ele.ele_share_pop_copy_link + "//span").text_content() == "Copy link"

        # 点击copy link
        top_page.FE.ele(mweb_topx_ele.ele_share_pop_copy_link).click()
        # 断言pop 关闭
        assert not top_page.FE.ele(mweb_topx_ele.ele_share_topx_pop)
