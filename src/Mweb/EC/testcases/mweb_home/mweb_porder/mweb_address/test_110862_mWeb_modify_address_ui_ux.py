import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_porder.mweb_address import mweb_address_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_porder.mweb_page_address.mweb_page_address import MWebPageAddress
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log

@allure.story("[110862]首页/checkout/acccount/order detail-编辑&应用地址验证")
class TestMWebModifyAddressUIUX():
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    
    def _edit_address_info(self, p: Page, address_page: MWebPageAddress, first_name="Modified", last_name="Automation", phone="9876543210", note="h5 UI自动化"):
        """
        编辑地址信息的公共方法
        
        Args:
            p: Page对象
            address_page: MWebPageAddress对象
            first_name: 名字，默认为"Modified"
            last_name: 姓氏，默认为"Automation"
            phone: 电话，默认为"9876543210"
            note: 备注，默认为"h5 UI自动化"
        """
        log.info("编辑地址信息")
        # 验证Delivery Info弹窗显示
        assert p.get_by_test_id(mweb_address_ele.address_first_name).is_visible(), "Delivery Info弹窗未显示"
        
        # 修改姓名和电话
        first_name_input = p.get_by_test_id(mweb_address_ele.address_first_name)
        first_name_input.clear()
        first_name_input.fill(first_name)
        
        last_name_input = p.get_by_test_id(mweb_address_ele.address_last_name)
        last_name_input.clear()
        last_name_input.fill(last_name)
        
        phone_input = p.get_by_test_id(mweb_address_ele.address_phone)
        phone_input.clear()
        phone_input.fill(phone)
        
        # 添加备注
        note_input = p.get_by_test_id(mweb_address_ele.address_note)
        note_input.clear()
        note_input.fill(note)
        
        # 验证街道、城市、zipcode已自动填充
        assert p.get_by_test_id(mweb_address_ele.address_street).input_value() != "", "街道未自动填充"
        assert p.locator(mweb_address_ele.address_city).input_value() != "", "城市未自动填充"
        assert p.get_by_test_id(mweb_address_ele.address_zipcode).input_value() != "", "邮编未自动填充"
        
        # 点击保存
        p.get_by_test_id(mweb_address_ele.address_save_button).click()
        p.wait_for_timeout(2000)

    @allure.title("【110862】 从首页-编辑地址验证")
    def test_110862_mWeb_modify_address_from_home_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110862】 从首页-编辑地址验证
        1. 点击首页zipcode data-testid="wid-home-zipcode-modal"
        2. 验证弹出地址选择弹窗 data-testid="wid-delivery-to-pop"
        3. 判断是否有地址卡片 data-testid="wid-address-card"
        4. 如果有地址，点击编辑按钮 data-testid="btn-edit-address"
        4.1 进入 Delivery Info 新增地址pop
        4.2 按照页面修改地址 姓名、电话、备注
        4.3 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        4.4 点击保存 data-testid="btn-save-address"，回到首页
        5. 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c)

        # 1. 点击首页zipcode
        log.info("步骤1：点击首页zipcode")
        p.get_by_test_id(mweb_address_ele.home_zipcode_modal).click()
        p.wait_for_timeout(1000)

        # 2. 验证弹出地址选择弹窗
        log.info("步骤2：验证弹出地址选择弹窗")
        assert p.get_by_test_id(mweb_address_ele.delivery_to_pop).is_visible(), "地址选择弹窗未显示"

        # 3. 判断是否有地址卡片
        log.info("步骤3：判断是否有地址卡片")
        try:
            has_address = p.get_by_test_id(mweb_address_ele.address_card).is_visible(timeout=3000)
        except:
            has_address = False

        if has_address:
            # 4. 有地址，点击编辑
            log.info("步骤4：有地址，点击编辑")
            edit_btn = p.get_by_test_id(mweb_address_ele.edit_address_button)
            assert edit_btn.is_visible(), "编辑按钮不可见"
            edit_btn.click()
            p.wait_for_timeout(2000)

            # 4.1-4.4 编辑地址信息并保存
            log.info("步骤4.1-4.4：编辑地址信息并保存")
            self._edit_address_info(p, address_page)
            assert p.get_by_test_id(mweb_address_ele.home_main_banner).is_visible(), "未成功回到首页"
            log.info("从首页编辑地址测试完成")
        else:
            log.info("没有地址可编辑，跳过测试")
            close_btn = p.get_by_test_id(mweb_address_ele.close_delivery_dialog)
            if close_btn.is_visible():
                close_btn.click()
                p.wait_for_timeout(1000)

    @allure.title("【110862】 从Account页-设置页面-编辑地址验证")
    def test_110862_mWeb_modify_address_from_account_setting_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110862】 从Account页-设置页面-编辑地址验证
        1. 点击我的icon data-testid="wid-profile"进入account页面
        2. 点击设置 data-testid="wid-settings"进入设置页面
        3. 点击address book data-testid="wid-address-book"进入地址簿页面
        4. 如果有地址 data-testid="wid-address-item"，点击第一个地址的编辑按钮 data-testid="btn-edit-address"
        4.1 进入 Delivery Info 新增地址pop
        4.2 按照页面修改地址 姓名、电话、备注
        4.3 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        4.4 点击保存 data-testid="btn-save-address"，弹出是否更换地址pop data-testid="wid-modal-confirm"
        4.5 点击取消 data-testid="btn-confirm-close"，回到 address book页面
        4.6 再次点击编辑按钮 data-testid="btn-edit-address"
        4.7 重复4.2-4.4步骤
        4.8 点击确认更换按钮 data-testid="btn-confirm-ok"，回到 address book页面
        4.9 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c)
        p.wait_for_timeout(3000)
        address_page.delete_address_from_home()

        # 1. 点击我的icon进入account页面
        log.info("步骤1：点击我的icon进入account页面")
        p.get_by_test_id(mweb_address_ele.profile_icon).click()
        p.wait_for_timeout(1000)
        assert p.get_by_test_id(mweb_address_ele.my_orders_link).is_visible(), "未成功进入account页面"
        
        # 2. 点击设置进入设置页面
        log.info("步骤2：点击设置进入设置页面")
        p.get_by_test_id(mweb_address_ele.settings_link).click()
        p.wait_for_timeout(1000)
        
        # 3. 点击address book进入地址簿页面
        log.info("步骤3：点击address book进入地址簿页面")
        p.get_by_test_id(mweb_address_ele.address_book_link).click()
        p.wait_for_timeout(1000)
        
        # 4. 判断是否有地址
        log.info("步骤4：判断是否有地址")
        has_address = p.get_by_test_id(mweb_address_ele.address_item).is_visible()
        
        if has_address:
            # 4.1 点击第一个地址的编辑按钮
            log.info("步骤4.1：点击第一个地址的编辑按钮")
            address_items = p.get_by_test_id(mweb_address_ele.address_item).all()
            first_address = address_items[0]
            first_address.get_by_test_id(mweb_address_ele.btn_edit_address).click()
            p.wait_for_timeout(2000)
            
            # 4.2-4.11 编辑地址信息，先取消再确认
            log.info("步骤4.2-4.11：编辑地址信息，先取消再确认")
            self._edit_address_info(p, address_page, first_name="Modified", last_name="Account", phone="**********", note="从Account设置页编辑")
            
            # 验证确认弹窗并点击取消
            if p.get_by_test_id(mweb_address_ele.confirm_modal).is_visible(timeout=3000):
                p.get_by_test_id(mweb_address_ele.btn_confirm_close).click()
                p.wait_for_timeout(1000)
            
            # 再次编辑并确认
            first_address.get_by_test_id(mweb_address_ele.btn_edit_address).click()
            p.wait_for_timeout(2000)
            self._edit_address_info(p, address_page, first_name="Final", last_name="Test", phone="**********", note="最终编辑")
            
            # 点击确认
            if p.get_by_test_id(mweb_address_ele.confirm_modal).is_visible(timeout=3000):
                p.get_by_test_id(mweb_address_ele.btn_confirm_ok).click()
                p.wait_for_timeout(2000)
            
            log.info("从Account设置页编辑地址测试完成")
        else:
            log.info("没有地址可编辑，跳过测试")

    @allure.title("【110841】 从结算页面-编辑地址验证")
    def test_110862_mWeb_modify_address_from_checkout_page_ui_ux(self, phone_page: dict,
                                                          h5_autotest_header,
                                                          h5_open_and_close_trace):
        """
        【110841】 从结算页面-编辑地址验证
        1. 点击购物车按钮data-testid="wid-cart"进入购物车页面
        2.如果购物车为空，滑动到购物车下方推荐模块data-testid="wid-cart-recommend-tab-perference_cart"，加购3件商品data-testid="btn-atc-plus"
        4.如果有商品，直接点击结算按钮 data-testid="btn-checkout"
        5.如果有中间页，点击continue data-testid="btn-select-all-carts"
        6.点击 继续解释 data-testid="btn-select-cart-checkout"
        7.如果有upsell页面pop data-testid="wid-popup-checkoutupsell"，
        8.点击continue data-testid="btn-continue"
        9.进入结算页，点击地址选择区域 data-testid="wid-checkout-reminder-content"
        10. 进入地址簿页面，如果有地址wid-address-item，点击编辑按钮data-testid="btn-edit-address"
        11.进入 Delivery Info 新增地址pop
        12.按照页面修改地址 姓名、电话、备注
        13. 其中街道、城市、zipcode 会默认已经填上了，此处可以不做修改
        14.点击保存btn-save-address，弹出是否更换地址pop data-testid="wid-modal-confirm"
        15.点击取消，回到 address book页面
        16.再次点击编辑按钮data-testid="btn-edit-address"
        17.重复12-14步骤
        18.点击确认更换按钮data-testid="btn-confirm-ok"，回到结算页data-testid="btn-checkout"
        19. 如果没有地址，跳过（已有新增地址的case）
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="")
        address_page = MWebPageAddress(p, h5_autotest_header, browser_context=c)

        # 1. 点击购物车按钮进入购物车页面
        log.info("步骤1：点击购物车按钮进入购物车页面")
        p.get_by_test_id("wid-cart").click()
        p.wait_for_timeout(2000)

        # 2. 检查购物车是否为空，如果为空则添加商品
        log.info("步骤2：检查购物车状态")
        if p.get_by_test_id("btn-cart-start-shopping").is_visible(timeout=3000):
            log.info("购物车为空，添加推荐商品")
            scroll_one_page_until(p, "wid-cart-recommend-tab-perference_cart")
            cart_page.add_recommend_product_product(2)

        # 4. 点击结算按钮
        log.info("步骤4：点击结算按钮")
        p.get_by_test_id("btn-checkout").click()
        p.wait_for_timeout(2000)

        # 5. 处理中间页
        log.info("步骤5：处理中间页")
        if p.get_by_test_id("btn-select-all-carts").is_visible(timeout=3000):
            p.get_by_test_id("btn-select-all-carts").click()
            p.wait_for_timeout(1000)
            # 6. 点击继续结算
            p.get_by_test_id("btn-select-cart-checkout").click()
            p.wait_for_timeout(2000)

        # 7-8. 处理upsell页面
        log.info("步骤7-8：处理upsell页面")
        if p.get_by_test_id("wid-popup-checkoutupsell").is_visible(timeout=3000):
            p.get_by_test_id("btn-continue").click()
            p.wait_for_timeout(2000)

        # 9. 进入结算页，点击地址选择区域
        log.info("步骤9：点击地址选择区域")
        if p.get_by_test_id("mod-checkout-delivery-info-content").is_visible(timeout=5000):
            p.get_by_test_id("mod-checkout-delivery-info-content").click()
            p.wait_for_timeout(2000)

            # 10. 检查是否有地址
            if p.get_by_test_id("wid-address-item").is_visible(timeout=3000):
                log.info("步骤10-18：编辑现有地址")
                # 点击编辑按钮
                p.get_by_test_id("btn-edit-address").click()
                p.wait_for_timeout(2000)

                # 12-14. 编辑地址信息并保存
                log.info("步骤12-14：编辑地址信息并保存")
                self._edit_address_info(p, address_page, first_name="Checkout", last_name="Edit", phone="5555555555", note="从结算页编辑")

                # 15. 点击取消
                log.info("步骤15：点击取消")
                if p.get_by_test_id("wid-modal-confirm").is_visible(timeout=3000):
                    p.get_by_test_id("btn-confirm-close").click()
                    p.wait_for_timeout(1000)

                # 16. 再次编辑
                log.info("步骤16：再次点击编辑按钮")
                p.get_by_test_id("btn-edit-address").click()
                p.wait_for_timeout(2000)
                
                # 17. 重复编辑步骤
                log.info("步骤17：重复编辑步骤")
                self._edit_address_info(p, address_page, first_name="Final", last_name="Checkout", phone="6666666666", note="最终结算页编辑")

                # 18. 点击确认
                log.info("步骤18：点击确认更换按钮")
                if p.get_by_test_id("wid-modal-confirm").is_visible(timeout=3000):
                    p.get_by_test_id("btn-confirm-ok").click()
                    p.wait_for_timeout(2000)

                # 验证回到结算页
                assert p.get_by_test_id("btn-checkout").is_visible(timeout=5000), "未成功回到结算页"
                log.info("从结算页编辑地址测试完成")
            else:
                # 19. 如果没有地址，跳过
                log.info("步骤19：没有地址可编辑，跳过测试（已有新增地址的case）")
        else:
            log.info("未找到地址选择区域，跳过测试")