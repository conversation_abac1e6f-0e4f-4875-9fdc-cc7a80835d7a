import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele import mweb_common_ele
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.api.porder import update_zipcode_v1
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log


@allure.story("【108558】 购物车页面-切换日期验证")
class TestMWebChangeDeliveryDateOnCartPageUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108558】 购物车页面-切换日期验证")
    def test_108558_mWeb_change_delivery_date_on_cart_page_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108558】 购物车页面-切换日期验证
        测试步骤：
        1、进入购物车页面
        3、如果购物车为空，从推荐模块加购商品
        4、检查切换日期入口：data-testid="btn-change-delivery-date"
        5、点击切换日期按钮
        5、弹出切换日期pop，data-testid="wid-navbar"
        6、获取日期 data-testid="wid-delivery-date-item-content"
        6、随机点击pop里的日期进行切换日期
        7、验证购物车日期切换成功，验证回到购物车，data-testid="btn-change-delivery-date"
        """
        import random
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        
        # 1. 进入购物车页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(3000)
        log.info("成功进入购物车页面")
        
        # 3. 如果购物车为空或者没有normal购物车，从推荐模块加购商品
        if p.get_by_test_id("btn-cart-start-shopping").is_visible() or not p.get_by_test_id("wid-cart-normal").is_visible():
            # 滚动到指定位置
            scroll_one_page_until(p, p.get_by_test_id("wid-cart-recommend-tab-perference_cart"))
            # 加购4件猜你喜欢商品
            cart_page.add_recommend_product_product(4)
            # 滚动回购物车顶部
            scroll_one_page_until(p, p.get_by_test_id("wid-page-nav-header-title"))
            p.wait_for_timeout(2000)
            # 验证商品已加入购物车
            cart_items = p.get_by_test_id("wid-cart-normal").get_by_test_id("wid-product-card-container").all()
            assert len(cart_items) > 0, "加购商品后购物车仍为空"
            log.info(f"购物车现有{len(cart_items)}件商品")

        p.wait_for_timeout(2000)
        
        # 4. 检查切换日期入口
        delivery_date_btn = p.get_by_test_id("btn-change-delivery-date")
        assert delivery_date_btn.is_visible(), "购物车页面切换日期按钮未显示"
        
        # 获取当前显示的配送日期文本
        current_date_text = delivery_date_btn.text_content()
        log.info(f"当前配送日期: {current_date_text}")
        
        # 5. 点击切换日期按钮
        delivery_date_btn.click()
        p.wait_for_timeout(2000)
        log.info("点击切换日期按钮")
        
        # 5. 弹出切换日期pop
        navbar = p.get_by_test_id("wid-navbar")
        assert navbar.is_visible(), "切换日期弹窗未显示"
        log.info("成功弹出切换日期弹窗")
        
        # 6. 获取日期并随机点击
        date_contents = p.get_by_test_id("wid-delivery-date-item-content").all()
        assert len(date_contents) > 0, "没有可选的配送日期"
        
        # 随机选择一个日期
        selected_date = random.choice(date_contents)
        selected_date_text = selected_date.text_content()
        log.info(f"随机选择配送日期: {selected_date_text}")
        
        selected_date.click()
        p.wait_for_timeout(3000)
        
        # 7. 验证购物车日期切换成功
        updated_date_btn = p.get_by_test_id("btn-change-delivery-date")
        assert updated_date_btn.is_visible(), "返回购物车页面失败"
        
        updated_date_text = updated_date_btn.text_content()
        log.info(f"更新后的配送日期: {updated_date_text}")
        
        log.info("购物车页面切换日期验证成功")


