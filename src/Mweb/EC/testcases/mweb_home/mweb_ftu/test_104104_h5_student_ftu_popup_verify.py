# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/7/18
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home_ftu import MWebTimeBannerPage
from playwright.sync_api import Page
from src.Mweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("mweb-student-ftu pop up UI/UX验证")
class TestMwebTimebanner:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-student-ftu pop up UI/UX验证")
    @pytest.mark.ftu
    def test_104104_pc_student_ftu_popup_verify(self, h5_autotest_header, not_login_phone_page,page_url: str = "student"):
        "dweb-student-ftu pop up UI/UX验证"
        p: Page = not_login_phone_page.get("page")
        c = not_login_phone_page.get("context")
        h5_home_page = MWebTimeBannerPage(p, h5_autotest_header, c)
        p.goto(TEST_URL + "/" + page_url)
        p.wait_for_timeout(10000)

        with allure.step("验证FTU Popup是否存在"):
            if h5_home_page.m_verify_student_ftu_visibility():
                log.info("FTU Popup验证成功")
            else:
                log.info("FTU Popup验证失败,studentpopup不存在")