# -*- coding: utf-8 -*-
"""
<AUTHOR>  yue.wang
@Version        :  V1.0.0
------------------------------------
@Description    :  
@CreateTime     :  2025/6/16
@Software       :  PyCharm
------------------------------------
"""
import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home_ftu import MWebTimeBannerPage
from playwright.sync_api import Page
from src.Mweb.EC.conftest import ReportPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
@allure.story("mweb-注册-timebar-未登录用户time bar的功能验证")
class TestMwebTimebanner:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_wangyue]

    @allure.title("mweb-注册-timebar-未登录用户time bar的功能验证")
    @pytest.mark.time_banner
    def test_110289_mweb_unlogin_timebanner_click_verify(self, h5_autotest_header, not_login_phone_page):
        "mweb-注册-timebar-未登录用户time bar的功能验证"
        p: Page = not_login_phone_page.get("page")
        c = not_login_phone_page.get("context")
        h5_home_page = MWebTimeBannerPage(p, h5_autotest_header, c)
        p.goto(TEST_URL)
        p.wait_for_timeout(10000)

        # 1.验证FTU popup是否存在

        with allure.step("验证FTU Popup是否存在"):
            if h5_home_page.m_verify_ftu_visibility():
                h5_home_page.m_close_ftu()
            else:
                log.info("FTU Popup 不存在")

        # 步骤2：验证Time Banner是否存在
        with allure.step("验证Time Banner是否存在"):
            try:
                h5_home_page.m_verify_time_banner_visibility()
                # Time Banner存在，执行点击和关闭操作
                with allure.step("点击Time Banner"):
                    h5_home_page.m_click_time_banner()
                    h5_home_page.m_close_ftu()

                with allure.step("关闭Time Banner"):
                    h5_home_page.m_close_time_banner()

                # 验证Time Banner是否已关闭
                with allure.step("验证Time Banner已关闭"):
                    h5_home_page.m_verify_time_banner_closed()
            except AssertionError:
                log.info("Time Banner 不存在")
                allure.step("Time Banner 不存在，跳过后续操作")

        log.info("Time Banner验证完成")




