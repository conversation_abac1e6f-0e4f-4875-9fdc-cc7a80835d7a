
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home.mweb_home_ele import ele_h5_everyday_value
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations


class TestEDLP:
    # 验证mweb 首页，分类，pdp ，搜索结果页面一级页面对应edlp 标签展示
    def test_mweb_home_edlp_ui(self, phone_page: dict, h5_autotest_header):
        p: Page = phone_page.get('page')
        p.goto("https://www.sayweee.com/en")
        _visible = None

        for i in range(20):
            PageH5CommonOperations(p, h5_autotest_header).scroll_one_page(1)
            _visible = p.locator(ele_h5_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")

    def test_mweb_pdp_edlp_ui(self,phone_page:dict,h5_autotest_header):
        # PDP页面edlp 标签展示验证
        p: Page = phone_page.get('page')
        p.goto("https://www.sayweee.com/en/product/LQQM-Puff-Paratha-Chive-Onion-Flatbread-10pc--Frozen-1/94776")
        _visible = None

        for i in range(20):
            PageH5CommonOperations(p, h5_autotest_header).scroll_one_page(1)
            _visible = p.locator(ele_h5_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")

    def test_mweb_search_result_edlp_ui(self, phone_page: dict, h5_autotest_header):
        # category页面 edlp 标签展示验证
        p: Page = phone_page.get('page')
        p.goto("https://www.sayweee.com/en/category/trending?filter_sub_category=trending")
        _visible = None

        for i in range(20):
            PageH5CommonOperations(p, h5_autotest_header).scroll_one_page(1)
            _visible = p.locator(ele_h5_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")

    def test_mweb_category_edlp_ui(self, phone_page: dict, h5_autotest_header):
        #搜索结果页对应edlp 标签展示验证
        p: Page = phone_page.get('page')
        p.goto("https://www.sayweee.com/en/search?keyword=egg&trigger_type=%22search_active%22")
        _visible = None

        for i in range(20):
            PageH5CommonOperations(p, h5_autotest_header).scroll_one_page(1)
            _visible = p.locator(ele_h5_everyday_value).all()
            if _visible:
                break

        if not _visible:
            pytest.skip("home page no edlp, pls check manually!!!")


