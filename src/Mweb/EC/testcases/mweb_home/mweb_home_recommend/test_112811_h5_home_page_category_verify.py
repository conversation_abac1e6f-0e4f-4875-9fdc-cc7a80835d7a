import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome as PH5
from src.config.weee.log_help import log


@allure.story("H5-首页分类验证")
class TestH5HomePageCategoryVerify:
    pytestmark = [pytest.mark.mweb_regression]
    @allure.title("H5-首页分类验证")
    @pytest.mark.h5home
    def test_112811_h5_home_page_category_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        _page:Page = phone_page["page"]
        _context = phone_page["context"]
        ph5 = PH5(_page, h5_autotest_header, _context)
        # 首页category存在
        assert _page.get_by_test_id("wid-home-collection-cm_categories").is_visible()
        # 断言每行显示5个分类, 只能断言10个，页面上布局代码未分2个div
        # assert len(_page.get_by_test_id("wid-home-collection-cm_categories").locator("//./div[contains(@class, 'grid')]/div").all()) == 10
        assert len(_page.get_by_test_id("wid-home-collection-cm_categories").locator("xpath=/div[contains(@class, 'grid')]/div").all()) == 10
        # 点击...拉起弹框
        _page.get_by_test_id("wid-view-more").click()
        _page.wait_for_timeout(3000)
        assert _page.locator("#popup-categories-header").is_visible()
        assert _page.locator("#popup-categories-header").locator("xpath=//h2[text()='Categories']").is_visible()
        # 判断弹框内的分类数量大于12
        assert len(_page.get_by_test_id("wid-categories-item").all()) > 12
        # 回到首页
        _page.get_by_test_id("btn-modal-close").click()
        # 点击每个分类
        all_categories = _page.locator("a[data-testid*='wid-categories-item']").all()[3:]
        for category in all_categories:
            log.info("category is: " + category.get_attribute("aria-label"))
            category_aria_label = category.get_attribute("aria-label")
            category.click()
            _page.wait_for_timeout(3000)
            # global+首页不滚动，没有商品显示
            if "Global+" not in category_aria_label:
                assert _page.get_by_test_id("wid-product-card-container").all(), f"{category}分类下没有商品"
            # global+首页有reminder，点击关闭
            else:
                if _page.locator("//div[@id='reminderContainer']//i").all():
                    _page.locator("//div[@id='reminderContainer']//i").click()
            _page.wait_for_timeout(2000)
            _page.go_back()
            _page.wait_for_timeout(6000)


