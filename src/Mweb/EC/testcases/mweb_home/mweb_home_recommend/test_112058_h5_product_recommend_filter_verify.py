import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_explore_page.mweb_page_explore import MWebPageExplore
from src.api.zipcode import switch_zipcode
from src.config.weee.log_help import log


@allure.story("H5-商品推荐规则filter 查询验证")
class TestH5ProductRecommendFilterVerify:
    # todo 新的enki样式暂不支持，以后修改
    pytestmark = [pytest.mark.mweb_regression]
    @allure.title("H5-deals商品推荐规则filter 查询验证")
    @pytest.mark.h5home
    def _test_112058_deals_h5_product_recommend_filter_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        _page:Page = phone_page["page"]
        _context = phone_page["context"]
        explore_page = MWebPageExplore(_page, h5_autotest_header, _context)

        # 如果zipcode不是98011， 则切换到98011
        if not _page.locator("//span[text()='98011']").all():
            switch_zipcode(h5_autotest_header, "98011")
            _page.reload()
        # 切换到explore页面
        _page.get_by_test_id("btn-navi-explore").click()
        # 1. sort
        # 点击Deals（默认就是Deals）
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()

        # 点击featured (default)
        explore_page.search_product_by_sort("btn-sort-recommend")
        self._filter_assertion(_page)

        # 点击Deals（默认就是Deals）
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()

        # 点击Best Selling
        explore_page.search_product_by_sort("btn-sort-sold_count")
        self._filter_assertion(_page)

        # 点击Deals（默认就是Deals）
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()

        # 点击 price low to high
        explore_page.search_product_by_sort("btn-sort-price_asc")
        self._filter_assertion(_page)

        # 点击Deals（默认就是Deals）
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()

        # 点击 price high to low
        explore_page.search_product_by_sort("btn-sort-price_desc")
        self._filter_assertion(_page)

        # 2. filter
        # 点击Deals（默认就是Deals） local delivery
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_local")
        self._filter_assertion(_page)

        # 点击Deals（默认就是Deals） pantry
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_pantry")
        self._filter_assertion(_page)

        # 点击Deals（默认就是Deals） global+
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_global")
        self._filter_assertion(_page)

        # 3. product_type -> Deals
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_sale")
        self._filter_assertion(_page)

        # product_type -> New arrival
        _page.get_by_test_id("btn-main-category-sale").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_new")
        self._filter_assertion(_page)


    @allure.title("H5-new arrivals商品推荐规则filter 查询验证")
    @pytest.mark.h5home
    def _test_112058_new_h5_product_recommend_filter_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        _page:Page = phone_page["page"]
        _context = phone_page["context"]
        explore_page = MWebPageExplore(_page, h5_autotest_header, _context)
        # 切换到explore页面
        _page.get_by_test_id("btn-navi-explore").click()
        # 1. sort
        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        # 点击featured (default)
        explore_page.search_product_by_sort("btn-sort-recommend")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"

        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        # 点击Best Selling
        explore_page.search_product_by_sort("btn-sort-sold_count")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"

        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        # 点击 price low to high
        explore_page.search_product_by_sort("btn-sort-price_asc")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        # 点击 price high to low
        explore_page.search_product_by_sort("btn-sort-price_desc")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # 2. filter
        # 点击New arrivals local delivery
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_local")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # 点击New arrivals pantry
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_pantry")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # 点击New arrivals global+
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_global")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # 3. product_type -> Deals
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_sale")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"


        # product_type -> New arrival
        _page.get_by_test_id("btn-main-category-new").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_new")
        self._filter_assertion(_page)
        assert _page.locator("//span[text()='New']").all(), f"页面上没有New标签的商品"

    @allure.title("H5-bestsellers商品推荐规则filter 查询验证")
    @pytest.mark.h5home
    def _test_112058_bestsellers_h5_product_recommend_filter_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        _page:Page = phone_page["page"]
        _context = phone_page["context"]
        explore_page = MWebPageExplore(_page, h5_autotest_header, _context)
        # 切换到explore页面
        _page.get_by_test_id("btn-navi-explore").click()
        # 1. sort
        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        # 点击featured (default)
        explore_page.search_product_by_sort("btn-sort-recommend")
        self._filter_assertion(_page)

        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        # 点击Best Selling
        explore_page.search_product_by_sort("btn-sort-sold_count")
        self._filter_assertion(_page)

        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        # 点击 price low to high
        explore_page.search_product_by_sort("btn-sort-price_asc")
        self._filter_assertion(_page)


        # 点击New arrivals
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        # 点击 price high to low
        explore_page.search_product_by_sort("btn-sort-price_desc")
        self._filter_assertion(_page)


        # 2. filter
        # 点击New arrivals local delivery
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_local")
        self._filter_assertion(_page)


        # 点击New arrivals pantry
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_pantry")
        self._filter_assertion(_page)


        # 点击New arrivals global+
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        explore_page.search_product_by_filter("btn-filter-delivery_type-delivery_type_global")
        self._filter_assertion(_page)


        # 3. product_type -> Deals
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_sale")
        self._filter_assertion(_page)


        # product_type -> New arrival
        _page.get_by_test_id("btn-main-category-trending").all()[1].click()
        explore_page.search_product_by_product_type("btn-filter-product_type-product_type_new")
        assert _page.get_by_test_id("mod-category-product-list").is_visible()

    def _filter_assertion(self, _page: Page):
        """
        断言每个filter的商品列表是否有商品
        """
        assert _page.get_by_test_id("mod-category-product-list").is_visible()
        assert _page.get_by_test_id("wid-product-card-container").all(), f"页面上没有商品"








