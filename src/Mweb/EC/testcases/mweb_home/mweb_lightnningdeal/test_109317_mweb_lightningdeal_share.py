# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_109317_mweb_lightningdeal_share.py
@Description    :  移动端秒杀页面分享功能测试
@CreateTime     :  2025/7/14 03:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/14 03:35
"""

import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_home import mweb_lightningdeal_ele
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_lightningdeal.mweb_page_lightningdeal import MWebLightningDealPage
from src.common.commonui import scroll_one_page_until


@allure.story("【109317】 Lightning Deal-分享-页面分享流程验证")
class TestMWebLightningDealShareUIUX:
    pytestmark = [pytest.mark.h5lightningdeal, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【109317】 Lightning Deal-分享-页面分享流程验证")
    def test_109317_mweb_lightningdeal_share_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【109317】 Lightning Deal-分享-页面分享流程验证
        测试步骤：
        1. 打开秒杀页面 /promotion/lightning-deals
        2. 进入秒杀页面后判断是否有秒杀商品 data-testid="wid-product-card-container"
        3. 如果有秒杀商品就会有分享按钮，data-testid="btn-share"
        4. 点击分享按钮，拉起分享pop，data-testid="mod-share-popup"
        5. 获取分享语言位 data-testid="btn-share-lang-zh"，这个是包含前面 btn-share-lang-
        6. 根据获取到的语言位点击切换语言位
        7. 点击复制链接按钮 data-testid="btn-share-method-copyLink"
        8. 复制链接后pop自动关闭，回到秒杀页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 步骤1: 打开秒杀页面
        with allure.step("步骤1: 打开秒杀页面"):
            lightning_deal_page = MWebLightningDealPage(p, h5_autotest_header, browser_context=c,
                                                       page_url="/promotion/lightning-deals")
            p.wait_for_timeout(3000)

            # 关闭Continue pop
            if p.locator(u"//button[contains(text(), 'Continue')]").all():
                p.locator(u"//button[contains(text(), 'Continue')]").click()

            # 等待页面加载完成
            p.wait_for_load_state("networkidle", timeout=60000)

        # 步骤2: 判断是否有秒杀商品
        with allure.step("步骤2: 判断是否有秒杀商品"):
            product_cards = p.get_by_test_id("wid-product-card-container").all()
            if not product_cards:
                pytest.skip("当前没有秒杀商品，跳过分享流程测试")

            assert len(product_cards) > 0, "秒杀页面没有找到商品卡片"
            print(f"找到 {len(product_cards)} 个秒杀商品")

        # 步骤3: 检查分享按钮
        with allure.step("步骤3: 检查分享按钮"):
            share_button = p.get_by_test_id("btn-share")
            if not share_button.is_visible():
                pytest.skip("分享按钮不可见，跳过分享流程测试")

            assert share_button.is_visible(), "分享按钮不可见"

        # 步骤4: 点击分享按钮，拉起分享弹窗
        with allure.step("步骤4: 点击分享按钮，拉起分享弹窗"):
            share_button.click()
            p.wait_for_timeout(2000)

            # 验证分享弹窗弹出
            share_popup = p.get_by_test_id("mod-share-popup")
            assert share_popup.is_visible(), "点击分享按钮后，分享弹窗未弹出"

        # 步骤5: 获取分享语言选项
        with allure.step("步骤5: 获取分享语言选项"):
            # 查找所有以 btn-share-lang- 开头的语言按钮
            lang_buttons = p.locator("[data-testid^='btn-share-lang-']").all()
            assert len(lang_buttons) > 0, "未找到分享语言选项"

            print(f"找到 {len(lang_buttons)} 个语言选项")

            # 获取所有语言按钮的data-testid
            lang_testids = []
            for btn in lang_buttons:
                testid = btn.get_attribute("data-testid")
                if testid:
                    lang_testids.append(testid)
                    print(f"找到语言选项: {testid}")

        # 步骤6: 点击切换语言
        with allure.step("步骤6: 点击切换语言"):
            for testid in lang_testids:
                lang_btn = p.get_by_test_id(testid)
                if lang_btn.is_visible():
                    print(f"点击语言选项: {testid}")
                    lang_btn.click()
                    p.wait_for_timeout(500)  # 短暂等待语言切换

        # 步骤7: 点击复制链接按钮
        with allure.step("步骤7: 点击复制链接按钮"):
            copy_link_btn = p.get_by_test_id("btn-share-method-copyLink")
            assert copy_link_btn.is_visible(), "复制链接按钮不可见"

            copy_link_btn.click()
            p.wait_for_timeout(2000)

        # 步骤8: 验证弹窗关闭，回到秒杀页面
        with allure.step("步骤8: 验证弹窗关闭，回到秒杀页面"):
            # 等待弹窗关闭
            p.wait_for_timeout(1000)

            # 验证分享弹窗已关闭
            share_popup_after_click = p.get_by_test_id("mod-share-popup")
            assert not share_popup_after_click.is_visible(), "复制链接后，分享弹窗未正确关闭"

            # 验证回到秒杀页面 - 检查商品卡片仍然存在
            product_cards_after = p.get_by_test_id("wid-product-card-container").all()
            assert len(product_cards_after) > 0, "未正确回到秒杀页面"

            print("测试完成：成功回到秒杀页面")
