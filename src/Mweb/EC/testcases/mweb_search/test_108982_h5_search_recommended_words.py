import allure
import pytest
from playwright.sync_api import Page, expect
from src.Mweb.EC.mweb_pages.mweb_search.mweb_page_search import MWebSearchPage
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


@allure.story("H5-搜索联想结果验证")
class TestH5SearchSuggestion:
    pytestmark = [
        pytest.mark.mweb_regression,
        pytest.mark.h5search
    ]

    @allure.title("[108982]首页-搜索页面联想商品UI/UX验证")
    def test_tofu_suggestion_display(self, phone_page: dict, h5_autotest_header):
        """
        测试步骤：
        1. 访问首页
        2. 输入tofu关键词
        3. 验证联想结果中的图片alt属性
        4. 验证联想结果中的文案显示
        """
        _page: Page = phone_page["page"]
        _context = phone_page["context"]
        search_page = MWebSearchPage(_page, h5_autotest_header, _context)

        test_keyword = "tofu"

        try:
            # === 1. 访问首页 ===
            _page.goto(TEST_URL)
            # _page.wait_for_load_state("networkidle")
            log.info("首页加载完成")

            # === 2. 输入关键词 ===
            with allure.step("输入tofu关键词"):
                search_input = _page.locator("//input[@placeholder='Search']")
                search_input.click()
                _page.get_by_placeholder("What are you looking for?").fill(test_keyword)
                _page.wait_for_timeout(1000)  # 等待联想结果加载
                log.info(f"已输入关键词: {test_keyword}")

            # === 3. 验证联想结果图片 ===
            with allure.step("验证联想结果图片"):
                suggestion_images = _page.locator("//img[@alt='Search item image']")
                expect(suggestion_images.first).to_be_visible()
                log.info("验证联想结果图片存在")

            # === 4. 验证联想结果文案 ===
            with allure.step("验证联想结果文案"):
                suggestion_text = _page.locator('//*[@id="container"]/div/div[1]/div/div/span')
                expect(suggestion_text).to_have_text(test_keyword)
                log.info(f"验证联想结果文案包含: {test_keyword}")

            # 附加截图作为证据
            allure.attach(
                _page.screenshot(full_page=True),
                name="search_suggestion",
                attachment_type=allure.attachment_type.PNG
            )

        except Exception as e:
            log.error(f"测试失败: {str(e)}")
            allure.attach(
                _page.screenshot(full_page=True),
                name="search_suggestion_failure",
                attachment_type=allure.attachment_type.PNG
            )
            raise



# @allure.story("H5-搜索页面联想商品UI/UX验证")
# class TestH5SearchFunction:
#     pytestmark = [pytest.mark.mweb_regression]
#
#     @allure.title("H5-搜索页面联想商品UI/UX验证")
#     @pytest.mark.h5search
#     def test_112059_h5_search_by_suggestion(self, phone_page: dict, h5_autotest_header):
#         """
#         测试步骤：
#         1. 访问首页
#         2. 点击搜索框
#         3. 输入关键词并选择建议项
#         4. 验证跳转到正确的结果页
#         """
#         _page: Page = phone_page["page"]
#         _context = phone_page["context"]
#         _page.goto(TEST_URL)
#         search_page = MWebSearchPage(_page, h5_autotest_header, _context)
#
#         # 测试数据
#         test_keyword = "tofu"
#         expected_url = f"https://www.sayweee.com/en/search?keyword={test_keyword}"
#
#         # 1. 点击搜索框并输入关键词
#         search_input = _page.locator("//input[@placeholder='Search']")
#         search_input.click()
#         _page.get_by_placeholder("What are you looking for?").fill(test_keyword)
#
#         # 等待建议项加载
#         _page.wait_for_selector(f"//div[contains(@class, 'search-suggestion')]//*[contains(text(), '{test_keyword}')]",
#                                 state="visible", timeout=2000)
#
#
#
#         # # 2. 选择第一个匹配的建议项
#         # first_suggestion = _page.locator(
#         #     f"//div[contains(@class, 'search-suggestion')]//*[contains(text(), '{test_keyword}')]").first
#         # first_suggestion.click()
#         # log.info(f"Selected search suggestion: {test_keyword}")
#         #
#         # # 3. 验证跳转结果页
#         # _page.wait_for_url(expected_url, timeout=2000)
#         # assert _page.url == expected_url, f"搜索结果页URL不匹配，当前: {_page.url}"
#         #
#         # # 4. 验证结果页元素
#         # assert _page.locator("//h1[contains(@class, 'search-results-title')]").is_visible(), "搜索结果标题未显示"
#         # assert _page.locator("//div[@data-testid='wid-product-card-container']").first.is_visible(), "未找到商品卡片"
#         #
#         # # 5. 验证搜索关键词显示正确
#         # results_title = _page.locator("//h1[contains(@class, 'search-results-title')]").text_content()
#         # assert test_keyword.lower() in results_title.lower(), f"结果标题中未包含关键词'{test_keyword}'"
