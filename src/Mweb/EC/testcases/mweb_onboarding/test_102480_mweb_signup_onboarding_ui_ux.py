import allure
import pytest

from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from playwright.sync_api import Page


@allure.story("WEB-注册-onboarding-页面UI/UX验证")
class TestWebSignupOnboardingUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.mweb_zhongyuan]

    @allure.title("WEB-注册-onboarding-页面UI/UX验证")
    @pytest.mark.h5onboarding
    def _test_102480_mweb_signup_onboarding_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【102480】 WEB-注册-onboarding-页面UI/UX验证
        """
        #todo  enki样式下，首页zipcode, store等无法定位，等开发加上test_id以后再写
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        h5_home_page = MWebPageHome(p, h5_autotest_header, c)
        # 关闭广告弹窗
        if p.locator("//img[contains(@aria-label, 'close button')]").all():
            p.locator("//img[contains(@aria-label, 'close button')]").click()
        # 1. 关闭time banner
        h5_home_page.m_close_time_banner()
        # 断言time banner不存在
        assert not p.get_by_test_id("btn-close-promo-bar").is_visible()
        # 2. 切换store
        h5_home_page.m_home_switch_specific_store("chinese")
        # 3. 点击底部各种城市 H5无此步骤

        # 4. 输入正确的zipcode, H5不能直接输入错误的zipcode
        h5_home_page.m_change_zipcode()

        # 5. 点击app下载按钮（AppStore、Googlestore、QRcode）
        h5_home_page.m_download_app("ios")
