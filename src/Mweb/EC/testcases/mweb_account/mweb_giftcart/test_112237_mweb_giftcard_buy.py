import allure
import pytest
from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_giftcard_page.mweb_giftcard_page import MWebGiftCardPage
from src.config.weee.log_help import log

@allure.story("礼品卡购买流程")
class TestMWebGiftCardBuy:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.transaction]

    @allure.title("礼品卡购买流程验证")
    def test_112237_giftcard_buy(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试礼品卡购买流程
        步骤：
        1. 进入礼品卡页面
        2. 关闭礼品卡弹窗
        3. 输入收件人邮箱
        4. 点击结算按钮
        5. 选择支付方式为PayPal
        6. 确认支付方式
        6. 点击最终结算按钮
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        giftcard_url = "/product/gift-card/2189607?"

        try:
            # 1. 初始化礼品卡页面
            giftcard_page = MWebGiftCardPage(p, h5_autotest_header, browser_context=c, page_url=giftcard_url)
            log.info("成功初始化礼品卡页面")

            # 2. 关闭礼品卡弹窗
            giftcard_page.close_gift_card_popup()
            log.info("成功关闭礼品卡弹窗")
            p.wait_for_timeout(1000)

            # 3. 输入收件人邮箱
            p.wait_for_selector("//input[@id='recipient_email' and @type='email']", state="visible", timeout=10000)
            giftcard_page.input_recipient_email("<EMAIL>")
            log.info("成功输入收件人邮箱")
            p.wait_for_timeout(1000)

            # 4. 点击结算按钮
            p.wait_for_selector("//button[text()='Checkout']", state="visible", timeout=10000)
            giftcard_page.click_place_order()
            log.info("成功点击结算按钮")
            p.wait_for_timeout(2000)

            # 5. 支付方式模块，点击进去
            p.wait_for_selector("[data-testid='wid-payment-box']", state="visible", timeout=10000)
            giftcard_page.select_payment_method()
            log.info("成功点击支付方式模块")
            p.wait_for_timeout(1000)

            # 6. 选择 PayPal 支付方式
            p.wait_for_selector("//*[@data-category='P']", state="visible", timeout=10000)
            giftcard_page.select_paypal_payment()
            log.info("成功选择PayPal支付方式")
            p.wait_for_timeout(1000)

            # 7. 点击确认支付方式
            p.wait_for_selector("[data-testid='btn-pay-method-confirm']", state="visible", timeout=10000)
            giftcard_page.confirm_payment_method()
            log.info("成功点击确认支付方式")
            p.wait_for_timeout(1000)

            # 8. 点击 checkout 右下角的结算按钮
            p.wait_for_selector("[data-testid='btn-place-order']", state="visible", timeout=10000)
            giftcard_page.click_final_payment()
            log.info("成功点击最终结算按钮")
            p.wait_for_timeout(2000)

        except Exception as e:
            log.error(f"测试过程中发生异常: {str(e)}")
            try:
                log.info(f"当前页面URL: {p.url}")
                log.info(f"页面标题: {p.title()}")
                import time
                screenshot_path = f"error_screenshot_{int(time.time())}.png"
                p.screenshot(path=screenshot_path)
                log.info(f"错误截图已保存: {screenshot_path}")
            except Exception as debug_e:
                log.error(f"保存调试信息时出错: {str(debug_e)}")
            raise