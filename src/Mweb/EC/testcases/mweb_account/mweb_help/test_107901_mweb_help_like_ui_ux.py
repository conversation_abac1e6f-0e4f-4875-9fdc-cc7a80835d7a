import allure
import pytest
from playwright.sync_api import Page
from src.Mweb.EC.mweb_ele.mweb_account.mweb_help import mweb_help_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_page_help.mweb_page_help import HelpPage
from src.config.weee.log_help import log


@allure.story("帮助中心-文章查看流程")
class TestHelpCenter:
    pytestmark = [pytest.mark.h5help, pytest.mark.transaction]
    

    @allure.title("帮助中心-热点问题文章查看流程验证")
    def test_help_center_hot_issues(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        测试帮助中心热点问题文章查看流程
        步骤：
        1. 访问帮助中心页面
        2. 点击热点问题分类
        3. 点击如何修改订单地址文章
        4. 验证文章页面标题
        5. 点击有用按钮
        6. 验证按钮变为灰色
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        with allure.step("初始化帮助中心页面"):
            try:
                # 初始化帮助中心页面
                help_page = HelpPage(p, h5_autotest_header, browser_context=c)
                log.info("成功初始化帮助中心页面")
            except Exception as e:
                log.error(f"初始化帮助中心页面失败: {str(e)}")
                raise

        with allure.step("点击热点问题分类"):
            try:
                # 1. 点击热点问题分类
                help_page.click_hot_issues()
                log.info("成功点击热点问题分类")
            except Exception as e:
                log.error(f"点击热点问题分类失败: {str(e)}")
                raise

        with allure.step("点击如何修改订单地址文章"):
            try:
                # 2. 点击如何修改订单地址文章
                help_page.click_change_delivery_address()
                log.info("成功点击如何修改订单地址文章")
            except Exception as e:
                log.error(f"点击如何修改订单地址文章失败: {str(e)}")
                raise

        # with allure.step("验证文章页面标题"):
        #     try:
        #         # 3. 验证文章页面标题
        #         help_page.verify_article_title()
        #         log.info("成功验证文章标题")
        #     except Exception as e:
        #         log.error(f"验证文章页面标题失败: {str(e)}")
        #         raise

        with allure.step("点击有用按钮"):
            try:
                # 4. 点击有用按钮
                help_page.click_confirm_button()
                log.info("成功点击有用按钮")
            except Exception as e:
                log.error(f"点击有用按钮失败: {str(e)}")
                raise

        with allure.step("验证按钮变为灰色"):
            try:
                # 5. 等待3秒
                p.wait_for_timeout(3000)

                # 6. 验证按钮变为灰色
                gray_button = p.locator(mweb_help_ele.ele_confirm_button_gray)
                assert gray_button.is_visible(), "按钮未变为灰色"
                log.info("成功验证按钮变为灰色")

            except Exception as e:
                log.error(f"验证按钮变为灰色失败: {str(e)}")
                raise