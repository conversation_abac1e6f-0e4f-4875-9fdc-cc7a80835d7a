
import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_list_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108649】 订单列表待晒单tab-待晒单及已晒单流程")
class TestMWebMyToReviewOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108649】 订单列表待晒单tab-待晒单及已晒单流程")
    def test_108649_mWeb_my_to_review_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108649】 订单列表待晒单tab-待晒单及已晒单流程
        测试步骤：
        1、切换到待晒单tab 下，检查是否有待晒单订单
        2、如果没有，点击去逛逛按钮
        3、如果有，点击订单组件上Review按钮，跳转至order review 页面
        4、这个页面上部分是评分，下部分待评论的商品列表
        5、点击评分上的星星，最多可选5颗星，也可以不选，点击星星之后，弹出 order rating pop
        6、在order rating 页面有两个评分模块，一个是How was your delivery with the Weee! driver ?可以选择评分原因，支持多选，还可以在输入框里输入原因
        7、在order rating页面还有一个 how where the products 评分模块，也是可以最多选5颗星，可以选择评分原因，支持多选，还可以在输入框里输入原因
        8、做完评分之后，点击send to weee！ 按钮，回到order review 页面
        9、继续进行评论，选择其中一个商品旁边的review 按钮，弹出review pop
        10、这个review pop 页面第一部分是刚才选择的商品，第二部分reting（必填），可以再次改评分，第三部分是 update photos（必填），第三部分是评论
        11、如果点击review pop左上角x 按钮，会再弹一个是否离开review 的pop
        12、点击stay，停留在review pop页面
        13、继续点击review pop左上角x 按钮，会又弹一个是否离开review 的pop
        14、点击leave，回到order review 页面
        15、继续点击 选择其中一个商品旁边的review 按钮，弹出review pop
        16、完成rating、上传图片、输入评论之后，post 按钮才是高亮可点击状态
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到待晒单tab
        to_review_tab = p.get_by_test_id(mweb_order_list_ele.order_review_tab_ele)
        assert to_review_tab.is_visible(), "未找到待晒单tab"
        to_review_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到待晒单 tab
        assert "/order/list?filter_status=6" in p.url, "未切换到待晒单tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 待晒单tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("wid-view-more").is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表
            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_tab_info(item_R, "6")
                    assert order_page.assert_order_card_info(item_R, "R"), f"第{index + 1}个生鲜订单卡片信息验证失败"
                    assert order_page.order_card_btn(item_R), f"第{index + 1}个生鲜订单卡片按钮验证失败"
                    break
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_S, "S"), f"第{index + 1}个seller订单卡片信息验证失败"
                    break
            elif len(order_P_items) > 0:
                for index, item_P in enumerate(order_P_items):
                    # 验证前10 个订单内容信息
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_P, "P"), f"第{index + 1}个积分订单卡片信息验证失败"
            elif len(order_G_items) > 0:
                for index, item_G in enumerate(order_G_items):
                    # 验证前10 个订单内容信息
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_G,
                                                             "G"), f"第{index + 1}个里礼品卡订单卡片信息验证失败"
                    break
            # 点击卡片进入pdp
            order_R_items[0].click()
            p.wait_for_timeout(2000)
            # 断言页面进入订单详情页面
            assert "order/detail/" in p.url, "没有进入订单详情页面"
            p.get_by_test_id("wid-page-nav-header-back-button")