import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_list_ele, mweb_buy_again_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108616】订单列表已取消tab-再来一单流程验证")
class TestMWebMyCancelOrderBuyAgainUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108616】订单列表已取消tab-再来一单流程验证")
    def test_111625_mWeb_my_cancel_order_buy_again_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108616】订单列表已取消tab-再来一单流程验证
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、点击全部订单，找到订单列表下再来一单按钮，进入商品选择页面
        3、商品选择页面，默认勾选全选按钮，勾选掉某个商品，全选勾掉，点击全选商品全部选中，这里如果选中几个商品，加入购物车按钮上就会显示几个
        4、点击加入购物车按钮，自动回到订单列表页面，并弹出toast，点击查看，进入购物车页面
         第3、4 会在 再来一单case 里实现
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(mweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        # 切换到已取消 tab
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已取消 tab
        assert "/order/list?filter_status=4" in p.url, "未切换到已取消tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 已取消tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id(mweb_order_list_ele.home_view_more_ele).is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表上buy order 按钮
            buy_order_btns = p.get_by_test_id(mweb_order_list_ele.order_buy_again_btn).all()
            if len(buy_order_btns) == 0:
                log.warning("已取消tab下没有订单，无法继续测试")
                pytest.skip("已取消tab下没有订单，跳过测试")
            else:
                for index, item in enumerate(buy_order_btns):
                    # 点击再来一单按钮
                    item.click()
                    p.wait_for_timeout(3000)
                    assert p.get_by_test_id(mweb_buy_again_ele.buy_again_page_id).is_visible(), "未成功进入再来一单页面"
                    break



