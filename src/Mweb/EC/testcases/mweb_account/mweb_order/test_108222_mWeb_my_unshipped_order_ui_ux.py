
import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_list_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log


@allure.story("【108222】 订单列表待发货 tab-订单流程验证")
class TestMWebMyUnShippedOrderUIUX:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]
    @allure.title("【108222】 订单列表待发货 tab-订单流程验证")
    def test_108222_mWeb_my_unshipped_order_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【108222】 订单列表待发货 tab-订单流程验证
        测试步骤：
        1、切换到待发货tab 下，检查是否有待发货订单
        2、如果没有，点击start shopping，进入首页
        3、如果有查看待发货订单，验证订单信息正确，订单状态正确
        4、点击订单列表下各按钮
        5、点击订单进入订单详情，验证订单详情信息正确，各交互正确
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")
        
        # 切换到待发货tab
        unshipped_tab = p.get_by_test_id(mweb_order_list_ele.order_unshipped_tab_ele)
        assert unshipped_tab.is_visible(), "未找到已发货tab"
        # 切换到已发货tab
        unshipped_tab.click()
        p.wait_for_timeout(2000)
        # 验证已切换到已发货 tab
        assert "/order/list?filter_status=2" in p.url, "未切换到已发货tab"
        # 2. 检查是否存在订单
        empty_state = p.get_by_test_id(mweb_order_list_ele.empty_image_ele)
        if empty_state.is_visible():
            # 已发货tab下没有订单
            # 2. 如果没有订单，点击start shopping按钮
            start_shopping_btn = p.get_by_test_id(mweb_order_list_ele.start_shopping_btn_ele)
            assert start_shopping_btn.is_visible(), "未找到start shopping按钮"
            assert p.get_by_test_id(mweb_order_list_ele.empty_title_ele)
            start_shopping_btn.click()
            p.wait_for_timeout(3000)
            # 判断进入首页
            assert p.get_by_test_id("wid-view-more").is_visible(), "未成功跳转到首页"
        else:
            # 获取订单列表
            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()

            if len(order_R_items) == 0 and len(order_S_items) == 0:
                log.warning("已发货tab下没有normal+seller 订单，无法继续测试")
                pytest.skip("已发货tab下没有normal+seller 订单，跳过测试")
            elif len(order_R_items) > 0:
                for index, item_R in enumerate(order_R_items):
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_tab_info(item_R, "2")
                    assert order_page.assert_order_card_info(item_R, "R"), f"第{index + 1}个生鲜订单卡片信息验证失败"
                    break
            elif len(order_S_items) > 0:
                for index, item_S in enumerate(order_S_items):
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_S, "S"), f"第{index + 1}个seller订单卡片信息验证失败"
                    break
            elif len(order_P_items) > 0:
                for index, item_P in enumerate(order_P_items):
                    # 验证前10 个订单内容信息
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_P, "P"), f"第{index + 1}个积分订单卡片信息验证失败"
                    break
            elif len(order_G_items) > 0:
                for index, item_G in enumerate(order_G_items):
                    # 验证前10 个订单内容信息
                    # 使用公共断言方法验证订单卡片信息
                    assert order_page.assert_order_card_info(item_G,
                                                             "G"), f"第{index + 1}个里礼品卡订单卡片信息验证失败"
                    break
            # 点击卡片进入pdp
            order_R_items[0].click()
            p.wait_for_timeout(2000)
            # 断言页面进入订单详情页面
            assert "order/detail/" in p.url, "没有进入订单详情页面"
            p.get_by_test_id("wid-page-nav-header-back-button")

