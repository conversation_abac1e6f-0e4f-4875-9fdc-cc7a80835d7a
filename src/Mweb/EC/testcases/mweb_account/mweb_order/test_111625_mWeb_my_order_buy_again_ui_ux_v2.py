import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_account.mweb_order import mweb_order_list_ele, mweb_buy_again_ele
from src.Mweb.EC.mweb_pages.mweb_account_page.mweb_order_page.mweb_order_page import MWebOrderPage
from src.config.weee.log_help import log

"""
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、订单类型有如下：
            order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
            order_S_items = p.get_by_test_id(mweb_order_list_ele.order_list_S_card_ele).all()
            order_P_items = p.get_by_test_id(mweb_order_list_ele.order_list_P_card_ele).all()
            order_G_items = p.get_by_test_id(mweb_order_list_ele.order_list_G_card_ele).all()
            order_Pre_items = p.get_by_test_id(mweb_order_list_ele.order_list_Pre_card_ele).all()

        2、找到其中一个order_R_items类型的订单，
        3、点击再来一单按钮data-testid="wid-order-btn-buy_again"
        4、判断进入再来一单商品选择页面data-testid="wid-popup-order-buy-again"
        5、判断页面有如下内容
            有效商品区域data-testid="wid-order-buy-again-content-available"
            无效商品区域data-testid="wid-order-buy-again-content-unavailable"
            加购按钮data-testid="wid-order-buy-again-add-cart-button"
            全选按钮data-testid="wid-order-buy-again-chose-all"
            切换日期按钮data-testid="wid-order-buy-again-change-date"
        5、商品选择页面，默认勾选全选按钮，data-checked="true"
        6、勾选掉有效商品下某个商品data-testid="wid-order-buy-again-product-item"，商品的勾选状态也是data-checked="false"
        7、如果无效商品区域data-testid="wid-order-buy-again-content-unavailable" 下有商品 data-testid="wid-order-buy-again-product-item"，那么商品都是置灰状态，且 无法勾选 data-checked="false"
        7、全选按钮去掉时状态 data-checked="false"
        8、只要勾选状态取消，data-checked="false"
        9、只要取消全选，加购按钮data-testid="wid-order-buy-again-submit" 置灰状态data-disabled="true"
        10、只要有一个商品选上，加购按钮就高亮状态 data-disabled="false"，
        11、勾选几个商品按钮上存在数字 data-testid="wid-order-buy-again-submit-count"，textcontent 就等于几
        12、点击加入购物车按钮，自动回到订单列表页面
"""


@allure.story("【111625】 订单列表-再来一单功能验证 V2")
class TestMWebMyOrderBuyAgainUIUXV2:
    pytestmark = [pytest.mark.h5order, pytest.mark.mweb_regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【111625】 订单列表-再来一单功能验证 V2")
    def test_111625_mWeb_my_order_buy_again_ui_ux_v2(self, phone_page: dict, h5_autotest_header,
                                                     h5_open_and_close_trace):
        """
        【111625】 订单列表-再来一单功能验证 V2
        测试步骤：
        1、进入订单列表页面，切换到已取消tab下
        2、找到其中一个order_R_items类型的订单
        3、点击再来一单按钮data-testid="wid-order-btn-buy_again"
        4、判断进入再来一单商品选择页面data-testid="wid-popup-order-buy-again"
        5、判断页面有如下内容
        6、商品选择页面，默认勾选全选按钮，data-checked="true"
        7、勾选掉某个商品，验证状态变化
        8、验证全选按钮状态变化
        9、验证加购按钮状态变化
        10、验证商品数量计数器
        11、点击加入购物车按钮，自动回到订单列表页面
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 1. 进入订单列表页面，切换到已取消tab下
        order_page = MWebOrderPage(p, h5_autotest_header, browser_context=c, page_url="/order/list")
        p.wait_for_timeout(3000)
        log.info("成功进入订单列表页面")

        # 切换到已取消tab
        cancelled_tab = p.get_by_test_id(mweb_order_list_ele.order_cancelled_tab_ele)
        assert cancelled_tab.is_visible(), "未找到已取消tab"
        cancelled_tab.click()
        p.wait_for_timeout(2000)
        assert "/order/list?filter_status=4" in p.url, "未切换到已取消tab"

        # 2. 获取订单类型
        order_R_items = p.get_by_test_id(mweb_order_list_ele.order_list_R_card_ele).all()
        # 找到R类型订单
        if len(order_R_items) == 0:
            log.warning("已取消tab下没有R类型订单，无法继续测试")
            pytest.skip("已取消tab下没有R类型订单，跳过测试")

        # 3. 点击再来一单按钮
        first_r_order = order_R_items[0]
        buy_again_btn = first_r_order.get_by_test_id(mweb_order_list_ele.order_buy_again_btn)
        assert buy_again_btn.is_visible(), "再来一单按钮不可见"
        buy_again_btn.click()
        p.wait_for_timeout(3000)

        # 4. 判断进入再来一单商品选择页面
        buy_again_popup = p.get_by_test_id(mweb_buy_again_ele.buy_again_popup)
        assert buy_again_popup.is_visible(), "再来一单商品选择页面未显示"
        buy_again_page = p.get_by_test_id(mweb_buy_again_ele.buy_again_page)
        assert buy_again_page.is_visible(), "再来一单商品选择页面未显示"

        # 5. 判断页面有如下内容
        available_area = buy_again_page.get_by_test_id(mweb_buy_again_ele.buy_again_available)
        assert available_area.is_visible(), "有效商品区域不可见"

        unavailable_area = buy_again_page.get_by_test_id(mweb_buy_again_ele.buy_again_unavailable)
        # assert unavailable_area.is_visible(), "无效商品区域不可见"

        # 验证无效商品区域的商品状态
        unavailable_products = unavailable_area.get_by_test_id(mweb_buy_again_ele.buy_again_available_product).all()
        if len(unavailable_products) > 0:
            log.info(f"无效商品区域存在{len(unavailable_products)}个商品")
            for unavailable_product in unavailable_products:
                # 验证无效商品都是置灰状态且无法勾选
                assert unavailable_product.get_attribute("data-checked") == "false", "无效商品不应该被勾选"

                # 尝试点击无效商品，验证无法勾选
                unavailable_product.click()
                p.wait_for_timeout(500)
                assert unavailable_product.get_attribute("data-checked") == "false", "无效商品点击后仍应保持未勾选状态"
                log.info("验证无效商品置灰状态且无法勾选成功")
        else:
            log.info("无效商品区域没有商品")

        add_cart_btn = p.get_by_test_id(mweb_buy_again_ele.buy_again_add_cart_button)
        assert add_cart_btn.is_visible(), "加购按钮不可见"

        select_all_btn = p.get_by_test_id(mweb_buy_again_ele.buy_again_chose_all)
        assert select_all_btn.is_visible(), "全选按钮不可见"

        change_date_btn = p.get_by_test_id(mweb_buy_again_ele.buy_again_change_date)
        assert change_date_btn.is_visible(), "切换日期按钮不可见"

        # 6. 商品选择页面，默认勾选全选按钮
        assert select_all_btn.get_attribute("data-checked") == "true", "全选按钮默认未勾选"
        log.info("验证全选按钮默认勾选状态成功")

        # 获取有效商品列表
        product_items = available_area.get_by_test_id(mweb_buy_again_ele.buy_again_available_product).all()
        assert len(product_items) > 0, "没有找到商品"

        # 验证所有商品默认被选中
        for item in product_items:
            assert item.get_attribute("data-checked") == "true", "商品默认未被选中"

            # 验证商品基础信息
            # 1. 商品标题不为空
            item_title = item.get_by_test_id(mweb_buy_again_ele.buy_again_product_item_content_title)
            assert item_title.is_visible(), "商品标题元素不可见"
            assert item_title.text_content().strip() != "", "商品标题内容为空"

            # 2. 商品图片存在且src属性包含.auto
            item_image = item.get_by_test_id(mweb_buy_again_ele.buy_again_product_item_content_image)
            assert item_image.is_visible(), "商品图片元素不可见"
            image_src = item_image.get_attribute("src")
            assert image_src is not None and ".auto" in image_src, "商品图片src属性不包含.auto"

            # 3. 商品价格格式为$x.xx
            item_price = item.get_by_test_id(mweb_buy_again_ele.buy_again_product_item_content_price)
            assert item_price.is_visible(), "商品价格元素不可见"
            price_text = item_price.text_content().strip()
            assert price_text != "", "商品价格内容为空"
            import re
            price_pattern = r'^\$\d+\.\d{2}$'
            assert re.match(price_pattern, price_text), f"商品价格格式不正确，期望$x.xx格式，实际为: {price_text}"

        # 7. 勾选掉某个商品的checkbox
        first_product = product_items[0]
        first_product.get_by_test_id(mweb_buy_again_ele.buy_again_item_checkbox).click()
        p.wait_for_timeout(1000)

        # 验证商品勾选状态变为false
        assert first_product.get_attribute("data-checked") == "false", "商品取消勾选失败"
        log.info("验证商品取消勾选状态成功")

        # 8. 验证全选按钮状态变为false
        assert select_all_btn.get_attribute("data-checked") == "false", "全选按钮状态未更新"
        log.info("验证全选按钮状态变化成功")

        # 9. 验证加购按钮状态
        submit_btn = p.get_by_test_id(mweb_buy_again_ele.buy_again_add_cart_button)
        # 计算当前选中的商品数量
        selected_count = len([item for item in product_items if item.get_attribute("data-checked") == "true"])
        
        if selected_count == 0:
            # 如果没有商品被选中，按钮应该置灰
            assert submit_btn.get_attribute("data-disabled") == "true", "没有商品选中时加购按钮应该置灰"
            log.info("验证加购按钮置灰状态成功")
        else:
            # 如果还有商品被选中，按钮应该可用
            assert submit_btn.get_attribute("data-disabled") == "false", "有商品选中时加购按钮应该可用"
            log.info("验证加购按钮可用状态成功")

        # 10. 验证商品数量计数器
        count_element = p.get_by_test_id(mweb_buy_again_ele.buy_again_add_cart_button_count)
        if count_element.is_visible():
            assert count_element.text_content() == str(selected_count), "商品数量计数器显示错误"
            log.info(f"验证商品数量计数器成功: {selected_count}")

        # 重新选中商品以确保有商品可以加购
        first_product.get_by_test_id(mweb_buy_again_ele.buy_again_item_checkbox).click()
        p.wait_for_timeout(1000)
        assert first_product.get_attribute("data-checked") == "true", "重新选中商品失败"

        # 验证至少有一个商品被选中时，加购按钮可用
        submit_btn = p.get_by_test_id(mweb_buy_again_ele.buy_again_add_cart_button)
        assert submit_btn.get_attribute("data-disabled") == "false", "有商品选中时加购按钮应该可用"
        log.info("验证加购按钮可用状态成功")

        # 12. 点击加入购物车按钮
        submit_btn.click()
        p.wait_for_timeout(3000)

        # 验证自动回到订单列表页面
        assert "/order/list" in p.url, "未成功回到订单列表页面"
        log.info("验证回到订单列表页面成功")

        log.info("再来一单功能验证V2完成")