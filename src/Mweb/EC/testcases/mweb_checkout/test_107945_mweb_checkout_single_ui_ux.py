"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   test_107945_mweb_checkout_single_ui_ux.py
@Description    :  
@CreateTime     :  2025/3/25 13:59
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/3/25 13:59
"""
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("H5结算单个购物车uiux")
class TestCheckoutSingleUiUx:
    pytestmark = [pytest.mark.h5cart, pytest.mark.todo, pytest.mark.transaction, pytest.mark.zhuli]
    @allure.title("H5结算单个购物车uiux")
    @pytest.mark.smoke
    def test_107945_mweb_checkout_single_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击结算按钮
        5. 选择配送地址
        6. 选择配送时间
        7. 选择信用卡支付
        8. 提交订单
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 清空购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品

        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            # log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")

            # 尝试直接使用XPath定位Local Delivery选项
            local_filter_id = mweb_category_ele.ele_local_delivery

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=local_filter_id,
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

            # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)

            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")


        with allure.step("点击结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击结算按钮")
            except Exception as e:
                log.error(f"点击结算按钮失败: {str(e)}")
                raise
                # 处理可能的upsell弹窗
            upsell_button = p.get_by_test_id("btn-continue")
            if upsell_button.is_visible(timeout=3000):
                log.info("检测到upsell弹窗，点击继续结算按钮")
                upsell_button.click()
                p.wait_for_timeout(3000)
            else:
                log.info("未检测到upsell弹窗，直接进行下一步")
                p.wait_for_timeout(2000)
        # 5. 验证结算页面各功能
        with allure.step("验证结算页面各功能"):
            try:
                # 创建结算页面对象
                checkout_page = MWebPageCheckout(p, h5_autotest_header, browser_context=c)
                p.wait_for_timeout(2000)

                # 验证免运费banner
                with allure.step("验证免运费banner"):
                    banner_result = checkout_page.verify_shipping_banner()
                    assert banner_result["success"], f"免运费banner验证失败: {banner_result.get('reason', '未知原因')}"
                    log.info("免运费banner验证完成")

                # 验证日期切换功能
                with allure.step("验证日期切换功能"):
                    date_result = checkout_page.verify_date_change()
                    assert date_result["success"], f"日期切换功能验证失败: {date_result.get('reason', '未知原因')}"
                    log.info("日期切换功能验证完成")

                # 验证优惠券功能
                with allure.step("验证优惠券功能"):
                    coupon_result = checkout_page.verify_coupon_functionality()
                    assert coupon_result["success"], f"优惠券功能验证失败: {coupon_result.get('reason', '未知原因')}"
                    log.info("优惠券功能验证完成")

                # 验证商品信息弹窗
                with allure.step("验证商品信息弹窗"):
                    product_result = checkout_page.verify_product_info_popup()
                    assert product_result["success"], f"商品信息弹窗验证失败: {product_result.get('reason', '未知原因')}"
                    log.info("商品信息弹窗验证完成")

                # 验证订单总结信息
                with allure.step("验证订单总结信息"):
                    summary_result = checkout_page.verify_order_summary()
                    assert summary_result["success"], f"订单总结信息验证失败: {summary_result.get('reason', '未知原因')}"
                    log.info("订单总结信息验证完成")

                # 验证小费功能
                with allure.step("验证小费功能"):
                    tip_result = checkout_page.verify_tip_functionality()
                    assert tip_result["success"], f"小费功能验证失败: {tip_result.get('reason', '未知原因')}"
                    log.info("小费功能验证完成")

                # 验证支付方式功能 需要等加了新的test_id在改
                # with allure.step("验证支付方式功能"):
                #     payment_result = checkout_page.verify_payment_method()
                #     assert payment_result["success"], f"支付方式功能验证失败: {payment_result.get('reason', '未知原因')}"
                #     log.info("支付方式功能验证完成")

                log.info("结算页面所有功能验证完成")
            except Exception as e:
                log.error(f"验证结算页面功能失败: {str(e)}")
                raise