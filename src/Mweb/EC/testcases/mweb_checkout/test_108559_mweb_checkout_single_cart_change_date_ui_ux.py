from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("H5结算页-单个购物车切换日期UI/UX验证")
class TestCheckoutSingleCartChangeDateUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.huimin]
    @allure.title("H5结算单个购物车切换日期UI/UX验证")
    @pytest.mark.smoke
    def test_108559_mweb_checkout_single_cart_change_date_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【108559】 H5结算单个购物车切换日期UI/UX验证

        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击去结算按钮
        5. 处理可能存在的upsell弹窗
           如果检测到upsell弹窗，然后点击继续按钮
           如果未检测到upsell弹窗，直接进行下一步
        6. 验证进入结算页
        7. 根据第5步再次处理可能存在的upsell弹窗
        8. 向下滑动结算页，检查切换日期按钮是否存在
        9. 如果存在切换日期按钮，点击切换日期按钮
        10. 如果支持切换日期会拉起切换日期popup
        11. 验证切换日期popup顶部标题有内容
        12. 验证切换日期popup下面有日期数据
        13. 点击任意一个可选择的日期
        14. 验证回到结算页面
        15. 验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期

        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        p.wait_for_timeout(2000)

        # 1.清空购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品

        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=mweb_category_ele.ele_local_delivery,  # 直接传递字符串test_id
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

            # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)

            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")

        # with allure.step("从分类页加购商品"):
        #     try:
        #         # 进入分类页
        #         category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c)
        #         # 添加商品到购物车
        #         category_page.add_to_local_product_cart_from_category()
        #         p.wait_for_timeout(2000)
        #         log.info("商品加购成功")
        #     except Exception as e:
        #         log.error(f"加购商品失败: {str(e)}")
        #         raise

        # 4. 点击去结算按钮
        with allure.step("点击去结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击去结算按钮")
            except Exception as e:
                log.error(f"点击去结算按钮失败: {str(e)}")
                raise

        # 5. 处理可能存在的upsell弹窗（点击结算按钮后）
        with allure.step("处理可能存在的upsell弹窗（点击结算按钮后）"):
            try:
                # 检测upsell弹窗
                upsell_popup = p.get_by_test_id("wid-popup-checkoutupsell")
                if upsell_popup.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗（点击结算按钮后）")
                    # 点击继续按钮
                    continue_button = p.get_by_test_id("btn-continue")
                    if continue_button.is_visible(timeout=2000):
                        continue_button.click()
                        p.wait_for_timeout(3000)
                        log.info("成功点击继续按钮")
                    else:
                        log.warning("找到upsell弹窗但未找到继续按钮")
                else:
                    log.info("未检测到upsell弹窗（点击结算按钮后），直接进行下一步")
                    p.wait_for_timeout(2000)
            except Exception as e:
                log.warning(f"处理upsell弹窗时出现异常: {str(e)}")
                log.info("继续执行下一步")

        # 6. 验证进入结算页
        with allure.step("验证进入结算页"):
            checkout_page = MWebPageCheckout(p, h5_autotest_header, browser_context=c, page_url="/order/checkout?cart_domain=grocery")
            assert "/order/checkout?cart_domain=grocery" in p.url, "未成功进入结算页"
            log.info("成功进入结算页")
        # 7. 处理可能存在的upsell弹窗（进入结算页后）
        with allure.step("处理可能存在的upsell弹窗（进入结算页后）"):
            try:
                # 检测upsell弹窗
                upsell_popup = p.get_by_test_id("wid-popup-checkoutupsell")
                if upsell_popup.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗（进入结算页后）")
                    # 点击继续按钮
                    continue_button = p.get_by_test_id("btn-continue")
                    if continue_button.is_visible(timeout=2000):
                        continue_button.click()
                        p.wait_for_timeout(3000)
                        log.info("成功点击继续按钮")
                    else:
                        log.warning("找到upsell弹窗但未找到继续按钮")
                else:
                    log.info("未检测到upsell弹窗（进入结算页后），直接进行下一步")
                    p.wait_for_timeout(2000)
            except Exception as e:
                log.warning(f"处理upsell弹窗时出现异常: {str(e)}")
                log.info("继续执行下一步")

        # 8. 向下滑动结算页，检查切换日期按钮是否存在
        with allure.step("向下滑动结算页，检查切换日期按钮是否存在"):
            # 向下滑动页面查找切换日期按钮
            date_btn_found = False
            max_scroll_attempts = 5

            for scroll_attempt in range(max_scroll_attempts):
                log.info(f"第 {scroll_attempt + 1} 次尝试定位切换日期按钮")

                # 尝试定位切换日期按钮
                change_date_btn = p.get_by_test_id("wid-review-order-card-normal-shipping-date")
                if change_date_btn.is_visible():
                    date_btn_found = True
                    log.info(f"在第 {scroll_attempt + 1} 次尝试中找到切换日期按钮")
                    break

                # 如果没找到，向下滑动页面
                if scroll_attempt < max_scroll_attempts - 1:
                    log.info("切换日期按钮不可见，向下滑动页面")
                    p.evaluate("window.scrollBy(0, 300)")  # 向下滑动300像素
                    p.wait_for_timeout(1000)  # 等待滑动完成

            if not date_btn_found:
                log.warning("经过多次滑动尝试，仍未找到切换日期按钮")
            else:
                log.info("成功找到切换日期按钮")

        # 9. 如果存在切换日期按钮，点击切换日期按钮
        with allure.step("如果存在切换日期按钮，点击切换日期按钮"):
            if date_btn_found:
                change_date_btn = p.get_by_test_id("wid-review-order-card-normal-shipping-date")
                if change_date_btn.is_visible():
                    change_date_btn.click()
                    log.info("成功点击切换日期按钮")
                    p.wait_for_timeout(2000)
                else:
                    log.warning("切换日期按钮不可见，跳过点击操作")
                    # 如果按钮不存在，直接结束测试
                    log.info("由于切换日期按钮不存在，测试提前结束")
                    return
            else:
                log.warning("未找到切换日期按钮，跳过后续日期切换相关步骤")
                log.info("由于切换日期按钮不存在，测试提前结束")
                return

        # 10. 验证切换日期popup弹出
        with allure.step("验证切换日期popup弹出"):
            change_date_popup = p.get_by_test_id("wid-popup-delivery-date")
            assert change_date_popup.is_visible(timeout=5000), "切换日期弹窗未显示"
            log.info("切换日期弹窗显示成功")

        # 11. 验证切换日期popup顶部标题有内容
        with allure.step("验证切换日期popup顶部标题"):
            navbar_title = p.get_by_test_id("wid-navbar")
            assert navbar_title.is_visible(), "弹窗标题不可见"
            title_text = navbar_title.text_content()
            assert title_text and title_text.strip(), "弹窗标题内容为空"
            log.info(f"弹窗标题验证成功: {title_text}")

        # 12. 验证切换日期popup下面有日期数据
        with allure.step("验证切换日期popup下面有日期数据"):
            # 获取所有日期选项
            date_items = p.get_by_test_id("wid-delivery-date-item-content").all()
            assert len(date_items) > 0, "未找到日期数据"
            log.info(f"找到 {len(date_items)} 个日期选项")

            # 验证至少有一个日期选项是可见的
            visible_date_items = [item for item in date_items if item.is_visible()]
            assert len(visible_date_items) > 0, "没有可见的日期选项"
            log.info(f"其中 {len(visible_date_items)} 个日期选项可见")

        # 13. 点击任意一个可选择的日期
        with allure.step("点击任意一个可选择的日期"):
            # 选择第一个可见且可点击的日期选项
            selected_date_item = None
            for i, date_item in enumerate(date_items):
                if date_item.is_visible() and date_item.is_enabled():
                    selected_date_item = date_item
                    log.info(f"选择第 {i+1} 个日期选项")
                    break

            assert selected_date_item is not None, "未找到可点击的日期选项"

            # 记录选择的日期文本
            selected_date_text = selected_date_item.text_content()
            log.info(f"准备选择日期: {selected_date_text}")

            # 点击选择的日期
            selected_date_item.click()
            p.wait_for_timeout(2000)
            log.info("成功点击日期选项")

        # 14. 验证回到结算页面
        with allure.step("验证回到结算页面"):
            assert "/order/checkout?cart_domain=grocery" in p.url, "未回到结算页面"
            log.info("成功回到结算页面")

        # 15. 验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期
        with allure.step("验证向下滑动结算页，检测结算页面日期更新成功，检查页面显示的是最新日期"):
            # 等待页面更新
            p.wait_for_timeout(3000)
            log.info("开始验证结算页面日期更新")

            # 向下滑动页面查找日期按钮
            updated_date_btn = None
            max_scroll_attempts = 5

            for scroll_attempt in range(max_scroll_attempts):
                log.info(f"第 {scroll_attempt + 1} 次尝试定位日期按钮")

                # 尝试定位日期按钮
                date_btn = p.get_by_test_id("wid-review-order-card-normal-shipping-date")
                if date_btn.is_visible():
                    updated_date_btn = date_btn
                    log.info(f"在第 {scroll_attempt + 1} 次尝试中找到日期按钮")
                    break

                # 如果没找到，向下滑动页面
                if scroll_attempt < max_scroll_attempts - 1:
                    log.info("日期按钮不可见，向下滑动页面")
                    p.evaluate("window.scrollBy(0, 300)")  # 向下滑动300像素
                    p.wait_for_timeout(1000)  # 等待滑动完成

            # 验证是否找到日期按钮
            if updated_date_btn and updated_date_btn.is_visible():
                # 滚动到日期按钮位置，确保完全可见
                updated_date_btn.scroll_into_view_if_needed()
                p.wait_for_timeout(1000)

                # 获取更新后的日期文本
                updated_date_text = updated_date_btn.text_content()
                assert updated_date_text and updated_date_text.strip(), "结算页面日期显示为空"
                log.info(f"结算页面显示的最新日期: {updated_date_text}")

                # 验证日期确实发生了变化（如果可能的话）
                if 'selected_date_text' in locals() and selected_date_text:
                    if updated_date_text != selected_date_text:
                        log.info(f"日期切换成功: 从 '{selected_date_text}' 更新为 '{updated_date_text}'")
                    else:
                        log.info("页面显示的日期与选择的日期一致")
                else:
                    log.info("日期更新验证完成，页面显示最新日期")

                log.info("结算页面日期验证成功")
            else:
                log.warning(f"经过 {max_scroll_attempts} 次滑动尝试，仍未找到日期按钮")
                # 记录当前页面信息用于调试
                current_url = p.url
                log.info(f"当前页面URL: {current_url}")

                # 查找页面上所有包含date的元素
                all_date_elements = p.locator("[data-testid*='date']").all()
                log.info(f"页面上找到 {len(all_date_elements)} 个包含'date'的元素")

                for i, element in enumerate(all_date_elements):
                    try:
                        testid = element.get_attribute("data-testid")
                        is_visible = element.is_visible()
                        log.info(f"元素 {i+1}: testid='{testid}', 可见={is_visible}")
                    except Exception as e:
                        log.debug(f"获取元素 {i+1} 信息失败: {str(e)}")

                log.info("日期切换操作已完成，但无法验证页面显示更新")

        log.info("H5结算单个购物车切换日期UI/UX验证测试完成")
