# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_110518_mweb_checkout_upsell_ui_ux.py
@Description    :
@CreateTime     :  2025/7/28 04:10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/28 04:10
"""
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("H5结算页-upsell页面-商品数据模块UI/UX验证")
class TestCheckoutSingleCartChangeDateUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.huimin]
    @allure.title("H5结算页-upsell页面-商品数据模块UI/UX验证")
    @pytest.mark.smoke
    def test_110518_mweb_checkout_upsell_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【110518】 H5结算页-upsell页面-商品数据模块UI/UX验证

        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击去结算按钮，data-testid="btn-checkout"
        5. 验证upsell弹窗标题存在 data-testid="wid-drawer-header-title"
        6. 依次验证upsell页面有哪些数据模块，有就验证，没有就跳过
        7. 验证存在数据的模块都有商品卡片 data-testid="wid-product-card-title"
        8. 对于每个模块的第一个商品点击收藏按钮，然后取消收藏
        9. 对于收藏的商品点击加购 data-testid="btn-atc-plus"
        10. 所有模块数据都验证完成后点击继续按钮，data-testid="btn-continue" 回到结算页
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        p.wait_for_timeout(2000)

        # 1.清空购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品
        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=mweb_category_ele.ele_local_delivery,  # 直接传递字符串test_id
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

        # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)

            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")

        # 4. 点击去结算按钮
        with allure.step("点击去结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击去结算按钮")
            except Exception as e:
                log.error(f"点击去结算按钮失败: {str(e)}")
                raise
                
        # 5. 验证upsell弹窗标题存在
        with allure.step("验证upsell弹窗标题存在"):
            # 等待upsell弹窗出现
            p.wait_for_timeout(3000)
            
            # 验证upsell弹窗标题
            drawer_title = p.get_by_test_id("wid-drawer-header-title")
            if not drawer_title.is_visible(timeout=5000):
                log.warning("未找到upsell弹窗标题，可能没有弹出upsell页面")
                # 检查是否直接进入了结算页
                if "/order/checkout" in p.url:
                    log.info("已直接进入结算页，没有upsell弹窗，测试结束")
                    return
                else:
                    assert False, "未找到upsell弹窗标题，且未进入结算页"
            
            title_text = drawer_title.text_content()
            log.info(f"upsell弹窗标题: {title_text}")
            assert title_text, "upsell弹窗标题为空"
            
        # 6. 依次验证upsell页面有哪些数据模块
        with allure.step("依次验证upsell页面有哪些数据模块"):
            # 定义所有可能的模块ID
            module_ids = [
                "wid-upsell-item-checkout_forgot_short_term-title",
                "wid-upsell-item-checkout_snack-title",
                "wid-upsell-item-checkout_beverages-title",
                "wid-upsell-item-checkout_forgot_long_term-title",
                "wid-upsell-item-checkout_recently_viewed-title",
                "wid-upsell-item-checkout_complete-title",
                "wid-upsell-item-checkout_fresh_bakery-title",
                "wid-upsell-item-checkout_fresh_deli-title",
                "wid-upsell-item-checkout_preference-title",
                "wid-upsell-item-checkout_sale-title"
            ]
            
            # 存储找到的模块
            found_modules = []
            
            # 向下滚动页面以确保所有模块都被加载
            for _ in range(3):
                p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                p.wait_for_timeout(1000)
            
            # 回到顶部
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(1000)
            
            # 检查每个模块
            for module_index, module_id in enumerate(module_ids):
                module = p.get_by_test_id(module_id)
                if module.is_visible(timeout=1000):
                    module_text = module.text_content()
                    log.info(f"找到模块 {module_index + 1}: {module_id}, 内容: {module_text}")
                    found_modules.append(module_id)
                    
                    # 7. 验证模块中的商品卡片
                    with allure.step(f"验证模块 {module_id} 中的商品卡片"):
                        # 滚动到该模块
                        module.scroll_into_view_if_needed()
                        p.wait_for_timeout(1000)
                        
                        # 查找该模块下的商品卡片 - 需要在模块容器内查找
                        module_container = module.locator("..").first  # 获取模块的父容器
                        product_cards = module_container.get_by_test_id("wid-product-card-title").all()
                        
                        if product_cards:
                            log.info(f"模块 {module_id} 中找到 {len(product_cards)} 个商品卡片")
                            
                            # 7.1 向右滑动商品，加载更多商品
                            # with allure.step(f"模块 {module_id} 向右滑动商品，加载更多商品"):
                            #     try:
                            #         # 获取模块容器的边界框
                            #         module_box = module_container.bounding_box()
                            #         if module_box:
                            #             # 计算滑动的起始和结束位置
                            #             start_x = module_box["x"] + module_box["width"] * 0.8
                            #             end_x = module_box["x"] + module_box["width"] * 0.2
                            #             y = module_box["y"] + module_box["height"] * 0.5
                            #
                            #             # 执行滑动操作
                            #             p.touchscreen.tap(start_x, y)
                            #             p.wait_for_timeout(100)
                            #             p.mouse.move(start_x, y)
                            #             p.mouse.down()
                            #             p.mouse.move(end_x, y, steps=10)
                            #             p.mouse.up()
                            #             p.wait_for_timeout(1500)
                            #
                            #             log.info(f"模块 {module_id} 执行向右滑动操作")
                            #
                            #             # 检查滑动后的商品数量
                            #             new_product_cards = module_container.get_by_test_id("wid-product-card-title").all()
                            #             log.info(f"模块 {module_id} 滑动后商品数量: {len(new_product_cards)} (滑动前: {len(product_cards)})")
                            #             product_cards = new_product_cards
                            #         else:
                            #             log.warning(f"模块 {module_id} 无法获取模块容器边界")
                            #     except Exception as e:
                            #         log.warning(f"模块 {module_id} 滑动操作失败: {str(e)}")
                            
                            # 8. 对该模块的第一个商品进行收藏和取消收藏操作
                            with allure.step(f"对模块 {module_id} 的第一个商品进行收藏和取消收藏操作"):
                                # 在该模块容器内找到第一个商品的收藏按钮
                                favorite_buttons = module_container.get_by_test_id("btn-favorite").all()
                                if favorite_buttons:
                                    try:
                                        # 点击收藏
                                        favorite_buttons[0].click()
                                        p.wait_for_timeout(1000)
                                        log.info(f"模块 {module_id} 第一个商品已点击收藏按钮")
                                        
                                        # 取消收藏
                                        favorite_buttons[0].click()
                                        p.wait_for_timeout(1000)
                                        log.info(f"模块 {module_id} 第一个商品已取消收藏")
                                        
                                        # 9. 对该模块的第一个商品点击加购
                                        with allure.step(f"对模块 {module_id} 的第一个商品进行加购操作"):
                                            add_buttons = module_container.get_by_test_id("btn-atc-plus").all()
                                            if add_buttons:
                                                try:
                                                    add_buttons[0].click()
                                                    p.wait_for_timeout(1000)
                                                    log.info(f"模块 {module_id} 第一个商品已点击加购按钮")
                                                except Exception as e:
                                                    log.warning(f"模块 {module_id} 加购操作失败: {str(e)}")
                                            else:
                                                log.warning(f"模块 {module_id} 未找到加购按钮")
                                                
                                    except Exception as e:
                                        log.warning(f"模块 {module_id} 收藏操作失败: {str(e)}")
                                else:
                                    log.warning(f"模块 {module_id} 未找到收藏按钮")
                        else:
                            log.warning(f"模块 {module_id} 中未找到商品卡片")
                else:
                    log.info(f"未找到模块: {module_id}")
            
            # 输出验证过的模块总结
            log.info(f"总共找到 {len(found_modules)} 个模块: {', '.join(found_modules)}")
            
        # 10. 点击继续按钮回到结算页
        with allure.step("点击继续按钮回到结算页"):
            # 滚动到页面顶部以找到继续按钮
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(1000)
            
            continue_button = p.get_by_test_id("btn-continue")
            if continue_button.is_visible(timeout=3000):
                continue_button.click()
                p.wait_for_timeout(3000)
                log.info("已点击继续按钮")
                
                # 验证是否回到结算页
                assert "/order/checkout" in p.url, "点击继续按钮后未回到结算页"
                log.info("成功回到结算页")
            else:
                log.error("未找到继续按钮")
                assert False, "未找到继续按钮"
                
        log.info("H5结算页-upsell页面-商品数据模块UI/UX验证测试完成")
