# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  <PERSON><PERSON><PERSON> <PERSON><PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  test_110688_mweb_checkout_delivery_window_ui_ux.py
@Description    :
@CreateTime     :  2025/7/28 03:27
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/28 03:27
"""
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_category.mweb_page_category import MWebCategorypage
import allure
import pytest
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_pages.mweb_page_checkout.mweb_page_checkout import MWebPageCheckout
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("H5结算页-delivery window模块UI/UX验证")
class TestCheckoutSingleCartChangeDateUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.huimin]
    @allure.title("H5结算页-delivery window模块UI/UX验证")
    @pytest.mark.smoke
    def test_110688_mweb_checkout_delivery_window_ui_ux(self, phone_page: dict, h5_autotest_header):
        """
        【108559】 H5结算单个购物车切换日期UI/UX验证

        测试步骤：
        1. 清空购物车
        2. 从分类页加购商品
        3. 进入购物车
        4. 点击去结算按钮，data-testid="btn-checkout"
        5. 处理可能存在的upsell弹窗，data-testid="wid-popup-checkoutupsell"
           如果检测到upsell弹窗，然后点击继续按钮，data-testid="btn-continue"，
           如果未检测到upsell弹窗，直接进行下一步
        6. 验证进入结算页 page_url="/order/checkout?cart_domain=grocery"
        7.向下滑动页面，查看是否有delivery window 模块 data-testid="wid-delivery-window-item-desc"
        8.delivery window存在title  data-testid="wid-delivery-window-item-desc"
        9.delivery window存在送达时间  data-testid="wid-delivery-window-item-arrival-before"
        10.delivery window存在文案 data-testid="wid-delivery-window-item-content"
        11.delivery window存在价格 data-testid="wid-delivery-window-item-delivery-price"
        12.右侧可以选择这个时间窗口 data-testid="wid-delivery-window-item-checkbox"，没有选择时data-selected="false"，选择后data-selected="true"
        13.验证选择时间窗口后订单总金额会增加，订单总金额 data-testid="wid-checkout-total-amount"，增加的金额为时间窗口的金额
           取消选择订单总金额会减少，验证金额正确
        14.验证选择时间窗口后，页面向下滑动，总计中有定时送达服务data-testid="wid-order-summary-item-guaranteed_delivery"
                """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        p.wait_for_timeout(2000)

        # 1.清空购物车
        empty_cart(h5_autotest_header)

        # 2. 使用封装方法加购Local Delivery商品

        with allure.step("使用封装方法加购Local Delivery商品"):
            # 创建分类页面对象
            category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c, page_url="/category/sale")
            p.wait_for_timeout(2000)

            # 检查筛选按钮是否存在
            filter_button = p.get_by_test_id("btn-sub-category-filter")
            log.info(f"筛选按钮是否可见: {filter_button.is_visible(timeout=1000)}")

            # 检查Local Delivery元素选择器
            log.info(f"Local Delivery元素选择器: {mweb_category_ele.ele_local_delivery}")

            # 调用封装方法加购Local Delivery商品
            added_count = category_page.add_products_from_home_by_filter(
                filter_name="Local Delivery",
                filter_id=mweb_category_ele.ele_local_delivery,  # 直接传递字符串test_id
                count=2,  # 加购2个商品
            )
            p.wait_for_timeout(5000)  # 增加等待时间
            log.info(f"成功加购{added_count}个商品")
            assert added_count > 0, "未能成功加购商品"

            # 3. 进入购物车页面
        with allure.step("进入购物车页面"):
            # 创建购物车页面对象
            cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
            p.wait_for_timeout(2000)

            # 关闭可能出现的广告弹窗
            if p.locator("//img[contains(@aria-label, 'close button')]").all():
                p.locator("//img[contains(@aria-label, 'close button')]").click()
                log.info("关闭广告弹窗")

        # with allure.step("从分类页加购商品"):
        #     try:
        #         # 进入分类页
        #         category_page = MWebCategorypage(p, h5_autotest_header, browser_context=c)
        #         # 添加商品到购物车
        #         category_page.add_to_local_product_cart_from_category()
        #         p.wait_for_timeout(2000)
        #         log.info("商品加购成功")
        #     except Exception as e:
        #         log.error(f"加购商品失败: {str(e)}")
        #         raise

        # 4. 点击去结算按钮
        with allure.step("点击去结算按钮"):
            try:
                checkout_button = p.get_by_test_id("btn-checkout")
                expect(checkout_button).to_be_visible()
                checkout_button.click()
                p.wait_for_timeout(2000)
                log.info("成功点击去结算按钮")
            except Exception as e:
                log.error(f"点击去结算按钮失败: {str(e)}")
                raise

        # 5. 处理可能存在的upsell弹窗（点击结算按钮后）
        with allure.step("处理可能存在的upsell弹窗（点击结算按钮后）"):
            try:
                # 检测upsell弹窗
                upsell_popup = p.get_by_test_id("wid-popup-checkoutupsell")
                if upsell_popup.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗（点击结算按钮后）")
                    # 点击继续按钮
                    continue_button = p.get_by_test_id("btn-continue")
                    if continue_button.is_visible(timeout=2000):
                        continue_button.click()
                        p.wait_for_timeout(3000)
                        log.info("成功点击继续按钮")
                        
                        # 确认upsell弹窗已关闭
                        upsell_closed = not upsell_popup.is_visible(timeout=5000)
                        log.info(f"upsell弹窗是否已关闭: {upsell_closed}")
                        if not upsell_closed:
                            log.warning("upsell弹窗仍然可见，尝试再次关闭")
                            # 尝试按ESC键关闭
                            p.keyboard.press("Escape")
                            p.wait_for_timeout(2000)
                    else:
                        log.warning("找到upsell弹窗但未找到继续按钮")
                else:
                    log.info("未检测到upsell弹窗（点击结算按钮后），直接进行下一步")
                    p.wait_for_timeout(2000)
            except Exception as e:
                log.warning(f"处理upsell弹窗时出现异常: {str(e)}")
                log.info("继续执行下一步")

        # 6. 验证进入结算页
        with allure.step("验证进入结算页"):
            checkout_page = MWebPageCheckout(p, h5_autotest_header, browser_context=c, page_url="/order/checkout?cart_domain=grocery")
            assert "/order/checkout?cart_domain=grocery" in p.url, "未成功进入结算页"
            log.info("成功进入结算页")

        # 6.1 再次检查并处理可能的upsell弹窗（进入结算页后）
        with allure.step("再次检查并处理可能的upsell弹窗（进入结算页后）"):
            try:
                upsell_popup = p.get_by_test_id("wid-popup-checkoutupsell")
                if upsell_popup.is_visible(timeout=3000):
                    log.info("检测到upsell弹窗（进入结算页后）")
                    continue_button = p.get_by_test_id("btn-continue")
                    if continue_button.is_visible(timeout=2000):
                        continue_button.click()
                        p.wait_for_timeout(3000)
                        log.info("成功点击继续按钮（进入结算页后）")
                        
                        # 再次确认upsell弹窗已关闭
                        upsell_closed = not upsell_popup.is_visible(timeout=5000)
                        log.info(f"upsell弹窗是否已关闭（进入结算页后）: {upsell_closed}")
                else:
                    log.info("未检测到upsell弹窗（进入结算页后）")
            except Exception as e:
                log.warning(f"处理upsell弹窗时出现异常（进入结算页后）: {str(e)}")

        # 6.2 最终确认页面状态
        with allure.step("最终确认页面状态"):
            # 等待页面完全加载
            p.wait_for_timeout(3000)
            
            # 检查是否还有任何弹窗
            all_popups = p.locator("[data-testid*='popup']").all()
            visible_popups = [popup for popup in all_popups if popup.is_visible()]
            log.info(f"页面上可见的弹窗数量: {len(visible_popups)}")
            
            for i, popup in enumerate(visible_popups):
                popup_testid = popup.get_attribute("data-testid")
                log.info(f"可见弹窗{i}: {popup_testid}")
            
            # 记录当前页面状态
            log.info(f"当前页面URL: {p.url}")
            log.info("页面状态检查完成，准备查找delivery window")

        # 7. 向下滑动页面，查看是否有delivery window模块
        with allure.step("向下滑动页面，查看是否有delivery window模块"):
            p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            p.wait_for_timeout(2000)
            
            # 添加调试信息
            log.info(f"当前页面URL: {p.url}")
            log.info("开始查找delivery window模块")
            
            delivery_window = p.get_by_test_id("wid-delivery-window-item-desc")
            is_visible = delivery_window.is_visible(timeout=5000)
            log.info(f"delivery window模块是否可见: {is_visible}")
            
            if not is_visible:
                # 尝试查找页面上所有包含delivery的元素
                all_delivery_elements = p.locator("[data-testid*='delivery']").all()
                log.info(f"页面上包含delivery的元素数量: {len(all_delivery_elements)}")
                for i, elem in enumerate(all_delivery_elements):
                    test_id = elem.get_attribute("data-testid")
                    log.info(f"delivery元素{i}: {test_id}")
                
                log.info("未找到delivery window模块，测试结束")
                return
            
            log.info("找到delivery window模块")

        # 8-12. 验证delivery window模块各个元素
        with allure.step("验证delivery window模块"):
            # 8. 验证title
            title_element = p.get_by_test_id("wid-delivery-window-item-desc")
            assert title_element.is_visible(), "delivery window title不可见"
            title_text = title_element.text_content()
            log.info(f"delivery window title: {title_text}")

            # 9. 验证送达时间
            arrival_time = p.get_by_test_id("wid-delivery-window-item-arrival-before")
            assert arrival_time.is_visible(), "delivery window送达时间不可见"
            arrival_text = arrival_time.text_content()
            log.info(f"delivery window送达时间: {arrival_text}")

            # 10. 验证文案
            content_text = p.get_by_test_id("wid-delivery-window-item-content")
            assert content_text.is_visible(), "delivery window文案不可见"
            content = content_text.text_content()
            log.info(f"delivery window文案: {content}")

            # 11. 验证价格
            price_element = p.get_by_test_id("wid-delivery-window-item-delivery-price")
            assert price_element.is_visible(), "delivery window价格不可见"
            price_text = price_element.text_content()
            log.info(f"delivery window价格: {price_text}")

            # 12. 验证选择功能
            checkbox = p.get_by_test_id("wid-delivery-window-item-checkbox")
            assert checkbox.is_visible(), "delivery window选择框不可见"
            
            # 验证初始状态
            initial_selected = checkbox.get_attribute("data-selected")
            assert initial_selected == "false", "delivery window初始状态应为未选择"
            log.info(f"delivery window初始选择状态: {initial_selected}")

        # 13. 验证选择时间窗口后订单总金额变化
        with allure.step("验证选择时间窗口后订单总金额变化"):
            # 获取初始订单总金额
            order_amount_element = p.get_by_test_id("wid-checkout-total-amount")
            assert order_amount_element.is_visible(), "订单总金额元素不可见"
            initial_amount_text = order_amount_element.text_content().strip()
            initial_amount = float(initial_amount_text.replace('$', '').replace(',', ''))
            log.info(f"初始订单总金额: ${initial_amount}")
            
            # 获取delivery window价格
            window_price_text = price_element.text_content().strip()
            window_price = float(window_price_text.replace('$', '').replace(',', ''))
            log.info(f"delivery window价格: ${window_price}")
            
            # 点击选择时间窗口
            checkbox.click()
            p.wait_for_timeout(2000)
            
            # 验证选择后状态
            selected_state = checkbox.get_attribute("data-selected")
            assert selected_state == "true", "delivery window选择后状态不正确"
            log.info(f"delivery window选择后状态: {selected_state}")
            
            # 验证选择后订单总金额增加
            selected_amount_text = order_amount_element.text_content().strip()
            selected_amount = float(selected_amount_text.replace('$', '').replace(',', ''))
            log.info(f"选择后订单总金额: ${selected_amount}")
            
            expected_amount = initial_amount + window_price
            assert abs(selected_amount - expected_amount) < 0.01, f"选择时间窗口后金额不正确，期望${expected_amount}，实际${selected_amount}"
            log.info(f"验证选择时间窗口后金额增加正确: ${initial_amount} + ${window_price} = ${selected_amount}")

        # 14. 验证选择时间窗口后，页面向下滑动，总计中有定时送达服务
        with allure.step("验证选择时间窗口后，页面向下滑动，总计中有定时送达服务"):
            p.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            p.wait_for_timeout(2000)
            
            guaranteed_delivery = p.get_by_test_id("wid-order-summary-item-guaranteed_delivery")
            assert guaranteed_delivery.is_visible(), "定时送达服务项不可见"
            guaranteed_delivery_text = guaranteed_delivery.text_content()
            log.info(f"定时送达服务: {guaranteed_delivery_text}")

        # 验证取消选择时间窗口
        with allure.step("验证取消选择时间窗口"):
            # 向上滑动回到delivery window区域
            p.evaluate("window.scrollTo(0, 0)")
            p.wait_for_timeout(1000)
            p.evaluate("window.scrollTo(0, document.body.scrollHeight/2)")
            p.wait_for_timeout(2000)
            
            # 取消选择时间窗口
            checkbox.click()
            p.wait_for_timeout(2000)
            
            # 验证取消选择后状态
            unselected_state = checkbox.get_attribute("data-selected")
            assert unselected_state == "false", "delivery window取消选择后状态不正确"
            log.info(f"delivery window取消选择后状态: {unselected_state}")
            
            # 验证取消选择后订单总金额恢复
            final_amount_text = order_amount_element.text_content().strip()
            final_amount = float(final_amount_text.replace('$', '').replace(',', ''))
            log.info(f"取消选择后订单总金额: ${final_amount}")
            
            assert abs(final_amount - initial_amount) < 0.01, f"取消选择时间窗口后金额不正确，期望${initial_amount}，实际${final_amount}"
            log.info(f"验证取消选择时间窗口后金额恢复正确: ${final_amount}")

        log.info("H5结算页delivery window模块UI/UX验证测试完成")
