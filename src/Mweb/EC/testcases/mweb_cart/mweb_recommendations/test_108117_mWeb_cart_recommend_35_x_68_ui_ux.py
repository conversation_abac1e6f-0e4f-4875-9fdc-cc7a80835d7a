import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_recommendations_page import MWebRecommendationPage
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_trade_in_page import MWebTradeInPage
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck


@allure.story("【108117】 $35<X<$68-Recommended items页面UI/UX验证")
class TestMWebEmptyCartUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.mweb_todu, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【108117】 $35<X<$68-Recommended items页面UI/UX验证")
    def test_108117_mWeb_cart_recommend_35_x_68_ui_ux(self, phone_page: dict, h5_autotest_header,
                                                      h5_open_and_close_trace):
        """
        【108117】 $35<X<$68-Recommended items页面UI/UX验证
        测试步骤：
        1. 清空购物车后从推荐模块加购商品
        2. 验证购物车换购模块状态
        3. 进入换购页面，验证按钮状态和提示信息
        4. 进入凑单页面，验证各项功能
        5. 完成凑单后返回购物车，验证状态
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")

        # 初始化页面和清空购物车
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info(f"初始化失败: {str(e)}")

        # 初始化页面对象
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        trade_in_page = MWebTradeInPage(p, h5_autotest_header, browser_context=c)
        recommendation_page = MWebRecommendationPage(p, h5_autotest_header, browser_context=c)

        # 加购商品到35-68区间并验证换购模块
        cart_page.add_to_cart_until_trade_in_range(p, min_amount=35, max_amount=68)

        # 验证购物车换购模块
        cart_page.check_trade_in_module()

        # 进入换购页面并验证
        cart_page.go_to_trade_in_page()
        trade_in_page.verify_trade_in_page_status()

        # 验证换购页面元素状态
        cart_page.verify_trade_in_page_status()

        # 进入并验证凑单页面
        cart_page.verify_recommendations_page()
        # 进入凑单页面并验证
        trade_in_page.go_to_recommendations_page()
        recommendation_page.verify_recommendations_page()
        recommendation_page.operate_recommendations_page()

        # 验证最终状态并返回购物车
        cart_page.complete_trade_in_flow()
        # 完成换购流程
        trade_in_page.complete_trade_in_flow()
