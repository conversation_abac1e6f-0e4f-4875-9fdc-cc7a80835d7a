import allure
import pytest
from playwright.sync_api import Page, sync_playwright
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart


@allure.story("H5购物车-空购物车UI/UX验证")
class TestMwebEmptyCartUIUX:
    pytestmark = [pytest.mark.mweb_regression, pytest.mark.zhuli]
    @allure.title("H5购物车-空购物车UI/UX验证")
    @pytest.mark.present
    def test_109506_MWeb_empty_cart_ui_ux(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        """
        【109506】 H5购物车-空购物车UI/UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        p.wait_for_timeout(3000)
        # 清空购物车
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 断言空购物车img存在
        p.reload()
        p.wait_for_timeout(3000)
        assert p.locator("//img[@alt='cart empty image']").is_visible()
        # # 断言空购物车 文案存在
        assert p.get_by_text("Your cart is empty").is_visible()
        # 点击空购物车的start_shopping按钮
        cart_page.FE.ele("//button[text()='Start shopping']").click()
        p.wait_for_timeout(5000)
        # 断言跳转到了首页,判断能找到首页banner即可
        # assert p.get_by_test_id("wid-sidebar-item-top-charts").all()[0].is_visible()
