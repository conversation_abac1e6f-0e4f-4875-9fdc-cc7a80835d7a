import allure
import pytest
from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome as PH5
from src.api.zipcode import switch_zipcode
from src.common.commfunc import empty_cart
from src.common.commonui import scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


@allure.story("结算页-crv-购物车加购含crv商品验证结算页")
class TestH5HomePageCategoryVerify:
    pytestmark = [pytest.mark.h5cart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.zhongyuan]

    @allure.title("结算页-crv-购物车加购含crv商品验证结算页")
    @pytest.mark.h5home
    def test_112159_checkout_page_crv_product_verify(self, phone_page: dict, h5_autotest_header, h5_open_and_close_trace):
        _page:Page = phone_page["page"]
        _context = phone_page["context"]
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        ph5cart = MWebCartPage(_page, h5_autotest_header, _context, '')
        res = switch_zipcode(h5_autotest_header, "94538")
        assert res.get('object') == 'Success', f"切换zipcode失败，res={res}"
        _page.wait_for_timeout(3000)
        _page.goto(TEST_URL + "/product/Mexican-Coca-Cola-Soda/76702")
        _page.wait_for_timeout(3000)
        # 加购76702，如果当前商品不可购买，则用例设为SKIP状态
        if self.is_product_available(_page):
            _page.get_by_test_id("btn-add-cart").click()
            _page.wait_for_timeout(3000)
        else:
            pytest.skip("当前商品76702不可购买")

        # 加购fbw
        _page.goto(TEST_URL + "/product/Combo-Taiwanese-Sandwich-Strawberry-Mochi-Donut/2201902")
        if self.is_product_available(_page):
            _page.get_by_test_id("btn-add-cart").click()
            _page.wait_for_timeout(3000)
        else:
            pytest.skip("当前商品2201902不可购买")

        # 跳转购物车
        # _page.get_by_test_id("btn-mini-cart").click()
        _page.locator("//button/img[@alt='Go to cart']").click()
        _page.wait_for_timeout(3000)
        # 跳转结算页
        _page.get_by_test_id("btn-checkout").click()
        _page.wait_for_timeout(5000)

        # 如果有upsell，continue
        if _page.get_by_test_id("btn-continue").all():
            _page.get_by_test_id("btn-continue").click()
            _page.wait_for_timeout(2000)

        # 结算页验证
        scroll_one_page_until(_page, "div[data-testid='wid-order-summary-item-subtotal']")
        assert _page.get_by_test_id("wid-order-summary-item-subtotal").locator("//div[2]/span").text_content().startswith("$"), "结算页商品小计显示异常"
        assert _page.get_by_test_id("wid-order-summary-item-taxes").locator("//div[2]/span").text_content().startswith("$")
        service_fee = _page.get_by_test_id("wid-order-summary-item-service_fee").locator("//div[2]/span").all_text_contents()
        assert "$2.95" in service_fee or "FREE" in service_fee, "结算页服务费显示异常"
        delivery_fee = _page.get_by_test_id("wid-order-summary-item-delivery_fee").locator("//div[2]/span").all_text_contents()
        assert "$4.95" in delivery_fee or "$6.95" in delivery_fee, "结算页运费显示异常"
        beverage_container_fee = _page.get_by_test_id("wid-order-summary-item-beverage_container_fee").locator("//div[2]/span").all_text_contents()
        assert "$0.20" in beverage_container_fee

        _page.get_by_test_id("wid-order-summary-item-service_fee").locator("//*[name()='svg']").click()
        _page.wait_for_timeout(2000)
        assert _page.locator("#feeinfo-header").is_visible()
        _page.locator("#feeinfo-header i").click()


    def is_product_available(self, _page: Page) -> bool:
        if _page.query_selector("div[class*='open-app-content']"):
            _page.locator("css=div[class*='open-app-content']").click()
            _page.locator("//button[text()='Continue']").click()

            # pytest.skip("当前商品不可购买")
        if _page.get_by_test_id("btn-add-cart").is_enabled():
            return True
        else:
            return False




