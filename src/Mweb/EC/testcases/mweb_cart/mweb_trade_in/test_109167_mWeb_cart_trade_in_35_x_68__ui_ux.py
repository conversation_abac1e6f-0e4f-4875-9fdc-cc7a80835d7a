import allure
import pytest

from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_pages.mweb_page_cart.mweb_page_cart import MWebCartPage
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.common.commfunc import empty_cart, CommonCheck


@allure.story("【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证")
class TestMWebEmptyCartUIUX:
    pytestmark = [pytest.mark.h5cart, pytest.mark.regression, pytest.mark.transaction, pytest.mark.suqin]

    @allure.title("【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证")
    def test_109167_mWeb_cart_trade_in_35_x_68__ui_ux(self, phone_page: dict, h5_autotest_header,
                                                      h5_open_and_close_trace):
        """
        【109167】 购物车-换购模块加购金额 大于35 ，小于68 UX验证
        """
        p: Page = phone_page.get("page")
        c = phone_page.get("context")
        # 如果不是98011，切换回98011
        try:
            CommonCheck().set_porder(h5_autotest_header, 98011)
        except Exception as e:
            log.info("账号没有在98011下" + str(e))
        try:
            empty_cart(h5_autotest_header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 进入指定页面
        cart_page = MWebCartPage(p, h5_autotest_header, browser_context=c, page_url="/cart")
        # 滚动到指定位置-猜你喜欢
        scroll_one_page_until(p, mweb_cart_ele.ele_recommend_tab)

        # 获取猜你喜欢商品
        recommend_card = cart_page.FE.eles(mweb_cart_ele.ele_recommend_module_card)
        for index1, item1 in enumerate(recommend_card):
            # 加购推荐商品达
            item1.query_selector(u"//div[@data-testid='btn-atc-plus']").click()
            p.wait_for_timeout(3000)
            # 回到购物车第一个商品位置
            p.query_selector(f"{mweb_cart_ele.ele_cart_normal_card}" + "[1]").scroll_into_view_if_needed()
            p.wait_for_timeout(2000)
            total_fee = cart_page.FE.ele(mweb_cart_ele.ele_cart_normal_total + u"//span[2]").text_content().split("$")[1]
            print(total_fee)
            # 如果商品金额小于35，不显示购物车换购模块
            if float(total_fee) < 35:
                # ele_cart_trade_in = cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
                assert not cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
            # 如果商品金额大于35，小于68，显示购物车换购模块
            elif 35 <= float(total_fee) < 68:
                # 断言换购模块存在
                ele_cart_trade_in = cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in)
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购文案提示：还可以加xx 解锁换购()
                # 使用 text 选择器查找包含特定文本的元素
                unlock_deals_text = ele_cart_trade_in.query_selector('text="to unlock extra deals!"')
                # 断言文本存在
                assert unlock_deals_text
                # 如果你想更精确地匹配整个句子，可以这样做：
                full_text = ele_cart_trade_in.query_selector('text=/Add .+ to unlock extra deals!/')
                assert full_text
                # 如果你想获取实际的文本并进行更详细的断言：
                actual_text = full_text.inner_text()
                assert "to unlock extra deals!" in actual_text

                # 断言 换购卡片上不可加购
                assert cart_page.FE.ele(
                    mweb_cart_ele.ele_cart_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]").is_visible()
                # 点击换购卡片进入换购页面，然后再返回购物车
                # 点击查看更多，跳转换购页，然后再返回购物车
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_button).click()

            # 如果商品金额大于68，购物车换购即可加购
            elif float(total_fee) >= 68:
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in).is_visible()
                # 断言 换购卡片上可加购
                assert cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).is_visible()
                # 点击加购换购商品
                cart_page.FE.ele(mweb_cart_ele.ele_cart_trade_in_card_add).click()
                # 断言换购商品加入购物车成功
            if index1 == 20:
                break


