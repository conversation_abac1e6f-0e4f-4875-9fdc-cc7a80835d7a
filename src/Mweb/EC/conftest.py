import asyncio
import codecs
import copy
import datetime
import json
import multiprocessing
import os.path
import time

import pytest
from _pytest.fixtures import FixtureRequest
from playwright.sync_api import Page, sync_playwright

from src.api.porder import query_simple_preorder_v1, update_pickup_date
from src.common.get_header import login_header, anony_header
from src.config.base_config import BASE_URL, TEST_URL
from src.config.weee.log_help import log
from src.utils.readYamlFile import ReadYamlFile


class CaseState:
    # 创建进程锁
    __lock = multiprocessing.Lock()

    @classmethod
    def set_case_state(cls, state, test_name):
        """
        更新测试状态
            :param state: 'passed' 'failed' 'skipped'
            :param test_name: 测试类名
        """
        with cls.__lock:
            if state == 'passed':
                cls.__set_case_state_passed(test_name)
            elif state == 'failed':
                cls.__set_case_state_failed(test_name)

    @classmethod
    def __set_case_state_passed(cls, test_name):
        """
        用例状态为成功，则判断类名是否在passed中，若不存在，则添加；并删除其他状态中的类名
            :param test_name: 测试类名
        """
        state_data = ReadYamlFile.read_yaml('case_state.yaml')
        if test_name not in state_data['passed']:
            state_data['passed'].append(test_name)
        if test_name in state_data['failed']:
            state_data['failed'].remove(test_name)
        ReadYamlFile.write_yaml('case_state.yaml', state_data)

    @classmethod
    def __set_case_state_failed(cls, test_name):
        """
        用例状态为失败，则判断类名是否在failed中，若不存在，则添加；并删除其他状态中的类名
            :param test_name: 测试类名
        """
        state_data = ReadYamlFile.read_yaml('case_state.yaml')
        if test_name not in state_data['failed']:
            state_data['failed'].append(test_name)
        if test_name in state_data['passed']:
            state_data['passed'].remove(test_name)
        ReadYamlFile.write_yaml('case_state.yaml', state_data)


class CaseTimeRecord:
    start_time, end_time = None, None
    record_map: dict = {}

    @classmethod
    def set_case_time_start(cls, case_name, start):
        cls.record_map[f"{case_name}_start"] = start

    @classmethod
    def set_case_time_end(cls, case_name, end):
        cls.record_map[f"{case_name}_end"] = end


class ReportPage:
    """
    ReportPage类，用于储存[失败截图]所需的page实例
    """
    # 初始化page对象
    __page = None

    @classmethod
    def set(cls, page):
        """
        将外部传入的page实例，存储到类变量中
            :param page: page实例
        """
        cls.__page = page

    @classmethod
    def get(cls):
        """
        获取当前处理页面的page实例
        """
        return cls.__page


# @pytest.fixture(scope='session')
# 这个账号登陆不了，需要验证码
def bronze_header():
    token = None
    try:
        token = login_header(email='<EMAIL>', password='Aa123456')
    except Exception as e:
        log.debug("获取bronze token失败")

    if token and token.get('authorization'):
        return token
    else:
        raise Exception(f"get bronze token failed: token={token}")


@pytest.fixture(scope='session')
def h5_autotest_header():
    token = login_header(email='<EMAIL>', password='A1234567')
    if token.get('authorization'):
        try:
            update_pickup_date(token, (datetime.datetime.now() + datetime.timedelta(days=2)).strftime('%Y-%m-%d'))
        except Exception as e:
            log.info("更新用户送货日期失败" + str(e))
        return token
    else:
        raise Exception(f"get autotest token failed: token={token}")


@pytest.fixture(scope='session')
def h5_anony_header():
    _h5_anony_header = anony_header()
    return _h5_anony_header




@pytest.fixture(scope='session')
def porder(h5_autotest_header) -> dict:
    porder = query_simple_preorder_v1(h5_autotest_header)
    return porder


@pytest.fixture(scope='session')
def playwright():
    print('启动playwright服务')
    # 启动playwright服务，创建browser、context实例
    playwright = sync_playwright().start()
    return playwright


@pytest.fixture(scope='session')
def phone_page(playwright, h5_autotest_header, porder) -> Page:
    iphone_14 = playwright.devices['iPhone 14 Pro']
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(
        **iphone_14,
    )

    if "sayweee" in TEST_URL:
        token = h5_autotest_header['authorization'].split(" ")[1]
        cookies = [
            {
                "name": "auth_token",
                "value": token,
                "domain": TEST_URL.split("/")[2],
                "path": "/",
            },
            {
                "name": "user_id",
                "value": str(porder['object']['id']),  # 如果更换另一个用户，这个user_id需要更换，此处于7.11日发生过坑
                "domain": TEST_URL.split("/")[2],
                "path": "/",
            }
        ]

        context.add_cookies(cookies)
    # context.storage_state(path='./state.json')
    print("主服务context_id===>", id(context))
    context.storage_state()
    page = context.new_page()

    yield {"page": page, "context": context}
    # 将page实例存储到Page类中
    ReportPage.set(page)
    # yield ReportPage.get()
    try:
        page.close()
    except Exception as e:
        log.info("关闭page失败" + str(e))

    try:
        context.tracing.stop(path=os.path.dirname(__file__) + '\\trace\\trace.zip')
        print("主服务关闭中")
    except Exception as e:
        print("主服务trace已经关闭")

    try:
        context.close()
        browser.close()
        playwright.stop()
        print("start===>", CaseTimeRecord.record_map.get("test_track_for_ui_start"))
        print("stop===>", CaseTimeRecord.record_map.get("test_track_for_ui_end"))
        print('关闭playwright服务')
    except Exception as e:
        print("关闭context, browser或playwright失败" + str(e))


@pytest.fixture(scope='session')
def not_login_phone_page(playwright, h5_autotest_header, porder) -> Page:
    iphone_14 = playwright.devices['iPhone 14 Pro']
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(
        **iphone_14,
    )

    print("主服务context_id===>", id(context))
    context.storage_state()
    page = context.new_page()

    yield {"page": page, "context": context}
    # 将page实例存储到Page类中
    ReportPage.set(page)
    # yield ReportPage.get()
    try:
        page.close()
    except Exception as e:
        log.info("关闭page失败" + str(e))

    try:
        context.tracing.stop(path=os.path.dirname(__file__) + '\\trace\\trace.zip')
        print("主服务关闭中")
    except Exception as e:
        print("主服务trace已经关闭")

    try:
        context.close()
        browser.close()
        playwright.stop()
        print("start===>", CaseTimeRecord.record_map.get("test_track_for_ui_start"))
        print("stop===>", CaseTimeRecord.record_map.get("test_track_for_ui_end"))
        print('关闭playwright服务')
    except Exception as e:
        print("关闭context, browser或playwright失败" + str(e))


@pytest.fixture(scope="function")
def h5_open_and_close_trace(request: FixtureRequest, phone_page: dict):
    trace_file_name = request.node.name
    trace_file_name = codecs.decode(trace_file_name, 'unicode_escape')
    print("trace_file_name===>", trace_file_name)
    context_case = phone_page["context"]
    try:
        context_case.tracing.start(screenshots=True, snapshots=True, sources=True)
        log.info("用例trace开启中...")
    except Exception as e:
        print("用例trace已经开启" + str(e))

    yield

    try:
        context_case.tracing.stop(path=f"./trace/{os.getenv('BUILD_NUMBER', 0)}/{trace_file_name}.zip")
        print("用例trace关闭中...")
    except Exception as e:
        print("用例trace已经关闭" + str(e))


CASE_DETAIL = []  # 用例详情
# 每个用例的执行结果
EVERY_CASES_RESULT = []
# 存入数据库的结果数据
DB_EVERY_CASES_RESULT = []


# def get_case_type(func, case_name: str):
#     case_type = func.__annotations__.get("case_type")
#
#     if case_type:
#         return case_type
#     if "simple" in case_name.split("::")[0].lower():
#         return "simple"
#     else:
#         return "scene"
#
#
# @pytest.hookimpl(hookwrapper=True, tryfirst=True)
# def pytest_runtest_makereport(item, call):
#     """
#     用例执行结果 hook 函数
#     :param item:
#     :param call:
#     :return:
#     """
#     result = yield
#     report = result.get_result()
#     _case_nodeid = item.nodeid  # 获取用例函数的名称
#
#     step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
#     out_message = ''  # 输出信息
#     error_message = ''  # 错误信息
#     step_desc = ''  # 步骤描述
#     case_name = item.nodeid  # 获取用例函数的名称
#     dev_case_name = item.originalname  # 获取用例函数的名称
#     RunResult.CURRENT_CASE_NAME = item.originalname
#     desc = '' if item.function.__doc__ is None else item.function.__doc__  # 获取用例函数的名称的文档
#     run_time = round(report.duration, 4)  # 获取用例setup执行时间
#
#     if report.when == 'setup':
#         step_name = "SetUp"
#         if report.outcome == 'passed':
#             step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
#
#         elif report.outcome == 'failed':
#             step_status = 2  # 0：成功，1：失败，2：错误，3：跳过
#             out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
#             error_message = report.longreprtext  # 获取用例执行失败的错误信息
#             log.error('\n异常信息: {}'.format(call.excinfo.value))
#             log.error('\n详细异常错误定位: {}'.format(report.longreprtext))
#         elif report.outcome == 'skipped':
#             step_status = 3  # 0：成功，1：失败，2：错误，3：跳过
#
#         # 测试开发报告详情
#         CASE_DETAIL.append(
#             {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
#              "run_time": run_time, "out_message": out_message, "error_message": error_message})
#
#     elif report.when == 'call':
#         step_name = "Call"
#         if report.outcome == 'passed':
#             step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
#         elif report.outcome == 'failed':
#             step_status = 1  # 0：成功，1：失败，2：错误，3：跳过
#             out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
#             error_message = report.longreprtext  # 获取用例执行失败的错误信息
#             log.error('\n用例异常信息：{}'.format(call.excinfo.value))
#             log.error('\n详细异常错误定位：{}'.format(report.longreprtext))
#         # 测试开发报告详情
#         CASE_DETAIL.append(
#             {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
#              "run_time": run_time, "out_message": out_message, "error_message": error_message})
#
#
#     elif report.when == 'teardown':
#         step_name = "TearDown"
#         if report.outcome == 'passed':
#             step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
#         elif report.outcome == 'failed':
#             step_status = 2  # 0：成功，1：失败，2：错误，3：跳过
#             out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
#             error_message = report.longreprtext  # 获取用例执行失败的错误信息
#             log.error('\n后置条件异常: {}'.format(call.excinfo.value))
#             log.error('\n详细异常错误定位: {}'.format(report.longreprtext))
#         elif report.outcome == 'skipped':
#             step_status = 3  # 0：成功，1：失败，2：错误，3：跳过
#
#         # 测试报告详情
#         CASE_DETAIL.append(
#             {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
#              "run_time": run_time, "out_message": out_message, "error_message": error_message})
#
#         # ************************* >> 用例执行结果处理  << *******************************
#         # ************************* >> 测试人员报告数据处理 start << **************************
#         case_details = []  # 用例详情列表
#         case_status = 0  # 0：成功，1：失败，2：错误，3：跳过
#         run_time_sum = 0  # 用例执行时间总和
#
#         for i in CASE_DETAIL:
#             if i['case_name'] == case_name:
#                 case_details.append(i)  # 将用例详情添加到用例详情列表
#
#         for i in case_details:  # 遍历用例详情列表
#             run_time_sum += i['run_time']  # 获取用例执行时间总和
#             if i['status'] == 2:  # 如果用例详情列表中的用例状态为2，即用例错误
#                 case_status = 2
#                 break
#             elif i['status'] == 1:  # 如果用例详情列表中的用例状态为1，即用例失败
#                 case_status = 1
#                 break
#             elif i['status'] == 3:  # 如果用例详情列表中的用例状态为3，即用例跳过
#                 case_status = 3
#                 break
#             else:  # 如果用例详情列表中的用例状态为3，即用例跳过
#                 num = 0
#                 num += 1
#
#         # case_type = item.function.__annotations__.get("case_type")
#         case_type = get_case_type(func=item.function, case_name=item.nodeid)
#         # 测试报告每个用例结果
#         EVERY_CASES_RESULT.append(
#             {"case_name": case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
#              "case_detail": case_details, "case_type": case_type})  # 每个用例的结果
#
#         DB_EVERY_CASES_RESULT.append(
#             {"case_name": case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
#              "case_detail": [i for i in case_details if i['step_name'] == 'Call'], "case_type": case_type,
#              "case_url": "item.instance.response_all.request.path_url",
#              "case_response_status": "item.instance.response_all.status_code", "case_node_id": item.nodeid})
#
#         res = copy.deepcopy(EVERY_CASES_RESULT)
#         # case_desc加入case_detail
#         [j.update({"case_desc": r["desc"]}) for r in res for j in r['case_detail']]
#         # 将执行成功的用例过滤掉，邮件中只展示错误的用例
#         case_detail = [j for item in res for j in item["case_detail"] if j["status"] != 0 and j["step_name"] == "Call"]
#         # 将run_time和error_message置空，否则邮件中错误用例会显示2次（rerun=1）
#         [item.update({"run_time": "", "error_message": ""}) for item in case_detail]
#         # 将同名用例合并，只显示一条
#         new_list = [dict(d) for d in (set([tuple(d.items()) for d in case_detail]))]
#         # 用例名太长，截取最后一部分作为用例名
#         [item.update({"case_name": item["case_name"].split("::")[-1]}) for item in new_list]
#         # RunResult.EVERY_CASE_RES中保存的是错误用例的信息
#         RunResult.EVERY_CASE_RES = new_list
#
#         #############################根据模块分组############################
#         # all_case_json_string = copy.deepcopy(EVERY_CASES_RESULT)
#         # new_case_json_dict = []
#         # for item in all_case_json_string:
#         #     case_desc = item["desc"].replace("\n", "")
#         #     case_module = item["case_name"].split("/")[4]
#         #     case_status = item["status"]
#         #     new_case_json_dict.append(
#         #         {
#         #             "case_desc": case_desc,
#         #             "case_module": case_module,
#         #             "case_status": case_status
#         #         }
#         #     )
#         #
#         # # 根据module分组
#         # new_list_sort = sorted(new_case_json_dict, key=lambda x: (x["case_module"], x["case_status"]))
#         # new_list_group = itertools.groupby(new_list_sort, key=lambda x: (x["case_module"]))
#         # final_data_list = []
#         # for key, gro in new_list_group:
#         #     j = list(gro)
#         #     final_data = {
#         #         "module": key,
#         #         "success": len([i for i in j if i["case_status"] == 0]),
#         #         'fail': len([i for i in j if i["case_status"] == 1]),
#         #         'error': len([i for i in j if i["case_status"] == 2]),
#         #         'skip': len([i for i in j if i["case_status"] == 3]),
#         #     }
#         #     final_data_list.append(final_data)
#         # print("final_data_list===>", final_data_list)
#         # RunResult.GROUP_BY_RESULT = final_data_list
#
#         # ******************** >> 测试人员报告数据处理 end  << **************************
#
#         # ******************** >> 开发数据处理 start << **************************
#
#         dev_case_details = []  # 用例详情列表
#
#         dev_out_message = ''  # 开发者输出信息
#         dev_error_message = ''  # 开发者错误信息
#
#         # 开发报告详情
#         for i in RunResult.DEV_EVERY_CASES_RES:  # 遍历用例详情列表
#             if i['case_name'] == dev_case_name:
#                 dev_case_details.append(i)  # 将用例详情添加到用例详情列表
#         for i in CASE_DETAIL:
#             if i['case_name'] == case_name:
#                 if i['status'] == 0:  # 如果用例详情列表中的用例状态为0，即用例成功
#                     # 将数据添加到dev测试报告数据列表中
#                     if len(i['out_message']) > 0:
#                         dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
#
#                 if i['status'] == 2:  # 如果用例详情列表中的用例状态为2，即用例错误
#                     # 将数据添加到dev测试报告数据列表中
#                     if len(i['error_message']) > 0:
#                         dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
#                         dev_error_message = '\n' + i['step_name'] + ':\n' + i['error_message']
#                 elif i['status'] == 1:  # 如果用例详情列表中的用例状态为1，即用例失败
#                     # 将数据添加到dev测试报告数据列表中
#                     if len(i['error_message']) > 0:
#                         dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
#                         dev_error_message = '\n' + i['step_name'] + ':\n' + i['error_message']
#                 elif i['status'] == 3:  # 如果用例详情列表中的用例状态为3，即用例跳过
#                     # 将数据添加到dev测试报告数据列表中
#                     if len(i['out_message']) > 0:
#                         dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
#
#         # 开发测试报告每个用例结果
#         RunResult.DEV_EVERY_CASES_RESULT.append(
#             {"case_name": dev_case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
#              "case_detail": dev_case_details, "out_message": dev_out_message,
#              "error_message": dev_error_message})  # 每个用例的结果
#
#         # print("DB_EVERY_CASES_RESULT====>", DB_EVERY_CASES_RESULT)
#         # print("EVERY_CASES_RESULT====>", EVERY_CASES_RESULT)
#
#         # ******************** >> 开发报告数据处理 end << **************************
#
#
# def pytest_terminal_summary(terminalreporter, config):
#     """
#     收集测试结果
#     """
#     pytest_total_num = terminalreporter._numcollected
#     pass_num = len(terminalreporter.stats.get('passed', []))  # 用例通过数
#     fail_num = len(terminalreporter.stats.get('failed', []))  # 用例失败数
#     error_num = len(terminalreporter.stats.get('error', []))  # 用例错误数
#     skip_num = len(terminalreporter.stats.get('skipped', []))  # 用例跳过数
#     RunResult.end_time = time.strftime("%Y-%m-%d %H:%M:%S")  # 测试结束时间
#     RunResult.duration = time.strftime("%H:%M:%S",
#                                        time.gmtime(time.time() - terminalreporter._sessionstarttime))  # 测试耗时转换成时分秒
#     RunResult.passed = pass_num  # 用例通过数
#     RunResult.failed = fail_num  # 用例失败数
#     RunResult.errors = error_num  # 用例错误数
#     RunResult.skipped = skip_num  # 用例跳过数
#     total_num = pass_num + fail_num + error_num + skip_num  # 用例总数
#     if total_num != 0:
#         RunResult.pass_rate = str(round(pass_num / total_num * 100, 2)) + '%'  # 用例通过率
#         RunResult.skip_rate = str(round(skip_num / total_num * 100, 2)) + '%'  # 用例跳过率
#         RunResult.failure_rate = str(round(fail_num / total_num * 100, 2)) + '%'  # 用例失败率
#         RunResult.error_rate = str(round(error_num / total_num * 100, 2)) + '%'  # 用例错误率
#     else:
#         RunResult.pass_rate = '0.00%'
#         RunResult.skip_rate = '0.00%'
#         RunResult.failure_rate = '0.00%'
#         RunResult.error_rate = '0.00%'
#
#     print("terminal.stats.passed", terminalreporter.stats.get('passed', []))
#     print("terminal.stats.failed", terminalreporter.stats.get('failed', []))
#
#     def get_base_data():
#         return {
#             "title": weeeConfig.report_title,
#             "start_time": RunResult.start_time,
#             "end_time": RunResult.end_time,
#             "duration": RunResult.duration,
#             "tester": weeeConfig.report_tester,
#             "description": weeeConfig.report_description
#         }
#
#     def get_total_data():
#         return {
#             "pass_num": RunResult.passed,
#             "pass_percent": RunResult.pass_rate,
#             "fail_num": RunResult.failed,
#             "fail_percent": RunResult.failure_rate,
#             "error_num": RunResult.errors,
#             "error_percent": RunResult.error_rate,
#             "skip_num": RunResult.skipped,
#             "skip_percent": RunResult.skip_rate,
#         }
#
#     # Jenkins 参加合并报告汇总数据
#     platform = os.getenv("upstream_env", None)
#     upstream_branch = os.getenv("upstream_branch", None)
#     upstream_build_number = os.getenv("upstream_build_number", None)
#     if upstream_branch is not None and upstream_build_number is not None:
#         version = upstream_branch + '[' + upstream_build_number + ']'
#     else:
#         version = None
#     app = os.getenv("upstream_app", None)
#
#     CASE_RESULT = {
#         "base": {
#             **get_base_data(),
#             "platform": platform,
#             "version": version,
#             "app": app,
#         },
#         "total": get_total_data(),
#     }
#
#     # ============================= 将每个用例的信息保存至数据库，方便统计, 开始 ============================
#     # 到jenkins上不使用明文的用户名和密码，使用get_secret()获得
#     db_config = get_secret()
#
#     if os.getenv("JOB_NAME", None) == 'iOS-UI-automation':
#         with MysqlUtil(host='weee.db.tb1.sayweee.net', user=db_config['db_erp_username'],
#                        password=db_config['db_erp_password'], db='autotest_result') as conn:
#             for i in DB_EVERY_CASES_RESULT:
#                 # out_message = "null" if i['case_detail'][0]['out_message'] == '' or i['status'] == 3 else i['case_detail'][0][
#                 #     'out_message'].replace("'", "")
#                 try:
#                     if len(i['case_detail']) == 0:
#                         out_message = "null"
#                     elif i['case_detail'][0]['out_message'] == '':
#                         out_message = "null"
#                     else:
#                         out_message = i['case_detail'][0]['out_message'].replace("'", "")
#                         # error message太长且格式错乱，所以不存库
#                         error_msg = {i['case_detail'][0]['error_message']}
#
#                     sql = f""" insert into autotest_result.case_stat (allure_epic, allure_feature, allure_story, allure_severity,allure_title,
#                             allure_description, allure_tage, case_file_path, case_class_name, case_fuction_name, case_parameter,
#                             case_url, case_header, case_request, case_request_time, case_response_status, case_response, case_desc,
#                             case_status, case_type, case_dev_user, case_review_user, case_run_time, case_out_message,
#                             case_error_message, create_time, pytest_case_nodeid, pytest_step_name, build_num) values
#                             ('', '', '', '', '', '', '', '{i['case_name'].split('::')[0]}', '{i['case_name'].split('::')[1].replace("'", "")}', '{i['case_name'].split('::')[2].replace("'", "")}', '',
#                             '{i['case_url']}', '', '', null, '{i['case_response_status']}', '', '{i['desc'].replace("'", "")}', {i['status']}, '{i['case_type']}', '',
#                             '', {i['run_time']}, '{out_message}', '', null, '{i['case_node_id'].replace("'", "")}',
#                             'Call', {os.getenv("BUILD_NUMBER", 0)}) """
#                     print("sql===>", sql)
#                     asyncio.run(tb1_mysql_update(sql=sql, conn=conn))
#                 except Exception as e:
#                     log.info(f"此条记录存储数据库case_stat表失败,记录为：{json.dumps(i)} " + str(e))
#
#         with MysqlUtil(host='weee.db.tb1.sayweee.net', user=db_config['db_erp_username'],
#                        password=db_config['db_erp_password'], db='autotest_result') as connection:
#             try:
#                 jenkins_sql = f"""
#                            insert into autotest_result.jenkins_stat (build_name, build_number, build_user, build_email, build_version, build_env, build_branch, total_num, pass_num, failed_num, skiped_num, create_time, pass_rate, failed_rate, skip_rate,allure,project_name, false_alarm_num, real_alarm_num) values
#                            ('{weeeConfig.report_title}', {os.getenv("BUILD_NUMBER", 0)}, '{weeeConfig.report_tester}', '', '', '', '{upstream_branch}', '{total_num}', '{pass_num}', '{fail_num}', '{skip_num}', '{RunResult.start_time}', '{RunResult.pass_rate}', '{RunResult.failure_rate}', '{RunResult.skip_rate}', 'http://autotest.sayweee.net:9090/job/{os.getenv("JOB_NAME", None)}/{os.getenv("BUILD_NUMBER", 0)}/allure','iOS-UI-automation', 0, 0)
#                            """
#                 print("jenkins sql===>", jenkins_sql)
#                 asyncio.run(tb1_mysql_update(sql=jenkins_sql, conn=connection))
#             except Exception as e:
#                 log.info(f"此条记录存储数据库jenkins_stat表失败,sql为：{jenkins_sql} " + str(e))
#     else:
#         log.info("此次运行不是IOS-UI-automation工程或为本地运行")
#
#     # ============================= 将每个用例的信息保存至数据库，结束 ============================
#
#     # 测试报告测试
#     case_re = json.dumps(CASE_RESULT, ensure_ascii=False, indent=4)  # 用例结果转换成json格式
#     case_details = json.dumps(EVERY_CASES_RESULT, ensure_ascii=False, indent=4)  # 用例详情转换成json格式
#     # 测试报告处理
#     dev_case_details = json.dumps(RunResult.DEV_EVERY_CASES_RESULT, ensure_ascii=False, indent=4)  # 用例详情转换成json格式
#
#     report = Report(weeeConfig.report_path)  # 实例化Report类
#     # 开发报告日志
#     log.debug('weeeTest插件获取测试报告路径: {}'.format(weeeConfig.report_path))
#     log.debug('weeeTest插件执行结果汇总json数据: {}'.format(case_re))
#     log.debug('weeeTest插件执行结果详情json数据: {}'.format(case_details))
#     # 开发报告详情日志
#     log.debug('weeeTest插件执行结果详情dev的json数据: {}'.format(dev_case_details))
#
#     # 提示信息
#     if pytest_total_num != total_num:
#         log.info(
#             f'注意：weeeTest获取到用例数量为：{pytest_total_num}个, 实际执行的用例数量为：{total_num}个不一致, 请检查用例是否全部被执行。')
#
#     if error_num > 0:
#         log.info(f'注意：weeeTest执行错误用例数量为：{error_num}个, 请检查用例代码是否有错.')
#
#     if fail_num > 0:
#         log.error(f'注意：weeeTest执行断言用例数量为：{fail_num}个, 请检查断言是否有误.')
#
#     report.generate_report(case_re, case_details)  # 测试开发测试报告生成
#     report.generate_report_dev(case_re, dev_case_details)  # 开发测试报告生成
#
#     print("build number===>", os.getenv("BUILD_NUMBER", None))
#     print("build user===>", os.getenv("BUILD_USER"))
#     print("build job name===>", os.getenv("JOB_NAME"))
#     print("mark===>", os.getenv("mark"))
#     print("to===>", os.getenv("to"))
#     print("cc===>", os.getenv("cc"))
#     if os.getenv("BUILD_NUMBER", None):
#         # if Message.to is not None and len(Message.to) > 0:
#         if os.getenv("to", None) is not None and len(os.getenv("to", None)) > 0:
#             # report.send(send_type='email', to=Message.to)
#             try:
#                 print("======= mail is sending =======")
#                 report.send(send_type='email', to=os.getenv("to"), cc=os.getenv("cc"))
#             except Exception as e:
#                 print("send email with os.getenv failed " + str(e))
#         else:
#             try:
#                 print("======= mail is sending =======")
#                 report.send(send_type='email', to=Message.to, cc=Message.cc)
#             except Exception as e:
#                 print("send email with Message failed " + str(e))
#
#
# @pytest.fixture(scope="session", autouse=True)
# def user_setup():
#     RunResult.start_time = time.strftime("%Y-%m-%d %H:%M:%S")  # 测试开始时间
#     yield


# 添加自定义标签
def pytest_collection_modifyitems(session, config, items: list):
    for item in items:
        if "case" in item.nodeid:
            item.add_marker(pytest.mark.case)
        if "test" in item.nodeid:
            item.add_marker(pytest.mark.test)
