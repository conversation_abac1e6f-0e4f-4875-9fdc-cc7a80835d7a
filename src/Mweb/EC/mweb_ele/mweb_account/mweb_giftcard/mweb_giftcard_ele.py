"""
<AUTHOR>  Assistant
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_giftcard_ele.py
@Description    :  Mobile Gift Card页面元素定位
@CreateTime     :  2025/07/10
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/07/10
"""

# 礼品卡页面弹窗 Got it 按钮
# 礼品卡页面弹窗 Got it 按钮，使用xpath定位，按钮文案为"Got it"
ele_giftcard_got_it_button = u"//button[text()='Got it']"

# 礼品卡页面邮箱输入框
ele_giftcard_email_input = u"//input[@id='recipient_email' and @type='email']"

# 礼品卡页面结算按钮
ele_giftcard_place_order = u"//button[text()='Checkout']"

# 结算页面支付模块按钮
ele_checkout_payment_box = u"[data-testid='wid-payment-box']"

# 结算页面 PayPal 支付方式按钮
ele_checkout_paypal_payment = u"//*[@data-category='P']"

# 结算页面确认支付方式按钮
ele_checkout_pay_method_confirm = u"[data-testid='btn-pay-method-confirm']"

# 结算页面支付按钮
ele_checkout_place_order = u"[data-testid='btn-place-order']"