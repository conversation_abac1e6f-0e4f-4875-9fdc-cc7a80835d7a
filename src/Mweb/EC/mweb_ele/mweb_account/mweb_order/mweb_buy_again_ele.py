
"""
<AUTHOR>  suqin
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_buy_again_ele.py
@Description    :  再来一单页面元素定义
@CreateTime     :  2025/6/8 11:31
@Software       :  PyCharm
------------------------------------

"""
# 再来一单页面
buy_again_page_close = "btn-drawer-close"
# 切换日期入口
# H5
buy_again_change_date = "wid-order-buy-again-change-date"
buy_again_change_date_label ="wid-order-buy-again-change-date-label"
buy_again_change_date_value ="wid-order-buy-again-change-date-value"

# PC
buy_again_page_info = "wid-account-buy-again-date-info"
# 再来一单弹窗
buy_again_popup = "wid-popup-order-buy-again"
buy_again_page = "wid-order-buy-again-content"
# 有效商品区域
buy_again_available = "wid-order-buy-again-content-available"
# 无效商品区域
buy_again_unavailable_area = "wid-order-buy-again-content-inavailable"
# 再来一单页面-商品列表
buy_again_available_product = "wid-order-buy-again-product-item"
buy_again_available_item_content = "wid-order-buy-again-product-item-content"

buy_again_unavailable = "wid-order-buy-again-content-inavailable"
buy_again_item_checkbox = "wid-order-buy-again-product-item-checkbox"
# 再来一单页面-加入购物车按钮
# H5
buy_again_add_cart_button = "wid-order-buy-again-submit"
buy_again_add_cart_button_count = "wid-order-buy-again-submit-count"
# pc
buy_again_add_cart_btn= "btn-account-buy-again-add-cart"
# 再来一单页面-全选按钮
# H5
buy_again_chose_all = "wid-order-buy-again-chose-all"
# pc
buy_again_select_all = "wid-account-buy-again-select-all"



# 商品内容元素
buy_again_product_item_content_title = "wid-order-buy-again-product-item-content-title"
buy_again_product_item_content_image = "wid-order-buy-again-product-item-content-image"
buy_again_product_item_content_price = "wid-order-buy-again-product-item-content-price"

# 再来一单页面ID
buy_again_page_id = "order-buy-again"
