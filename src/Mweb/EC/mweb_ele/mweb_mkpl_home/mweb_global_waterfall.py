# Marketplace banner array item 3
ele_mkpl_banner_arrays_1 = u"//a[@data-testid='wid-collection-cm_banner_array-1']"
ele_mkpl_banner_arrays_3 = u"//a[@data-testid='wid-collection-cm_banner_array-3']"

# 全球购介绍弹窗
ele_global_intro_popup = u"//div[contains(@class, 'global_intro-content')]"
# 全球购介绍弹窗标题
ele_global_intro_title = ele_global_intro_popup + u"//div[contains(@class, 'enki-body-xl-strong')]"
# 全球购介绍弹窗副标题
ele_global_intro_subtitle = ele_global_intro_popup + u"//div[contains(@class, 'enki-heading-xl')]"
# 全球购介绍弹窗描述
#ele_global_intro_description = ele_global_intro_popup + u"//div[contains(@class, 'enki-body-base-medium') and contains(text(), '在全球购上')]"
# 探索首单优惠按钮
ele_global_intro_explore_button = ele_global_intro_popup + u"//div[contains(@class, 'bg-[#F37519]')]"
# 暂时跳过按钮
#ele_global_intro_skip_button = ele_global_intro_popup + u"//div[contains(@class, 'enki-body-base-medium') and text()='暂时跳过']"
# 关闭按钮
ele_global_intro_close_button = ele_global_intro_popup + u"//i[contains(@class, 'iconTimes')]"

# 优惠券列表元素
ele_coupon_list_section = u"//section[@data-testid='wid-home-collection-cm_coupon_list']"
# 优惠券列表项目
ele_coupon_list_item = ele_coupon_list_section + u"//div[contains(@class, 'swiper-slide')]"
# 优惠券标题
ele_coupon_list_title = ele_coupon_list_section + u"//div[contains(@class, 'text-surface-100-fg-default')]"
# 查看全部按钮
ele_coupon_list_view_all = ele_coupon_list_section + u"//div[contains(@class, 'text-primary-100-fg-default')]"
# 优惠券图标
ele_coupon_list_icon = ele_coupon_list_section + u"//div[contains(@class, 'w-11 h-11 rounded-700 mr-2.5 overflow-hidden flex-none')]"