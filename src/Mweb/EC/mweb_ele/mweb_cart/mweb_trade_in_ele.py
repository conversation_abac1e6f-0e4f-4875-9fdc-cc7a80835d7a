"""
换购模块相关元素定位
"""
# PDP页面底部提示
ele_pdp_unlock_tip = u"//div[@data-testid='pdp-unlock-tip']"
# 换购进度条
ele_trade_in_progress = u"//div[@data-testid='trade-in-progress']"
# 换购商品已选状态
ele_trade_in_selected = u"//button[@data-testid='btn-selected']"
# 购物车换购商品标签
ele_cart_trade_in_tag = u"//div[@data-testid='tag-deal']"
# 换购商品删除按钮
ele_cart_trade_in_delete = u"//button[@data-testid='btn-delete']"
# 换购商品数量限制弹窗
ele_trade_in_limit_popup = u"//div[@data-testid='popup-limit']"

# 换购页面模块
ele_trade_in = u"//div[@id='trade_in']"
# 换购页面banner模块
ele_trade_in_banner = ele_trade_in + u"//div[@id='disaccountWrap']"
# 换购页面商品卡片
ele_trade_in_card = ele_trade_in + u"//a[@data-testid='wid-product-card-container']"
# 换购页面商品卡片标题
ele_trade_in_card_title = ele_trade_in_card + u"//div[@data-testid='wid-product-card-title']"
# 换购页面商品卡片价格
ele_trade_in_card_price = ele_trade_in_card + u"//div[@data-testid='wid-product-card-price']"
# 换购页面商品卡片加购按钮
ele_trade_in_card_add = ele_trade_in_card + u"//div[contains(@class, 'product-card-btn')]"
# 换购页面商品卡片加购按钮禁用状态
ele_trade_in_card_add_disabled = ele_trade_in_card + u"//div[contains(@class,'product-card-btn-shadow')]"

# 换购页shop more 按钮
ele_trade_in_shop_more = ele_trade_in + u"//div[@class='relative']//button"
# 换购页面按钮
ele_trade_in_button = ele_trade_in + u"//button"

# 凑单页面模块 -- 元素需要补充datatestid

# 换购页面元素
ele_trade_in_select_btn = ele_trade_in_card + u"//button[@data-testid='btn-select']"
ele_trade_in_select_btn_disabled = ele_trade_in_select_btn + u"[@disabled]"
ele_trade_in_remaining_amount = u"//div[@data-testid='remaining-amount-text']"

"""换购页面元素定位"""
# 换购页面模块
ele_trade_in = u"//div[@id='trade_in']"
# 换购页面banner模块
# 换购页面商品卡片
ele_trade_in_card = ele_trade_in + u"//a[@data-testid='wid-product-card-container']"
# 换购页面商品卡片
# 换购页面商品卡片
# 换购页shop more 按钮

# 换购页面select按钮
ele_select_btn = "//button[contains(@class, 'trade-in-select')]"
# 添加更多提示
ele_add_more_tip = "//div[contains(text(), 'Add ${} more to cart')]"
# 解锁折扣提示
ele_unlock_discount_pop = "//div[contains(text(), 'Unlock your special discount')]"
# 商品详情页解锁提示
# 进度条
ele_progress_bar = "//div[contains(@class, 'progress-bar')]"
# 选择限制提示
ele_limit_pop = "//div[contains(text(), 'selection is limited to 5 items')]"
# 返回购物车按钮
ele_go_to_cart = "//button[contains(@class, 'go-to-cart')]"