GITele_deals = u"//span[normalize-space(text())='Deals']"
ele_h5_home_zipcode = u"#layout-header i[class*='iconlocation-filled']"
ele_please_input_zipcode = u"input[placeholder]"
ele_add_new_address_div = u"//button[text()='Add new address']"
ele_input_address = u"input[placeholder='Street Address']"
ele_1st_matched_address = u"#streetAddressList span"

ele_fullname = u"input[placeholder='First Name']"
ele_family_name = u"input[placeholder='Last Name']"
ele_phone_number = u"input[placeholder='Phone Number']"
ele_note = u"textarea[placeholder]"
ele_save_address = u"button[type='submit']"

ele_hello_world_address = "//strong[text()='hello world']/../..//i[contains(@class, 'text-surface')]"
ele_delete_address_button = "i[class*='iconDelete']"
ele_confirm_delete_button = "//button[text()='Remove']"

# each collection on home page
ele_h5_home_editors_pick = u"""//h2[text()="Editor's Pick"]"""
ele_h5_home_editors_pick_add_to_cart = u"""//h2[text()="Editor's Pick"]/../..//i[@data-role='addButtonPlusIcon']"""
ele_h5_home_everyday_deals = u"//h2[text()='Everyday deals']"
ele_h5_home_everyday_deals_add_to_cart = ele_h5_home_everyday_deals + u"/../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_fresh_daily = u"//div[text()='Fresh Daily']"
ele_h5_home_fresh_daily_add_to_cart = ele_h5_home_fresh_daily + u"/../../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_best_sellers = u"//h2[text()='Bestsellers']"
ele_h5_home_best_sellers_add_to_cart = ele_h5_home_best_sellers + u"/../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_recommendations = u"//h2[text()='Recommendations']"
ele_h5_home_recommendations_add_to_cart = ele_h5_home_recommendations + u"/../..//i[@data-role='addButtonPlusIcon']"
ele_h5_home_navigation_global = "//div[@id='navigationGlobal']"

# edlp标签展示元素定位
ele_h5_everyday_value = "//span[text()='Everyday value']"






