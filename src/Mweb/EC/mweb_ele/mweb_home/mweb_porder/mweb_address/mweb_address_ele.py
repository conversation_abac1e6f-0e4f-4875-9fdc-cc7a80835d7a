# 1 销售组织地址
address_1 = "1251 Peralta Blvd, Fremont, CA 94536"
# 2 销售组织地址
address_2 = "1020 E Florence Ave, Los Angeles, CA 90001"
# 3 销售组织地址
address_3 = "3004 Andrade Rd, Sunol, CA 94586"
# 4 销售组织地址
address_4 = "18607 Bothell Way NE, Bothell, WA 98011"
# 24 销售组织地址
address_24 = "20 Huntington Rd, Blandford, MA 01008"

# 地址卡片
address_card = "wid-address-card"
address_card_location_icon = "wid-address-card-location-icon"
address_card_name = "wid-address-card-name"
address_card_address = "wid-address-card-address"
address_card_city = "wid-address-card-city"
address_card_edit = "wid-address-card-edit"

# Deliver to pop
# 添加地址按钮
# h5 暂时用这个，等开发修改了替换下面这个
delivery_to_pop = "wid-popup-homepage-change-porder"
deliver_to_pop_add_btn = "btn-add-address"
# deliver_to_pop_add_btn = "wid-btn-add-address"
# zipcode 弹窗x按钮
deliver_to_pop_close_btn  = "btn-close-delivery-info-dialog"
# 新增地址按钮
ele_add_new_address_button = u"//button[text()='Add new address']"
# ########################################################################
# delivery address pop
delivery_address_input = "wid-address-input-drawer-input"
# 第一个联想的地址
address_first_matched = "wid-address-input-drawer-street-address-item"
# 查看更多按钮
zipcode_pop_more_btn = "wid-btn-more-address"

# ########################################################################
# # Delivery Info 添加地址页面pop
# 输入姓名
address_first_name = "wid-input-first-name"
address_last_name = "wid-input-last-name"
# 输入电话
address_phone = "wid-input-phone"
# 输入街道地址
address_street = "wid-input-street"
# 输入公寓号
address_apt = "wid-input-flats"
# 输入城市
address_city = "wid-input-city"
# 输入州
address_state = "text-filed-input"
# 输入邮编
address_zipcode = "wid-input-zipcode"
# 输入备注
address_note = "wid-input-notes"
# 保存地址
address_save_button = "btn-save-address"
# 取消保存
address_cancel_button = u"button[type='button']"
# 删除按钮
address_delete_button = "btn-delete-address"

# 首页相关元素
home_zipcode_modal = "wid-modal-zip-code-and-eta"
home_collection_categories = "wid-home-collection-cm_categories"
home_main_banner = "wid-main-banner-card-0"

# Account相关元素
profile_icon = "wid-profile"
my_orders_link = "wid-my_orders"
settings_link = "wid-settings"
address_book_link = "address book"

# 地址管理相关元素
address_item = "wid-address-item"
btn_edit_address = "btn-edit-address"
btn_remove_address = "btn-remove-address"
remove_address_popup = "wid-popup-removeAddress"
btn_cancel_apply_address = "btn-cancel-apply-address"

# 购物车相关元素
cart_start_shopping = "btn-cart-start-shopping"
cart_recommendations = "mod-cart-Recommendations"
btn_checkout = "btn-checkout"
cart_popup = "wid-popup-cart1"
btn_select_all_carts = "btn-select-all-carts"
btn_select_cart_checkout = "btn-select-cart-checkout"
checkout_upsell_popup = "wid-popup-checkoutupsell"
btn_continue = "btn-continue"

# 结算页相关元素
checkout_delivery_info = "mod-checkout-delivery-info"
checkout_delivery_info_content = "mod-checkout-delivery-info-content"

# 弹窗相关元素
generic_popup = "pop"
confirm_modal = "wid-modal-confirm"
btn_confirm_ok = "btn-confirm-ok"
btn_confirm_close = "btn-confirm-close"

# 首页zipcode相关元素
home_zipcode_button = "wid-modal-zip-code-and-eta"
edit_address_button = "btn-edit-address"
add_address_button = "wid-btn-add-address"
close_delivery_dialog = "btn-close-delivery-info-dialog"

# 广告关闭按钮
close_advertisement = "//img[contains(@aria-label, 'close button')]"

# 地址卡片编辑按钮
address_card_edit_icon = "//div//i[contains(@class,'icon iconfont iconedit-new')]"

