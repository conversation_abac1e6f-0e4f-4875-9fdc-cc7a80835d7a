# !/usr/bin/python3
# -*- coding: utf-8 -*-

"""
<AUTHOR>  xia<PERSON> mi<PERSON>
@Version        :  V1.0.0
------------------------------------
@File           :  mweb_lightningdeal_ele.py
@Description    :  移动端秒杀页面元素定义
@CreateTime     :  2025/7/14 03:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/7/14 03:13
"""

# 秒杀页面分享按钮
ele_share = "//i[@data-testid='btn-share']"

# 分享弹窗
ele_share_pop = "//div[@data-type='popup' and @data-popup-visible='true' and @id='share-popup']"

# 分享弹窗标题
ele_share_pop_title = ele_share_pop + "//header[@id='share-popup-header']/h2"

# 分享弹窗关闭按钮
ele_share_pop_close = ele_share_pop + "//div[@data-testid='btn-modal-close']"

# 分享弹窗页面图片
ele_share_pop_page_img = ele_share_pop + "//div[@data-component='CroppedImage']"

# 分享弹窗语言选择
ele_share_pop_language = ele_share_pop + "//li"

# 复制链接按钮
ele_share_pop_copy_link = ele_share_pop + "//div[@data-method='copyLink']"

# 保存图片按钮
ele_share_pop_save_image = ele_share_pop + "//div[@data-method='saveImage']"

# 分享图片弹窗
ele_share_img_pop = "//div[@data-type='popup' and @data-popup-visible='true' and @id='share-image']"
ele_share_img_pop_title = ele_share_img_pop + "//div[contains(@class,'shareImage_title')]"

# 秒杀页面主要元素
ele_lightning_deal_container = "//div[@data-testid='lightning-deal-container']"
ele_lightning_deal_title = "//h1[contains(text(), 'Lightning Deals')]"
ele_lightning_deal_timer = "//div[@data-testid='countdown-timer']"
ele_lightning_deal_products = "//div[@data-testid='lightning-deal-product']"

# 秒杀商品卡片元素
ele_product_card = "//div[@data-testid='product-card']"
ele_product_title = "//div[@data-testid='product-title']"
ele_product_price = "//div[@data-testid='product-price']"
ele_product_discount = "//div[@data-testid='product-discount']"
ele_product_add_to_cart = "//button[@data-testid='btn-add-to-cart']"
