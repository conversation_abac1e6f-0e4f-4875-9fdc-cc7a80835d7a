"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_category_ele.py
@Description    :  移动端分类页面元素
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""

# 筛选类别
ele_filter_category_delivery = "btn-filter-category-delivery_type"
ele_filter_category_delivery_xpath = u"//div[@data-testid='btn-filter-category-delivery_type']"
ele_filter_category_price = "btn-filter-category-price"
ele_filter_category_price_xpath = u"//div[@data-testid='btn-filter-category-price']"
ele_filter_category_brand = "btn-filter-category-brand"
ele_filter_category_brand_xpath = u"//div[@data-testid='btn-filter-category-brand']"

# 筛选按钮
ele_filter_button = 'btn-sub-category-filter'
ele_filter_apply = 'btn-sort-filter-apply'
ele_filter_reset = u"//div[@data-testid='btn-sort-filter-reset']"

# 配送类型筛选 (使用test_id)
ele_local_delivery = "btn-filter-delivery_type-delivery_type_local"
ele_global_delivery = "btn-filter-delivery_type-delivery_type_global"
ele_pantry_delivery = "btn-filter-delivery_type-delivery_type_pantry"
ele_mof_delivery = 'btn-filter-delivery_type-delivery_type_local_mof'
ele_mo_delivery= 'btn-filter-delivery_type-delivery_type_fbw'
# 配送类型筛选 XPath
ele_local_delivery_xpath = u"//div[@data-testid='btn-filter-delivery_type-delivery_type_local']/div[2]"
ele_global_delivery_xpath = u"//div[@data-testid='btn-filter-delivery_type-delivery_type_global']/div[2]"
ele_pantry_delivery_xpath = u"//div[@data-testid='btn-filter-delivery_type-delivery_type_pantry']/div[2]"
ele_mof_delivery_xpath = u"//div[@data-testid='btn-filter-delivery_type-delivery_type_local_mof']/div[2]"
ele_mo_delivery_xpath=u"//div[@data-testid='btn-filter-delivery_type-delivery_type_fbw']/div[2]"
# 商品加购按钮
ele_add_to_cart_button = "btn-atc-plus"
ele_add_to_cart_xpath = u"//div[@data-testid='btn-atc-plus']"

# 商品列表
ele_product_list = "mod-category-product-list"
ele_product_card = "wid-product-card-container"
