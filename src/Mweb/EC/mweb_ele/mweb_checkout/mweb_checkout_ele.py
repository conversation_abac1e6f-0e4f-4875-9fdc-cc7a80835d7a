"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_checkout_ele.py
@Description    :  结算页面元素定位
@CreateTime     :  2025/5/16 15:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/16 15:30
"""

# 页面标题
ele_checkout_title = 'wid-header-title'
# 返回按钮
ele_back_button = 'btn-back'

# 页面头部
# ele_header = u"//div[@id='layout-header']"
# ele_header_title = u"//div[@data-role='middle']//div[contains(@class, 'text-primary-1')]"
# 顶部会员等级模块
ele_rewards_header = 'wid-checkout-rewards-header'

# 配送信息模块
# 配送地址模块
ele_delivery_info_content = 'mod-checkout-delivery-info-content'
ele_delivery_info_section = 'mod-checkout-delivery-info'
ele_delivery_info_title = 'mod-checkout-delivery-info-title'
ele_delivery_address = 'wid-checkout-delivery-info-address'
ele_delivery_address_name = 'wid-checkout-delivery-info-name'
ele_delivery_address_phone = 'wid-checkout-delivery-info-phone'
ele_delivery_address_comment = 'wid-checkout-delivery-info-comment'


# ---------------------------------支付方式模块--------------------------------------------
ele_payment_method_section = 'mod-checkout-payment'
ele_payment_method_title = 'mod-checkout-payment-title'
ele_payment_method_card_type = 'wid-checkout-payment-method-card-type'
ele_payment_card_number = 'wid-checkout-payment-method-card-number'
ele_payment_method_point='wid-checkout-payment-points-content'
ele_checkout_points_title = 'wid-checkout-payment-points-title'
ele_checkout_points_content = 'wid-checkout-payment-points-content'
# 跟ebt使用时候积分下面的文案
ele_checkout_points_desc ='wid-checkout-payment-points-desc'
# 支付方式下面的描述
ele_checkout_payment_method_item_desc = 'wid-checkout-payment-method-item-desc'
# -----------cvc----------------
ele_checkout_cvc_input = 'wid-checkout-payment-method-cvc-input'
ele_checkout_cvc_confirm_cvc  ='wid-checkout-payment-method-confirm-cvc'
ele_checkout_cvc_error = 'wid-checkout-payment-method-cvc-error'



# ------------------------------review order--------------------------------------------
ele_review_order = 'mod-checkout-review-order'
ele_review_order_title = 'mod-checkout-review-order-title'
# 小汽车icon
ele_review_order_shipping_icom = 'wid-review-order-card-normal-shipping-icon'
# 生鲜购物车的标题
ele_review_order_shipping_type_desc = 'wid-review-order-card-normal-shipping-type-desc'
# 生鲜购物车的配送描述
ele_review_order_shipping_desc = 'wid-review-order-card-normal-shipping-desc'
# 生鲜购物车的配送时间
ele_review_order_shipping_date= 'wid-review-order-card-normal-shipping-date'
# 生鲜购物车商品数量
ele_review_order_shipping_total = 'wid-review-order-card-normal-item-total'
# 生鲜购物车商品总金额
ele_review_order_shipping_total_price = 'wid-review-order-card-normal-item-total-price'
# 生鲜购物车的配送费
ele_wid_review_order_card_normal_shipping_fee ='wid-review-order-card-normal-shipping-fee'
# 商品图片
ele_review_order_product_image = 'wid-review-order-card-product-image'
ele_review_order_card_summary = 'wid-review-order-card-summary'
# 卡片右箭头
ele_rebiew_order_arrow_right = 'wid-review-order-card-product-arrow-right'
# 商品图片下面的配送文案
ele_review_order_delivery_window_content = 'wid-review-order-card-normal-delivery-window-content'



# ----------------------------------------优惠券模块------------------------------------------
ele_coupon_section = 'mod-checkout-coupon'
ele_coupon_title = 'mod-checkout-coupon-title'
ele_coupon_select_box = 'wid-checkout-coupon-wrap'
ele_coupon_header_back = 'btn-drawer-header-back'
# ele_coupon_value = u"//div[@id='coupon']//div[contains(@class, 'enki-body-sm-strong')]"

# ----------------------------------------司机小费--------------------------------------------
ele_checkout_tip = 'mod-checkout-delivery-tip'
ele_checkout_tip_title = 'mod-checkout-delivery-tip-title'
ele_checkout_tip_text  ='wid-checkout-delivery-tip-text'
ele_checkout_tip_2 = 'wid-checkout-delivery-tip-2'
ele_checkout_tip_3 = 'wid-checkout-delivery-tip-3'
ele_checkout_tip_4 = 'wid-checkout-delivery-tip-4'
ele_checkout_tip_0 = 'wid-checkout-delivery-tip-0'

# ----------------------------------------订单摘要模块--------------------------------------------
ele_checkout_summary_section = 'mod-checkout-order-summary'
ele_checkout_summary_title = 'wid-order-summary-title'
#loytal 感谢文案
ele_checkout_loytal_vip_content = 'wid-order-summary-loyalty-vip-thanks-content'
ele_checkout_loytal_vip_icon = 'wid-order-summary-loyalty-vip-icon'
ele_checkout_subtotal_title = 'wid-order-summary-item-subtotal-title'
ele_checkout_subtotal_amount = 'wid-order-summary-item-subtotal-amount'
# tax
ele_checkout_summary_taxes_title = 'wid-order-summary-item-taxes-title'
ele_checkout_summary_taxes_amount = 'wid-order-summary-item-taxes-amount'


# service fee的i标签
ele_order_service_fee_title='wid-order-summary-item-service_fee-title'
ele_order_service_fee_icon = 'wid-order-summary-item-fee-icon'
ele_order_service_fee_amount = 'wid-order-summary-item-service_fee-amount'
ele_order_service_fee_base_amount = 'wid-order-summary-item-service_fee-base-amount'

# delivery fee
ele_order_delivery_fee_title='wid-order-summary-item-delivery_fee-title'
ele_order_delivery_fee_icon = 'wid-order-summary-item-fee-icon'
ele_order_delivery_fee_amount = 'wid-order-summary-item-delivery_fee-amount'
ele_order_delivery_fee_base_amount = 'wid-order-summary-item-delivery_fee-base-amount'
ele_btn_modal_close = 'btn-modal-close'

# tip
ele_order_summary_tip_title  = 'wid-order-summary-item-tip-title'
ele_order_summary_tip_amount  = 'wid-order-summary-item-tip-amount'

# total
ele_order_summary_total_title = 'wid-order-summary-item-total-title'
ele_order_summary_total_amount = 'wid-order-summary-item-total-amount'

# 积分使用模块
ele_order_summary_point_deduction_title = 'wid-order-summary-item-weee!_points_deduction-title'
ele_order_summary_point_deduction_amount = 'wid-order-summary-item-weee!_points_deduction-amount'

# 结算按钮
ele_place_order_button = 'wid-checkout-btn'

# 底部冷冻商品报错提示信息
ele_checkout_warning_text = 'wid-checkout-delivery-tip-warning-text'

# 结算页底部总计按钮部分
ele_checkout_total_amount_warp = 'wid-checkout-total-amount-wrap'

# ------------------------------------------------------------------------------------------------------
# 错误提示
ele_error_message = u"//div[contains(@class, 'text-error-500')]"

# 加载状态
ele_loading_indicator = u"//div[contains(@class, 'loading')]"

# 支付方式选择
ele_payment_method_selector = 'payment-method-selector'
ele_credit_card_option = 'payment-method-credit-card'
ele_paypal_option = 'payment-method-paypal'

# 配送时间选择
ele_delivery_time_section ='delivery-time-selector'
ele_delivery_time_option = 'delivery-time-option'

# --------------------------点击结算页商品进入订单商品列表---------------------------------------
ele_checkout_product_title = 'wid-checkout-product-drawer-title-text'
ele_checkout_product_clos_icon = 'wid-checkout-product-drawer-close-icon'
ele_checkout_product_item_image = 'wid-checkout-product-drawer-product-item-image'
ele_checkout_product_item_title = 'wid-checkout-product-drawer-product-item-title'
ele_checkout_product_item_price = 'wid-checkout-product-drawer-product-item-price'
ele_checkout_product_item_quantity = 'wid-checkout-product-drawer-product-item-quantity'
# 订单列表商品卡片
ele_checkout_product_drawer_content = 'wid-checkout-product-drawer-content'

# 提示横幅
ele_promo_banner = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]"
ele_promo_banner_text = u"//div[contains(@style, 'background: rgb(253, 255, 164)')]//span"
