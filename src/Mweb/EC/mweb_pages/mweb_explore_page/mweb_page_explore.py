from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebPageExplore(PageH5CommonOperations):

    def __init__(self, page: Page, header, bc):
        super().__init__(page, header)
        self.bc = bc

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL + "/category/sale" + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()


    def search_product_by_sort(self, sort_name: str):
        # 点击filter
        self.page.get_by_test_id("btn-sub-category-filter").click()
        # 单选框不存在text方法，使用not text()方法来定位
        self.page.get_by_test_id(sort_name).locator("//div[not(text())]").click()
        self.page.get_by_test_id("btn-sort-filter-apply").click()
        self.page.wait_for_timeout(5000)

    def search_product_by_filter(self, filter_name: str):
        # 点击filter
        self.page.get_by_test_id("btn-sub-category-filter").click()
        self.page.wait_for_timeout(2000)
        self.page.get_by_test_id(filter_name).locator("//div[contains(@class,'rounded-200')]").click()
        self.page.get_by_test_id("btn-sort-filter-apply").click()
        self.page.wait_for_timeout(5000)

    def search_product_by_product_type(self, product_type: str):
        # 点击filter
        self.page.get_by_test_id("btn-sub-category-filter").click()
        self.page.get_by_test_id(product_type).locator("//div[not(text())]").click()
        self.page.get_by_test_id("btn-sort-filter-apply").click()
        self.page.wait_for_timeout(5000)
