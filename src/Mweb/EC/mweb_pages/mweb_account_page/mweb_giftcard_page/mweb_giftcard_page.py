from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_ele.mweb_account.mweb_giftcard.mweb_giftcard_ele import *
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class MWebGiftCardPage(MWebCommonPage):
    """礼品卡页面操作类 - MWeb版本"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "en/product/gift-card/2189607"):
        """
        初始化礼品卡页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: 礼品卡页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入礼品卡页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入礼品卡页面: {TEST_URL}/{page_url}")

    def close_gift_card_popup(self):
        """
        关闭礼品卡弹窗 - 点击 Got it 按钮
        """
        try:
            self.FE.ele(ele_giftcard_got_it_button).click()
            log.info("成功点击 Got it 按钮，关闭礼品卡弹窗")
        except TimeoutError:
            log.warning("未找到 Got it 按钮，可能弹窗已关闭或不存在")

    def input_recipient_email(self, email: str = "<EMAIL>"):
        """
        在邮箱输入框中输入收件人邮箱
        :param email: 收件人邮箱地址，默认为 <EMAIL>
        """
        self.FE.ele(ele_giftcard_email_input).fill(email)
        log.info(f"成功输入收件人邮箱: {email}")

    def click_place_order(self):
        """
        点击礼品卡页面下方的结算按钮
        """
        self.FE.ele(ele_giftcard_place_order).click()
        log.info("成功点击礼品卡结算按钮")

    def select_payment_method(self):
        """
        在结算页面点击选择支付模块按钮
        """
        self.FE.ele(ele_checkout_payment_box).click()
        log.info("成功点击支付模块按钮")

    def select_paypal_payment(self):
        """
        选择 PayPal 支付方式
        """
        self.FE.ele(ele_checkout_paypal_payment).click()
        log.info("成功选择 PayPal 支付方式")

    def confirm_payment_method(self):
        """
        点击确认支付方式按钮
        """
        self.FE.ele(ele_checkout_pay_method_confirm).click()
        log.info("成功点击确认支付方式按钮")

    def click_final_payment(self):
        """
        在结算页面点击最终支付按钮
        """
        self.FE.ele(ele_checkout_place_order).click()
        log.info("成功点击最终支付按钮")

    