"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_category.py
@Description    :  移动端分类页面类
@CreateTime     :  2025/4/25 10:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/25 10:30
"""

import allure
from playwright.sync_api import Locator

from src.Mweb.EC.conftest import h5_autotest_header
from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_category import mweb_category_ele
from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_pages.mweb_page_home.mweb_page_home import MWebPageHome
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.common.commonui import scroll_one_page_until


class MWebCategorypage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url=None):
        super().__init__(page, header)
        self.bc = browser_context
        # 进入首页
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)


    def add_products_from_home_by_filter(self, filter_name, filter_id, count=1):
        """
        购物车样式调用该方法
        通过筛选条件添加商品到购物车
        
        Args:
            filter_name: 筛选条件名称
            filter_id: 筛选条件的test_id
            count: 要添加的商品数量
        
        Returns:
            int: 成功添加的商品数量
        """
        try:
            # 1. 点击筛选按钮
            filter_button = self.page.get_by_test_id(mweb_category_ele.ele_filter_button)
            if not filter_button.is_visible(timeout=2000):
                log.error("筛选按钮不可见")
                return 0
                
            filter_button.click()
            self.page.wait_for_timeout(1000)
            log.info("成功点击筛选按钮")

            # 2. 选择筛选条件
            filter_element = self.page.get_by_test_id(filter_id)
            if not filter_element.is_visible(timeout=2000):
                log.error(f"{filter_name}筛选条件不可见")
                return 0
                
            filter_element.click()
            self.page.wait_for_timeout(1000)
            log.info(f"成功选择{filter_name}筛选条件")
            
            # 3. 点击应用按钮
            apply_button = self.page.get_by_test_id(mweb_category_ele.ele_filter_apply)
            if not apply_button.is_visible(timeout=2000):
                log.error("应用按钮不可见")
                return 0
                
            apply_button.click()
            self.page.wait_for_timeout(2000)
            log.info("成功点击应用按钮")

            # 4. 添加商品到购物车
            add_buttons = self.page.get_by_test_id(mweb_category_ele.ele_add_to_cart_button).all()
            if not add_buttons:
                log.error("未找到加购按钮")
                return 0
                
            log.info(f"找到 {len(add_buttons)} 个加购按钮")
            
            # 5. 点击加购按钮
            added_count = 0
            for i in range(min(len(add_buttons), count)):
                try:
                    if add_buttons[i].is_visible() and add_buttons[i].is_enabled():
                        add_buttons[i].click()
                        self.page.wait_for_timeout(1000)
                        added_count += 1
                        log.info(f"成功加购第 {added_count} 个商品")
                except Exception as e:
                    log.warning(f"加购第 {i+1} 个商品失败: {str(e)}")
            
            log.info(f"总共成功加购 {added_count} 个商品")
            return added_count
            
        except Exception as e:
            log.error(f"使用{filter_name}筛选添加商品失败: {str(e)}")
            return 0

    def reset_filter(self):
        """
        重置筛选条件
        """
        with allure.step("重置筛选条件"):
            try:
                log.info("开始重置筛选条件")
                
                # 1. 检查筛选面板是否已打开
                apply_button = self.page.get_by_test_id(mweb_category_ele.ele_filter_apply)
                filter_panel_visible = apply_button.is_visible(timeout=1000)
                
                # 2. 如果筛选面板未打开，则打开它
                if not filter_panel_visible:
                    filter_button = self.page.get_by_test_id(mweb_category_ele.ele_filter_button)
                    if filter_button.is_visible(timeout=2000):
                        filter_button.click()
                        self.page.wait_for_timeout(1000)
                        log.info("成功打开筛选面板")
                    else:
                        log.warning("找不到筛选按钮，尝试刷新页面")
                        self.page.reload()
                        self.page.wait_for_timeout(2000)
                        return
                
                # 3. 点击重置按钮
                reset_button = self.page.get_by_test_id("btn-sort-filter-reset")
                if reset_button.is_visible(timeout=2000):
                    reset_button.click()
                    self.page.wait_for_timeout(1000)
                    log.info("成功点击重置按钮")
                else:
                    log.warning("找不到重置按钮")
                
                # 4. 点击应用按钮
                apply_button = self.page.get_by_test_id(mweb_category_ele.ele_filter_apply)
                if apply_button.is_visible(timeout=2000):
                    apply_button.click()
                    self.page.wait_for_timeout(2000)
                    log.info("成功点击应用按钮")
                else:
                    log.warning("找不到应用按钮，尝试刷新页面")
                    self.page.reload()
                    self.page.wait_for_timeout(2000)
                
                log.info("筛选条件重置完成")
                return True
                
            except Exception as e:
                log.error(f"重置筛选条件失败: {str(e)}")

                


    def add_products_with_filter(self, filter_type, filter_selector, count=1):
        """
        验证中间页的case调用这个方法加购
        应用筛选条件并加购指定数量的商品
        现在不用这个方法了，但是怕别人调用，先放着-- 25.7.22zhuli

        Args:
            filter_type: 筛选类型名称（如"Local Delivery"、"Pantry"等）
            filter_selector: 筛选条件的选择器（XPath或test-id）
            count: 要加购的商品数量

        Returns:
            int: 成功加购的商品数量
        """
        with allure.step(f"应用{filter_type}筛选条件并加购{count}个商品"):
            try:
                log.info(f"开始应用{filter_type}筛选条件并加购商品")

                # 等待页面加载
                self.page.wait_for_timeout(2000)

                # 点击筛选按钮
                filter_button_found = False
                filter_button_selectors = [
                    "btn-sub-category-filter"
                ]

                for selector in filter_button_selectors:
                    try:
                        filter_button = self.page.get_by_test_id(selector)
                        if filter_button.is_visible(timeout=2000):
                            filter_button.click()
                            self.page.wait_for_timeout(2000)
                            log.info(f"成功点击筛选按钮: {selector}")
                            filter_button_found = True
                            break
                    except Exception as e:
                        log.debug(f"使用test-id {selector} 查找筛选按钮失败: {str(e)}")
                if not filter_button_found:
                    log.warning("未找到筛选按钮，尝试直接查找筛选条件")
                # 点击应用按钮
                try:
                    apply_selectors = [
                        "btn-sort-filter-apply"
                    ]

                    apply_button_found = False
                    for selector in apply_selectors:
                        try:
                            if selector.startswith("//"):
                                apply_button = self.page.locator(selector)
                            else:
                                apply_button = self.page.get_by_test_id(selector)

                            if apply_button.is_visible(timeout=2000):
                                apply_button.click()
                                self.page.wait_for_timeout(3000)
                                log.info(f"成功点击应用按钮: {selector}")
                                apply_button_found = True
                                break
                        except Exception as e:
                            log.debug(f"使用选择器 {selector} 查找应用按钮失败: {str(e)}")

                    if not apply_button_found:
                        log.warning("未找到应用按钮，尝试继续执行...")
                except Exception as e:
                    log.warning(f"点击应用按钮失败: {str(e)}")

                # 等待筛选结果加载
                self.page.wait_for_timeout(3000)

                # 查找并加购商品
                added_count = self._add_products_to_cart(filter_type, count)

                return added_count

            except Exception as e:
                log.error(f"应用{filter_type}筛选条件并加购商品失败: {str(e)}")
                import traceback
                log.error(traceback.format_exc())
                return 0
                
    def _add_products_to_cart(self, filter_type, items_to_add=1):
        """
        查找商品并加购
        
        Args:
            filter_type: 筛选类型名称
            items_to_add: 要加购的商品数量
        
        Returns:
            int: 成功加购的商品数量
        """
        try:
            # 查找可加购的商品
            product_buttons = []
            add_to_cart_selectors = [
                "//div[@data-testid='btn-atc-plus']",
                "//button[contains(@class, 'add-to-cart')]",
                "//div[contains(@class, 'add-to-cart')]",
                "//i[@data-role='addButtonPlusIcon']",
                "//div[contains(@class, 'AddToCartButton')]"
            ]
            
            for selector in add_to_cart_selectors:
                try:
                    buttons = self.page.query_selector_all(selector)
                    if buttons and len(buttons) > 0:
                        product_buttons = buttons
                        log.info(f"使用选择器 '{selector}' 找到{len(buttons)}个加购按钮")
                        break
                except Exception as e:
                    log.debug(f"使用选择器 '{selector}' 查找加购按钮失败: {str(e)}")
            
            if not product_buttons:
                log.error(f"未找到可加购的{filter_type}商品")
                return 0
            
            log.info(f"找到{len(product_buttons)}个可加购的{filter_type}商品")
            
            # 加购指定数量的商品
            added_count = 0
            for index, item in enumerate(product_buttons):
                if index >= items_to_add:
                    break
                try:
                    # 确保元素可见
                    try:
                        item.scroll_into_view_if_needed()
                        self.page.wait_for_timeout(1000)
                    except Exception as e:
                        log.warning(f"滚动到元素失败: {str(e)}")
                    
                    # 尝试点击
                    try:
                        item.click()
                        self.page.wait_for_timeout(2000)
                        added_count += 1
                        log.info(f"成功加购第{index + 1}个{filter_type}商品")
                    except Exception as e:
                        log.warning(f"点击加购按钮失败，尝试使用JavaScript: {str(e)}")
                        # 尝试使用JavaScript点击
                        try:
                            self.page.evaluate("(element) => element.click()", item)
                            self.page.wait_for_timeout(2000)
                            added_count += 1
                            log.info(f"使用JavaScript成功加购第{index + 1}个{filter_type}商品")
                        except Exception as js_e:
                            log.error(f"使用JavaScript加购也失败: {str(js_e)}")
                except Exception as e:
                    log.error(f"加购第{index + 1}个{filter_type}商品失败: {str(e)}")
            
            log.info(f"成功加购{added_count}个{filter_type}商品")
            return added_count
            
        except Exception as e:
            log.error(f"查找并加购{filter_type}商品失败: {str(e)}")
            return 0


