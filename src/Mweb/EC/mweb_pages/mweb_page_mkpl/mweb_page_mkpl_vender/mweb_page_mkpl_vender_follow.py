from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    ele_follow_seller_btn,
    ele_email_input,
    ele_password_input,
    ele_password_input_type,
    ele_next_step_btn,
    ele_confirm_btn,
    ele_unfollow_modal_container,
    ele_unfollow_confirm_btn
)
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log


class MWebMkplVendorFollowPage(PageH5CommonOperations):
    """
    商家页面关注功能操作类
    """
    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

    def goto_vendor_page(self):
        """访问商家页面"""
        self.page.goto("https://www.sayweee.com/en/mkpl/vendor/6887")
        self.page.wait_for_timeout(3000)
        log.info(f"访问商家页面: {self.page.url}")

    def click_follow_button(self):
        """点击关注按钮"""
        follow_btn = self.page.get_by_test_id(ele_follow_seller_btn)
        if follow_btn.count() > 0:
            follow_btn.click()
            self.page.wait_for_timeout(2000)
            log.info("点击关注按钮成功")
        else:
            log.error("未找到关注按钮")

    """def handle_login_flow(self):
        #处理登录流程
        email_input = self.FE.ele(ele_email_input, timeout=3000)
        if email_input:
            log.info("检测到email输入框，开始登录流程")
            email_input.fill("<EMAIL>")
            
            next_btn = self.FE.ele(ele_next_step_btn, timeout=5000)
            if next_btn:
                next_btn.click()
                self.page.wait_for_timeout(3000)
            
            # 尝试多种密码输入框选择器
            password_input = self.FE.ele(ele_password_input, timeout=3000)
            if not password_input:
                password_input = self.FE.ele(ele_password_input_type, timeout=3000)
            
            if password_input:
                log.info("检测到密码输入框")
                password_input.fill("123456")
                
                next_btn = self.FE.ele(ele_next_step_btn, timeout=5000)
                if next_btn:
                    next_btn.click()
                    self.page.wait_for_timeout(3000)
            else:
                log.info("未检测到密码输入框，可能不需要密码步骤")
            
            return True
        else:
            log.info("未检测到登录页面")
            return False
            
    """

    def get_follow_button_text(self):
        """获取关注按钮文字"""
        follow_btn = self.page.get_by_test_id(ele_follow_seller_btn)
        if follow_btn.count() > 0:
            text = follow_btn.inner_text().strip()
            log.info(f"关注按钮文字: {text}")
            return text
        return ""
    
    def click_confirm_button(self):
        """点击确定按钮"""
        confirm_btn = self.page.get_by_test_id(ele_confirm_btn)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("点击确定按钮成功")
            return True
        else:
            log.info("未找到确定按钮")
            return False
    
    def check_unfollow_modal_elements(self):
        """检查取消关注弹窗元素是否存在"""
        modal = self.page.get_by_test_id(ele_unfollow_modal_container)
        return modal.count() > 0
    
    def click_confirm_button_in_unfollow_modal(self):
        """在取消关注弹窗中点击确定按钮"""
        confirm_btn = self.page.get_by_test_id(ele_unfollow_confirm_btn)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("在取消关注弹窗中点击确定按钮成功")
            return True
        else:
            log.info("在取消关注弹窗中未找到确定按钮")
            return False
