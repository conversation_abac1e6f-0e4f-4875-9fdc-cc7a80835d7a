from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.common.commfunc import empty_cart
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_mkpl_all_store.mweb_mkpl_all_store_ele import (
    ele_seller_product_card,
    ele_seller_product_atc,
    ele_data_count,
    ele_seller_product_minus
)


class MWebMkplAllStorePage(PageH5CommonOperations):

    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        self.page.goto(TEST_URL + "/mkpl/global?mode=sub_page&hide_activity_pop=1?joinEnki=true")
        self.page.wait_for_timeout(5000)

    def navigate_to_global_page(self):
        """访问全球购页面"""
        self.page.goto("https://www.sayweee.com/zh/mkpl/global?mode=sub_page&hide_activity_pop=1")
        self.page.wait_for_timeout(3000)
        log.info("访问全球购页面完成")

    def check_product_card_exists(self):
        """检查商品卡片是否存在"""
        product_card = self.page.get_by_test_id(ele_seller_product_card)
        exists = product_card.count() > 0
        log.info(f"商品卡片存在状态: {exists}")
        return exists

    def check_and_click_atc_button(self):
        """检查并点击加购按钮"""
        atc_button = self.page.get_by_test_id(ele_seller_product_atc)
        if atc_button.count() > 0:
            atc_button.first.click()
            log.info("点击加购按钮成功")
            return True
        else:
            log.info("加购按钮不存在，跳过")
            return False

    def get_cart_count(self):
        """获取购物车数量"""
        count_element = self.page.locator(ele_data_count)
        if count_element.count() > 0:
            count_value = count_element.first.get_attribute("data-count")
            log.info(f"当前购物车数量: {count_value}")
            return int(count_value) if count_value else 0
        return 0

    def execute_add_to_cart_flow(self):
        """执行加购流程"""
        # 1. 访问页面
        self.navigate_to_global_page()

        # 2. 检查商品卡片是否存在
        if not self.check_product_card_exists():
            log.info("商品卡片不存在，结束操作")
            return False

        # 3. 检查并点击加购按钮
        if not self.check_and_click_atc_button():
            log.info("加购按钮不存在，结束操作")
            return False

        # 4. 验证数量变化
        self.page.wait_for_timeout(1000)  # 等待数量更新
        cart_count = self.get_cart_count()

        if cart_count > 0:
            log.info(f"加购成功，当前数量: {cart_count}")
            return True
        else:
            log.info("加购后数量未变化")
            return False

    def click_atc_and_verify_count(self, expected_count):
        """点击加购按钮并验证数量"""
        if self.check_and_click_atc_button():
            self.page.wait_for_timeout(1000)
            actual_count = self.get_cart_count()
            if actual_count == expected_count:
                log.info(f"数量验证成功: {actual_count}")
                return True
            else:
                log.warning(f"数量验证失败，期望: {expected_count}, 实际: {actual_count}")
                return False
        return False
    
    def check_and_click_minus_button(self):
        """检查并点击减购按钮"""
        minus_button = self.page.get_by_test_id(ele_seller_product_minus)
        if minus_button.count() > 0:
            minus_button.first.click()
            log.info("点击减购按钮成功")
            return True
        else:
            log.info("减购按钮不存在，跳过")
            return False
    
    def click_minus_and_verify_count(self, expected_count):
        """点击减购按钮并验证数量"""
        if self.check_and_click_minus_button():
            self.page.wait_for_timeout(1000)
            actual_count = self.get_cart_count()
            if actual_count == expected_count:
                log.info(f"减购数量验证成功: {actual_count}")
                return True
            else:
                log.warning(f"减购数量验证失败，期望: {expected_count}, 实际: {actual_count}")
                return False
        return False
