import time

import allure
from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_cart import mweb_cart_ele
from src.Mweb.EC.mweb_ele.mweb_checkout import mweb_checkout_ele
from src.common.commfunc import empty_cart
from src.common.commonui import home_init, home_init_h5, scroll_one_page_until
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations


class MWebCartPage(MWebCommonPage):
    ele_home_cart = u"div[aria-label='My cart']"
    ele_cart_back_to_home = u"//button[text()='Return home']"
    ele_cart_add_to_cart = u"i[data-role='addButtonPlusIcon'][role='button']"
    ele_cart_checkout_button = u"//button[text()='Checkout']"
    ele_cart_select_all_cart = u"//div[contains(text(), '全选')]/..//div[contains(@class, 'rounded-full')]"
    ele_cart_continue = u"//button[text()='继续']"
    ele_home_bestsellers = u"//h2[text()='人气热卖']"
    ele_checkout_input_your_address = u"//span[text()='请填写您的送货地址及联系方式。']"
    ele_checkout_payment = u"#payment i"
    # 人气热卖
    ele_home_hot_selling_add_to_cart = u"//h2[text()='人气热卖']/ancestor::section//i[@data-role='addButtonPlusIcon']"  # xpath

    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(2000)

    def cart_page_operations(self):
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))

        home_init_h5(self.page)
        self.page.wait_for_timeout(3000)
        self._empty_cart_verification()

    def mweb_start_shopping(self):
        """
        点击空购物车的start_shopping按钮
        """
        # 点击空购物车的start_shopping按钮
        self.FE.ele(mweb_cart_ele.ele_empty_cart_start_shopping).click()

    def checkout_with_paypal(self):
        home_init(self.page)
        self.page.wait_for_timeout(3000)

        if "tb1" in TEST_URL:
            # self.FE.ele(self.ele_home_bestsellers).scroll_into_view_if_needed()
            # self.page.locator(self.ele_home_bestsellers).scroll_into_view_if_needed()
            # self.page.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            # self.page.locator(self.ele_home_bestsellers).scroll_into_view_if_needed()
            self.FE.ele(self.ele_home_bestsellers).hover()
            best_sellers_add_to_cart = self.FE.eles(self.ele_home_hot_selling_add_to_cart)
            for index, item in enumerate(best_sellers_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 2:
                    break
        elif "www" in TEST_URL:
            editor_pick_add_to_cart = self.FE.eles(
                u"//h2[text()='特价精选']/../following-sibling::div//i[@data-role='addButtonPlusIcon']")
            for index, item in enumerate(editor_pick_add_to_cart):
                item.click()
                self.page.wait_for_timeout(1000)
                if index == 2:
                    break

        # 进入购物车
        self.FE.ele(self.ele_home_cart).click()
        self.page.wait_for_timeout(5000)

        self.FE.ele(self.ele_cart_checkout_button).click()

        # 如果购物车有多个，合并购物车
        if self.FE.ele(self.ele_cart_select_all_cart):
            self.FE.ele(self.ele_cart_select_all_cart).click()
            self.page.wait_for_timeout(3000)
            self.FE.ele(
                u"//div[contains(text(), '您可以选择多个购物车进行结算')]//following-sibling::div/button").click()

        # upsell处理
        if self.FE.ele(self.ele_cart_continue):
            self.FE.ele(self.ele_cart_continue).click()

        # 如果没填地址，需要填写地址
        if self.FE.ele(self.ele_checkout_input_your_address):
            self.FE.ele(u"//span[text()='请填写您的送货地址及联系方式。']/../i").click()
            self.page.wait_for_timeout(2000)
            self.FE.eles(u"//div[@data-id]")[0].click()

        # 如果积分支付打开，则把积分支付关掉
        if self.FE.ele(u"i[class*='iconchecked']"):
            self.FE.ele(u"i[class*='iconchecked']").click()

        self.FE.ele(self.ele_checkout_payment).click()
        self.FE.ele(u"//dl[@data-category='P']").click()
        self.page.wait_for_timeout(2000)
        self.FE.ele(self.ele_cart_checkout_button).click()
        self.page.wait_for_timeout(3000)
        if self.FE.ele(u"//div[text()='请确认该国际订单的收件人姓名和地址信息']"):
            self.FE.ele(u"//div[contains(text(), '我确认所提供的姓名是我的真实姓名')]/preceding-sibling::div").click()
            self.FE.ele(u"//div[text()='确认并下单']").click()

        # 校验paypal页面
        if 'tb1.sayweee.net' in TEST_URL:
            PageH5CommonOperations(self.page, self.header).pay_with_paypal(
                account='<EMAIL>',
                password='********'
            )

    def _empty_cart_verification(self):
        self.FE.ele(self.ele_home_cart).click()
        self.page.wait_for_timeout(5000)
        assert self.FE.ele("div[class^='text-center'] img").is_visible()
        assert self.FE.ele(u"//div[text()='Your cart is hungry']").is_enabled()
        assert self.FE.ele(u"//div[contains(text(),'You need to feed your cart with delicious food')]").is_enabled()
        assert len(self.FE.eles("#recommendTabFixed div")) == 1
        self.page.evaluate('window.scrollTo(0, document.body.scrollHeight/2)')

    def save_for_later_operations(self, product_index: int = 1, cart_type=None):
        """
        购物车商品稍后再买操作，跳过带有free、gift标签的商品

        Args:
            product_index: 要操作的商品索引,默认为第一个商品
            cart_type: 购物车类型，可选值: 'normal'(生鲜), 'seller'(商家直发), 'pantry'(零食)
        Returns:
            bool: 操作是否成功
        """
        try:
            log.info(f"开始执行{cart_type}购物车稍后再买操作")

            # 确保页面加载完成
            self.page.wait_for_timeout(2000)

            # 根据购物车类型获取容器
            container_selector = "[data-testid='mod-cart-normal']"  # 默认生鲜购物车
            if cart_type == "seller":
                container_selector = "[data-testid='mod-cart-seller']"
            elif cart_type == "pantry":
                container_selector = "[data-testid='mod-cart-pantry']"

            # 获取购物车容器
            container = self.page.locator(container_selector)

            # 检查容器是否存在，不存在则尝试查找其他容器
            if container.count() == 0:
                for type_info in [
                    {"type": "normal", "selector": "[data-testid='mod-cart-normal']"},
                    {"type": "seller", "selector": "[data-testid='mod-cart-seller']"},
                    {"type": "pantry", "selector": "[data-testid='mod-cart-pantry']"}
                ]:
                    temp_container = self.page.locator(type_info["selector"])
                    if temp_container.count() > 0 and temp_container.is_visible():
                        container = temp_container
                        cart_type = type_info["type"]
                        log.info(f"找到{cart_type}购物车容器")
                        break

            if container.count() == 0:
                log.error("未找到任何购物车容器")
                return False

            # 确保容器可见
            try:
                container.scroll_into_view_if_needed()
                self.page.wait_for_timeout(1000)
            except Exception:
                pass

            # 获取购物车商品卡片
            card_selectors = ["[data-testid='wid-product-card-container']"]
            if cart_type == "seller":
                card_selectors.insert(0, "[data-testid='cart-seller-card']")
            elif cart_type == "pantry":
                card_selectors.insert(0, "[data-testid='cart-pantry-card']")
            else:
                card_selectors.insert(0, "[data-testid='cart-normal-card']")

            # 查找商品卡片
            cards = []
            for selector in card_selectors:
                found_cards = container.locator(selector).all()
                if found_cards and len(found_cards) > 0:
                    cards = found_cards
                    log.info(f"购物车商品统计: 找到{len(cards)}个{cart_type or '所有类型'}购物车商品")
                    # log.info(f"找到{len(cards)}个商品")
                    break

            if not cards:
                log.warning("没有找到可操作的商品")
                return False

            # 查找非free/gift商品
            valid_product = None
            valid_product_title = ""

            for index, card in enumerate(cards):
                if product_index > 0 and index != product_index:
                    continue  # 如果指定了特定索引，只处理该索引的商品

                # 确保商品卡片可见
                card.scroll_into_view_if_needed()

                # 获取商品标题
                title_element = card.locator("[data-testid='wid-product-card-title'], div[class*='title']").first
                if not title_element.is_visible(timeout=1000):
                    continue

                product_title = title_element.text_content().strip()

                # 检查是否是free/gift商品
                price_element = card.locator("[data-testid*='price'], div[class*='price']").first
                if price_element.is_visible(timeout=1000) and "Free" in price_element.text_content():
                    log.info(f"商品 '{product_title}' 是免费商品，跳过")
                    continue

                # 检查是否有free/gift标签
                free_tag = card.locator("[data-testid*='free'], div:text('Free'), div:text('Gift')").first
                if free_tag.count() > 0 and free_tag.is_visible(timeout=1000):
                    log.info(f"商品 '{product_title}' 有Free/Gift标签，跳过")
                    continue

                # 找到非free/gift商品
                valid_product = card
                valid_product_title = product_title
                log.info(f"找到非free/gift商品: '{valid_product_title}'")
                break

            if not valid_product:
                log.warning("没有找到非free/gift商品")
                return False

            # 查找稍后再买按钮
            save_later_btn = valid_product.locator("[data-testid='wid-cart-section-normal-goods-save-for-later-btn'], [data-testid='wid-cart-section-seller-goods-save-for-later-btn'],[data-testid='wid-cart-section-pantry-goods-save-for-later-btn']").first
            self.page.wait_for_timeout(2000)
            if not save_later_btn.is_visible(timeout=1000):
                log.warning("稍后再买按钮不可见")
                return False

            # 记录操作前购物车商品数量
            before_count = len(cards)

            # 点击按钮
            save_later_btn.click()
            self.page.wait_for_timeout(3000)

            # 验证商品是否已移动到稍后再买区域
            # 1. 检查购物车商品数量是否减少
            current_cards = container.locator(card_selectors[0]).all()
            after_count = len(current_cards)
            scroll_one_page_until(self.page, mweb_cart_ele.ele_save_for_later)
            self.page.wait_for_timeout(2000)
            # 2. 检查稍后再买区域
            save_later_section = self.page.get_by_test_id('wid-cart-save-for-later')
            save_later_section_visible = save_later_section.is_visible(timeout=3000)

            # 3. 检查稍后再买区域中的商品
            save_later_cards = self.page.locator(mweb_cart_ele.ele_cart_s4l_card).all()

            # 判断操作是否成功
            if ((after_count < before_count and save_later_section_visible and len(save_later_cards) > 0) or
                (save_later_section_visible and len(save_later_cards) > 0)):
                log.info(f"商品已成功移动到稍后再买区域")
                return True

            log.warning("商品未成功移动到稍后再买区域")
            return False

        except Exception as e:
            log.error(f"稍后再买操作发生异常: {str(e)}")
            return False

    def move_to_cart_from_save_later(self, product_index: int = 0):
        """
        将稍后再买商品移回购物车
        Args:
            product_index: 要操作的商品索引,默认为第一个商品
        Returns:
            bool: 操作是否成功
        """
        try:
            # 获取稍后再买区域的商品
            save_later_cards = self.FE.eles("[data-testid='save-later-card']")
            if not save_later_cards or len(save_later_cards) <= product_index:
                log.info("稍后再买区域没有找到可操作的商品")
                return False

            # 点击移回购物车按钮
            move_to_cart_btn = save_later_cards[product_index].query_selector("[data-testid='btn-move-to-cart']")
            if not move_to_cart_btn:
                log.info("未找到移回购物车按钮")
                return False
            move_to_cart_btn.click()
            self.page.wait_for_timeout(1000)

            # 验证商品是否出现在普通购物车区域
            normal_cards = self.FE.eles("[data-testid='cart-normal-card']")
            if not normal_cards:
                log.info("普通购物车区域未显示商品")
                return False

            return True
        except Exception as e:
            log.error(f"移回购物车操作发生异常: {str(e)}")
            return False

    def verify_cart_page_elements(self):
        """
        校验购物车页面元素

        Returns:
            dict: 包含校验结果的字典
        """

        try:
            # 校验购物车页面标题
            cart_title = self.page.locator("//h1[text()='My cart']")
            return cart_title.is_enabled()
        except Exception as e:
            log.error(f"购物车页面校验失败: {str(e)}")
            return False

    def click_checkout_button_to_next_page(self):
        """
        点击购物车页面的Checkout按钮进入下一个页面

        Returns:
            bool: 是否成功点击Checkout按钮
        """
        try:
            # 点击Checkout按钮
            checkout_btn = self.page.get_by_test_id("btn-checkout").all()[0]
            if checkout_btn.is_visible(timeout=5000):
                checkout_btn.click()
                self.page.wait_for_timeout(3000)
                log.info("成功点击Checkout按钮")
                return True
            else:
                log.error("Checkout按钮不可见")
                return False
        except Exception as e:
            log.error(f"点击Checkout按钮失败: {str(e)}")
            return False

    def verify_cart_middle_page_default_state(self):
        """
        验证购物车中间页默认状态

        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证中间页标题
            title = self.page.locator(mweb_cart_ele.ele_cart_middle_title)
            assert title.is_visible(timeout=5000), "中间页标题不可见"
            assert title.text_content() == "Select carts for checkout", f"中间页标题文本不正确: {title.text_content()}"
            log.info("验证中间页标题成功")

            # 验证全选按钮存在且显示正确文本
            select_all = self.page.locator(mweb_cart_ele.ele_cart_select_all)
            assert select_all.is_visible(timeout=5000), "全选按钮不可见"

            select_all_text = self.page.locator(mweb_cart_ele.ele_cart_select_all_text)
            assert select_all_text.is_visible(timeout=3000), "全选按钮文本不可见"

            # 验证底部提示文本
            tip = self.page.locator(mweb_cart_ele.ele_cart_middle_tip)
            assert tip.is_visible(timeout=3000), "底部提示文本不可见"
            assert "You can select multiple carts for checkout" in tip.text_content(), f"底部提示文本不正确: {tip.text_content()}"
            log.info("验证底部提示文本成功")

            # 验证结算按钮为灰色禁用状态
            checkout_btn = self.page.locator(mweb_cart_ele.ele_cart_middle_checkout)
            assert checkout_btn.is_visible(timeout=3000), "结算按钮不可见"

            # 验证结算按钮是否禁用
            btn_class = checkout_btn.get_attribute("class") or ""
            assert "disabled" in btn_class or "bg-btn-disabled-bg" in btn_class, f"结算按钮未被禁用，应为灰色样式: {btn_class}"
            log.info("验证结算按钮禁用状态成功")

            log.info("所有中间页默认状态验证成功")
            return True
        except Exception as e:
            log.error(f"验证购物车中间页默认状态失败: {str(e)}")
            return False

    def close_cart_middle_page(self):
        """
        关闭购物车中间页

        Returns:
            bool: 是否成功关闭中间页
        """
        try:
            close_btn = self.page.get_by_test_id("btn-modal-close")
            assert close_btn.is_visible(timeout=3000), "关闭按钮不可见"
            close_btn.click()
            self.page.wait_for_timeout(2000)
            return True
        except Exception as e:
            log.error(f"关闭购物车中间页失败: {str(e)}")
            return False

    def get_save_for_later_count(self):
        """
        获取稍后再买区域的商品数量

        Returns:
            int: 稍后再买商品数量
        """
        try:
            # 查找稍后再买区域
            save_later_section_selectors = [
                "[data-testid='save-for-later-section']",
                "div:has-text('Saved for later')"
            ]

            save_later_section = None
            for selector in save_later_section_selectors:
                section = self.page.locator(selector)
                if section.count() > 0 and section.is_visible(timeout=1000):
                    save_later_section = section
                    break

            if not save_later_section:
                log.info("稍后再买区域不存在或不可见")
                return 0

            # 查找稍后再买商品
            save_later_card_selectors = [
                "[data-testid='save-later-card']",
                "div[class*='save-later']",
                "div[class*='saved-item']"
            ]

            for selector in save_later_card_selectors:
                try:
                    cards = save_later_section.locator(selector).all()
                    if cards:
                        count = len(cards)
                        log.info(f"稍后再买区域中找到 {count} 个商品")
                        return count
                except Exception:
                    continue

            # 如果找不到特定选择器，尝试计算子元素
            try:
                all_children = save_later_section.locator("div > div").all()
                possible_cards = [child for child in all_children if child.bounding_box() and child.bounding_box()["height"] > 50]
                count = len(possible_cards)
                log.info(f"通过分析容器子元素估计稍后再买区域有 {count} 个商品")
                return count
            except Exception as e:
                log.warning(f"分析稍后再买区域子元素失败: {str(e)}")

            return 0

        except Exception as e:
            log.error(f"获取稍后再买商品数量失败: {str(e)}")
            return 0

    def get_element_with_fallbacks(self, parent, selectors, timeout=1000):
        """
        使用多个备选选择器查找元素

        Args:
            parent: 父元素Locator
            selectors: 选择器列表
            timeout: 超时时间(毫秒)

        Returns:
            找到的元素Locator或None
        """
        if not parent:
            log.warning("父元素为空，无法查找子元素")
            return None

        if isinstance(selectors, str):
            selectors = [selectors]

        if not selectors:
            log.warning("选择器列表为空")
            return None

        for selector in selectors:
            try:
                element = parent.locator(selector)
                if element.is_visible(timeout=timeout):
                    return element
            except Exception as e:
                log.warning(f"使用选择器 '{selector}' 查找元素失败: {str(e)}")
                continue

        return None

    # def verify_cart_items(self, cart_type=None):
    #     """
    #     购物车验证调用
    #     验证购物车商品信息，包括标题、价格、按钮等元素
    #
    #     Args:
    #         cart_type (str, optional): 购物车类型，可选值: 'normal', 'seller', 'pantry', None(所有类型)
    #
    #     Returns:
    #         bool: 验证是否通过
    #     """
    #
    #     # 确保页面加载完成，增加等待时间
    #     self.page.wait_for_timeout(3000)
    #     log.info(f"开始验证{cart_type or '所有类型'}购物车商品")
    #
    #     # 获取购物车商品
    #     cart_items = []
    #
    #     try:
    #         # 根据购物车类型获取商品
    #         if cart_type == "normal":
    #             # 确保生鲜购物车容器存在
    #             normal_container = self.page.locator(mweb_cart_ele.ele_cart_normal)
    #             # 检查容器是否可见
    #             if normal_container.count() > 0:
    #                 # 方式1: 使用预定义的选择器
    #                 try:
    #                     cards = normal_container.get_by_test_id('wid-product-card-container').all()
    #                     cart_items = cards
    #                     log.info(f"找到{len(cart_items)}个生鲜购物车商品")
    #                 except Exception as e:
    #                     log.warning(f"获取生鲜购物车商品失败: {str(e)}")
    #         elif cart_type == "seller":
    #             # 获取商家直发购物车商品
    #             seller_container = self.page.locator(mweb_cart_ele.ele_cart_seller)
    #             if seller_container.count() > 0:
    #                 try:
    #                     cards = seller_container.get_by_test_id('wid-product-card-container').all()
    #                     cart_items = cards
    #                     log.info(f"找到{len(cart_items)}个mkpl购物车商品")
    #                 except Exception as e:
    #                     log.warning(f"获取mkpl购物车商品失败: {str(e)}")
    #         elif cart_type == "pantry":
    #             # 获取pantry购物车商品
    #             pantry_container = self.page.locator(mweb_cart_ele.ele_cart_pantry)
    #             if pantry_container.count() > 0:
    #                 try:
    #                     cards = pantry_container.get_by_test_id('wid-product-card-container').all()
    #                     cart_items = cards
    #                     log.info(f"找到{len(cart_items)}个pantry+购物车商品")
    #                 except Exception as e:
    #                     log.warning(f"获取pantry购物车商品失败: {str(e)}")
    #
    #
    #         # 记录找到的商品数量
    #         total_items = len(cart_items)
    #         self.page.wait_for_timeout(2000)
    #         log.info(f"购物车商品统计: 找到{total_items}个{cart_type or '所有类型'}购物车商品")
    #
    #         # 验证每个商品的信息
    #         free_items_count = 0
    #         normal_items_count = 0
    #
    #         for index, item in enumerate(cart_items):
    #             try:
    #                 # 确保商品卡片可见
    #                 item.scroll_into_view_if_needed()
    #                 self.page.wait_for_timeout(500)
    #
    #                 # 获取商品标题
    #                 title_element = item.locator("[data-testid='wid-product-card-title'], div[class*='title']").first
    #                 if title_element.is_visible(timeout=1000):
    #                     product_title = title_element.text_content().strip()
    #                     log.info(f"商品{index+1}标题: {product_title}")
    #                 else:
    #                     log.warning(f"商品{index+1}标题元素不可见")
    #
    #                 # 获取商品价格
    #                 price_element = item.locator("[data-testid*='price'], div[class*='price']").first
    #                 if price_element.is_visible(timeout=1000):
    #                     product_price = price_element.text_content().strip()
    #                     log.info(f"商品{index+1}价格: {product_price}")
    #                 else:
    #                     log.warning(f"商品{index+1}价格元素不可见")
    #
    #                 # 检查是否是free/gift商品
    #                 is_free_item = False
    #
    #                 # 方法1: 检查价格文本
    #                 if price_element.is_visible(timeout=1000) and "Free" in price_element.text_content():
    #                     is_free_item = True
    #                     log.info(f"商品{index+1}是免费商品(价格文本)")
    #
    #                 # 方法2: 检查free/gift标签 - 统一使用test_id
    #                 free_tag = item.get_by_test_id('wid-cart-section-normal-goods-free-tag')
    #                 if free_tag.count() > 0 and free_tag.is_visible(timeout=1000):
    #                     is_free_item = True
    #                     log.info(f"商品{index+1}有Free/Gift标签")
    #
    #                 if is_free_item:
    #                     free_items_count += 1
    #                     log.info(f"商品{index+1}是Free/Gift商品，跳过按钮验证")
    #                     continue
    #
    #                 # 非Free/Gift商品，验证加减按钮
    #                 normal_items_count += 1
    #
    #                 # 查找商品数量区域并点击
    #                 quantity_area_selectors = [
    #                     "[data-testid='wid-cart-product-quantity']",
    #                     "div[class*='quantity']",
    #                     "div[class*='amount']",
    #                     "div:has([data-testid='btn-atc-minus'])"
    #                 ]
    #
    #                 quantity_area = None
    #                 for selector in quantity_area_selectors:
    #                     try:
    #                         area = item.locator(selector).first
    #                         if area.count() > 0 and area.is_visible(timeout=2000):
    #                             quantity_area = area
    #                             log.info(f"找到商品{index+1}数量区域")
    #                             break
    #                     except Exception:
    #                         continue
    #
    #                 # 点击数量区域以确保加减按钮显示
    #                 if quantity_area:
    #                     try:
    #                         quantity_area.click()
    #                         self.page.wait_for_timeout(1000)
    #                         log.info(f"已点击商品{index+1}数量区域")
    #                     except Exception as e:
    #                         log.warning(f"点击商品{index+1}数量区域失败: {str(e)}")
    #
    #                 # 检查加减按钮
    #                 minus_btn_selectors = [
    #                     "[data-testid='btn-atc-minus']",
    #                     "button:has([data-testid='icon-minus'])",
    #                     "i[class*='minus']",
    #                     "div[class*='minus']"
    #                 ]
    #
    #                 plus_btn_selectors = [
    #                     "[data-testid='btn-atc-plus']",
    #                     "button:has([data-testid='icon-plus'])",
    #                     "i[class*='plus']",
    #                     "div[class*='plus']"
    #                 ]
    #
    #                 # 使用辅助方法查找按钮
    #                 minus_btn = self.get_element_with_fallbacks(item, minus_btn_selectors, timeout=2000)
    #                 plus_btn = self.get_element_with_fallbacks(item, plus_btn_selectors, timeout=2000)
    #
    #                 if minus_btn:
    #                     log.info(f"商品{index+1}减少按钮可见")
    #                 else:
    #                     log.warning(f"商品{index+1}减少按钮不可见")
    #
    #                 if plus_btn:
    #                     log.info(f"商品{index+1}增加按钮可见")
    #                 else:
    #                     log.warning(f"商品{index+1}增加按钮不可见")
    #
    #                 # 检查稍后再买按钮
    #                 save_later_selectors = [
    #                     "[data-testid='btn-save-for-later']",
    #                     "[data-testid='wid-cart-section-normal-goods-save-for-later-btn']",
    #                     "button:text('Save for later')",
    #                     "div:text('Save for later')"
    #                 ]
    #                 save_later_btn = self.get_element_with_fallbacks(item, save_later_selectors, timeout=2000)
    #
    #                 if save_later_btn:
    #                     log.info(f"商品{index+1}稍后再买按钮可见")
    #                 else:
    #                     log.warning(f"商品{index+1}稍后再买按钮不可见")
    #
    #                 # 检查删除按钮
    #                 delete_btn_selectors = [
    #                     "[data-testid='btn-delete']",
    #                     "[data-testid='btn-remove-product']",
    #                     "[data-testid='wid-cart-section-remove']",
    #                     "button:text('Delete')",
    #                     "div:text('Remove')"
    #                 ]
    #                 delete_btn = self.get_element_with_fallbacks(item, delete_btn_selectors, timeout=2000)
    #
    #                 if delete_btn:
    #                     log.info(f"商品{index+1}删除按钮可见")
    #                 else:
    #                     log.warning(f"商品{index+1}删除按钮不可见")
    #
    #             except Exception as e:
    #                 log.warning(f"验证商品{index+1}信息时发生异常: {str(e)}")
    #
    #         # 汇总验证结果
    #         log.info(f"购物车商品验证完成: 总计{len(cart_items)}个商品，其中Free/Gift商品{free_items_count}个，普通商品{normal_items_count}个")
    #
    #         # 所有验证通过
    #         return len(cart_items) > 0
    #
    #     except Exception as e:
    #         log.error(f"验证购物车商品时发生异常: {str(e)}")
    #         return False
    def verify_cart_items(self, cart_type=None):
        """
        购物车验证调用
        验证购物车商品信息，包括标题、价格、按钮等元素

        Args:
            cart_type (str, optional): 购物车类型，可选值: 'normal', 'seller', 'pantry', None(所有类型)

        Returns:
            bool: 验证是否通过
        """
        try:
            # 确保页面加载完成，增加等待时间
            self.page.wait_for_timeout(3000)
            log.info(f"开始验证{cart_type or '所有类型'}购物车商品")

            # 获取购物车商品
            cart_items = []

            # 根据购物车类型获取商品
            if cart_type == "normal":
                # 确保生鲜购物车容器存在
                normal_container = self.page.locator(f"[data-testid='{mweb_cart_ele.ele_cart_normal}']")
                if normal_container.count() > 0:
                    cart_items = normal_container.locator("[data-testid='wid-product-card-container']").all()
                    log.info(f"找到{len(cart_items)}个生鲜购物车商品")
            elif cart_type == "seller":
                # 获取商家直发购物车商品
                seller_container = self.page.locator(f"[data-testid='{mweb_cart_ele.ele_cart_seller}']")
                if seller_container.count() > 0:
                    cart_items = seller_container.locator("[data-testid='wid-product-card-container']").all()
                    log.info(f"找到{len(cart_items)}个mkpl购物车商品")
            elif cart_type == "pantry":
                # 获取pantry购物车商品
                pantry_container = self.page.locator(f"[data-testid='{mweb_cart_ele.ele_cart_pantry}']")
                if pantry_container.count() > 0:
                    cart_items = pantry_container.locator("[data-testid='wid-product-card-container']").all()
                    log.info(f"找到{len(cart_items)}个pantry+购物车商品")
            else:
                # 如果没有指定类型，尝试获取所有类型的购物车商品
                for container_type in [mweb_cart_ele.ele_cart_normal, mweb_cart_ele.ele_cart_seller, mweb_cart_ele.ele_cart_pantry]:
                    container = self.page.locator(f"[data-testid='{container_type}']")
                    if container.count() > 0:
                        items = container.locator("[data-testid='wid-product-card-container']").all()
                        cart_items.extend(items)
                        log.info(f"找到{len(items)}个{container_type}购物车商品")

            # 记录找到的商品数量
            total_items = len(cart_items)
            self.page.wait_for_timeout(1000)
            log.info(f"购物车商品统计: 找到{total_items}个{cart_type or '所有类型'}购物车商品")

            # 验证每个商品的信息
            free_items_count = 0
            normal_items_count = 0

            for index, item in enumerate(cart_items):
                try:
                    # 确保商品卡片可见
                    item.scroll_into_view_if_needed()
                    self.page.wait_for_timeout(500)

                    # 获取商品标题
                    title_element = item.locator("[data-testid='wid-product-card-title']")
                    if title_element.is_visible(timeout=1000):
                        product_title = title_element.text_content().strip()
                        log.info(f"商品{index + 1}标题: {product_title}")
                    else:
                        log.warning(f"商品{index + 1}标题元素不可见")
                    # 检查是否是free/gift商品
                    is_free_item = False
                    price_selectors = [
                        "[data-testid='wid-cart-section-normal-goods-price']",
                        "[data-testid='wid-cart-section-seller-goods-price']",
                        "[data-testid='wid-cart-section-pantry-goods-price']",
                        "div[class*='price']:not(del)",
                        "span[class*='price']:not(del)"
                    ]

                    product_price = None
                    price_element = None
                    for selector in price_selectors:
                        price_element = item.locator(selector)
                        if price_element.count() > 0 and price_element.is_visible(timeout=1000):
                            product_price = price_element.text_content().strip()
                            log.info(f"商品{index + 1}价格: {product_price}")

                            # 验证价格格式 - 应该包含$符号
                            assert "$" in product_price or "Free" in product_price, f"商品{index + 1}价格格式不正确: {product_price}"
                            break

                    if not product_price:
                        log.warning(f"商品{index + 1}价格元素不可见")

                    # 检查是否有划线价 - 直接从当前商品卡片获取
                    strikethrough_price_selectors = [
                        "[data-testid='wid-cart-section-normal-goods-original-price']",
                        "[data-testid='wid-cart-section-seller-goods-original-price']",
                        "[data-testid='wid-cart-section-pantry-goods-original-price']",
                        "del",
                        "del[class*='price']",
                        "span[class*='original']",
                        "span[class*='strikethrough']",
                        "div[class*='original']"
                    ]

                    has_strikethrough_price = False
                    strikethrough_price = None

                    for selector in strikethrough_price_selectors:
                        try:
                            strikethrough_element = item.locator(selector)
                            if strikethrough_element.count() > 0 and strikethrough_element.is_visible(timeout=1000):
                                has_strikethrough_price = True
                                strikethrough_price = strikethrough_element.text_content().strip()
                                log.info(f"商品{index + 1}有划线价: {strikethrough_price}")

                                # 验证划线价格式 - 应该包含$符号
                                assert "$" in strikethrough_price, f"商品{index + 1}划线价格式不正确: {strikethrough_price}"

                                # 验证划线价应该大于或等于当前价格（如果当前价格不是Free）
                                if product_price and "Free" not in product_price:
                                    try:
                                        current_price_value = float(product_price.replace("$", "").strip())
                                        strikethrough_price_value = float(
                                            strikethrough_price.replace("$", "").strip())
                                        assert strikethrough_price_value >= current_price_value, \
                                            f"商品{index + 1}划线价{strikethrough_price}应大于等于当前价格{product_price}"
                                    except ValueError as ve:
                                        log.warning(f"价格转换失败: {str(ve)}")

                                items_with_strikethrough_price += 1
                                break
                        except Exception as e:
                            log.debug(f"检查商品{index + 1}划线价时出现异常: {str(e)}")

                    if not has_strikethrough_price:
                        log.info(f"商品{index + 1}没有划线价")
                    # 方法1: 检查价格文本
                    if price_element.is_visible(timeout=1000) and "Free" in price_element.text_content():
                        is_free_item = True
                        log.info(f"商品{index + 1}是免费商品(价格文本)")

                    # 方法2: 检查free/gift标签
                    free_tag = item.locator("[data-testid='wid-cart-section-normal-goods-free-tag']")
                    if free_tag.count() > 0 and free_tag.is_visible(timeout=1000):
                        is_free_item = True
                        log.info(f"商品{index + 1}有Free/Gift标签")

                    if is_free_item:
                        free_items_count += 1
                        log.info(f"商品{index + 1}是Free/Gift商品，跳过按钮验证")
                        continue

                    # 非Free/Gift商品，验证加减按钮
                    normal_items_count += 1

                    # 查找商品数量区域并点击
                    quantity_area = item.get_by_test_id('wid-cart-product-quantity')
                    if quantity_area.count() > 0 and quantity_area.is_visible(timeout=2000):
                        quantity_area.click()
                        self.page.wait_for_timeout(1000)
                        log.info(f"已点击商品{index + 1}数量区域")
                    else:
                        log.warning(f"商品{index + 1}数量区域不可见")

                    # 检查加减按钮
                    minus_btn = item.get_by_test_id("btn-atc-minus")
                    if minus_btn.count() > 0 and minus_btn.is_visible(timeout=2000):
                        log.info(f"商品{index + 1}减号按钮可见")
                    else:
                        log.warning(f"商品{index + 1}减号按钮不可见")
                    self.page.wait_for_timeout(1000)
                    plus_btn = item.get_by_test_id('btn-atc-plus')
                    if plus_btn.count() > 0 and plus_btn.is_visible(timeout=2000):
                        log.info(f"商品{index + 1}加号按钮可见")
                    else:
                        log.warning(f"商品{index + 1}加号按钮不可见")

                    # 检查删除按钮
                    delete_btn = item.get_by_test_id('wid-cart-section-normal-goods-remove-btn')
                    if delete_btn.count() > 0 and delete_btn.is_visible(timeout=2000):
                        log.info(f"商品{index + 1}删除按钮可见")
                    else:
                        log.warning(f"商品{index + 1}删除按钮不可见")
                except Exception as e:
                    log.warning(f"验证商品{index + 1}时发生异常: {str(e)}")

            # 汇总验证结果
            log.info(
                f"购物车商品验证完成: 总计{len(cart_items)}个商品，其中Free/Gift商品{free_items_count}个，普通商品{normal_items_count}个")

            # 所有验证通过
            return len(cart_items) > 0
        except Exception as e:
            log.error(f"验证购物车商品时发生异常: {str(e)}")
            return False
    def remove_cart_item(self, product_index: int = 0, cart_type: str = "normal"):
        """
        购物车元素验证在调用
        根据购物车类型删除指定索引的商品，排除带free或gift标签的商品
        
        Args:
            product_index (int): 要删除的商品索引，默认为第一个商品(0)，这里指的是非free/gift商品的索引
            cart_type (str): 购物车类型，可选值: 'normal'(生鲜), 'seller'(商家直发), 'pantry'(零食)
        
        Returns:
            bool: 是否成功删除商品
        """
        try:
            log.info(f"开始删除{cart_type}购物车中索引为{product_index}的非free/gift商品")
            
            # 确保页面加载完成
            self.page.wait_for_timeout(2000)
            
            # 获取购物车商品
            cart_items = []
            # 根据购物车类型获取商品
            if cart_type == "normal":
                # 确保生鲜购物车容器存在
                normal_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_normal)
                # 检查容器是否可见
                if normal_container.count() > 0:
                    # 尝试多种方式获取商品卡片
                    try:
                        cards = normal_container.get_by_test_id('wid-product-card-container').all()
                        cart_items = cards
                        log.info(f"找到{len(cart_items)}个生鲜购物车商品")
                    except Exception as e:
                        log.warning(f"获取生鲜购物车商品失败: {str(e)}")

            elif cart_type == "seller":
                # 获取商家直发购物车商品
                seller_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_seller)
                if seller_container.count() > 0:
                    cart_items = seller_container.get_by_test_id('wid-product-card-container').all()
                    log.info(f"找到{len(cart_items)}个mkpl购物车商品")
            elif cart_type == "pantry":
                # 获取零食购物车商品
                pantry_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_pantry)
                if pantry_container.count() > 0:
                    cart_items = pantry_container.get_by_test_id('wid-product-card-container').all()
                    log.info(f"找到{len(cart_items)}个pantry+购物车商品")
            else:
                log.error(f"不支持的购物车类型: {cart_type}")
                return False
            # 检查是否找到商品
            if not cart_items:
                log.warning(f"未找到{cart_type}购物车商品")
                return False
            
            # 筛选非free/gift商品
            non_free_items = []
            for item in cart_items:
                # 检查是否是free/gift商品
                free_tag = item.locator("[data-testid*='free'], div:text('Free'), div:text('Gift')").first
                if free_tag.count() > 0 and free_tag.is_visible(timeout=1000):
                    log.info("跳过带Free/Gift标签的商品")
                    continue
                
                # 如果不是free/gift商品，添加到列表
                non_free_items.append(item)

            log.info(f"找到{len(non_free_items)}个非free/gift商品")
            
            # 检查商品索引是否有效
            if not non_free_items or product_index >= len(non_free_items):
                log.warning(f"{cart_type}购物车中没有索引为{product_index}的非free/gift商品")
                return False
            
            # 获取目标商品
            target_item = non_free_items[product_index]
            target_item.scroll_into_view_if_needed()
            
            # 获取商品数量，用于后续验证
            before_count = len(cart_items)
            
            # 查找并点击删除按钮
            remove_btn_selectors = [
                "[data-testid='wid-cart-section-normal-goods-remove-btn']",
                "[data-testid='wid-cart-section-seller-goods-remove-btn']",
                "[data-testid='wid-cart-section-pantry-goods-remove-btn']"
            ]
            
            for selector in remove_btn_selectors:
                remove_btn = target_item.locator(selector).first
                if remove_btn.count() > 0 and remove_btn.is_visible(timeout=1000):
                    remove_btn.click()
                    log.info("商品已点击删除按钮")
                    self.page.wait_for_timeout(2000)  # 等待删除操作完成
                    break
            
            # 验证商品是否已删除
            # 重新获取购物车商品
            after_items = []
            if cart_type == "normal":
                normal_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_normal)
                if normal_container.count() > 0:
                    after_items = normal_container.get_by_test_id('wid-product-card-container').all()
            elif cart_type == "seller":
                seller_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_seller)
                if seller_container.count() > 0:
                    after_items = seller_container.get_by_test_id('wid-product-card-container').all()
            elif cart_type == "pantry":
                pantry_container = self.page.get_by_test_id(mweb_cart_ele.ele_cart_pantry)
                if pantry_container.count() > 0:
                    after_items = pantry_container.get_by_test_id('wid-product-card-container').all()
            
            after_count = len(after_items)
            # 验证商品数量是否减少
            if after_count < before_count:
                log.info(f"删除成功: 商品数量从{before_count}减少到{after_count}")
                return True
            else:
                log.warning(f"删除可能失败: 商品数量未减少")
                return False
        
        except Exception as e:
            log.error(f"删除商品时发生异常: {str(e)}")
            return False

    def verify_cart_middle_page_interaction(self):
        """
        验证购物车中间页的交互
    
        步骤：
        1. 点击结算按钮
        2. 验证中间页面元素（标题、全选按钮、购物车选择框等）
        3. 点击关闭按钮
    
        Returns:
            bool: 验证是否成功
        """
        try:
            p = self.page
        
            # 滚动到页面底部找到结算按钮
            p.evaluate('window.scrollTo(0, document.body.scrollHeight)')
            p.wait_for_timeout(2000)

            # 点击结算按钮
            checkout_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_checkout_button)
            if not checkout_btn.is_visible(timeout=3000):
                log.error("结算按钮不可见")
                return False

            log.info("找到结算按钮，准备点击")
            checkout_btn.click()
            p.wait_for_timeout(2000)

            # 验证中间页面元素
            # 1. 验证标题
            title = p.get_by_test_id(mweb_cart_ele.ele_popup_header)
            if not title.is_visible(timeout=3000):
                log.error("中间页标题不可见")
                return False
            
            title_text = title.text_content()
            if title_text != "Select carts for checkout":
                log.error(f"中间页标题文本不正确: {title_text}")
                return False
            
            log.info("验证中间页标题成功")

            # 2. 验证全选按钮
            select_all = p.get_by_test_id(mweb_cart_ele.ele_cart_select_all)
            if not select_all.is_visible(timeout=5000):
                log.error("全选按钮不可见")
                return False

            select_all_text = p.get_by_test_id(mweb_cart_ele.ele_cart_select_all)
            if not select_all_text.is_visible():
                log.error("全选文本不可见")
                return False
            
            if "Select all carts" not in select_all_text.text_content():
                log.error(f"全选文本不正确: {select_all_text.text_content()}")
                return False
            
            log.info("验证全选按钮成功")

            # 3. 验证购物车选择框
            cart_types = {
                "normal": {"name": "Local Delivery", "selector": mweb_cart_ele.ele_cart_select_normal},
                "pantry": {"name": "Pantry", "selector": mweb_cart_ele.ele_cart_select_pantry},
                "seller": {"name": "Seller Direct", "selector": mweb_cart_ele.ele_cart_select_seller}
            }
            
            # 记录找到的购物车类型
            found_cart_types = []
            
            for cart_type, info in cart_types.items():
                try:
                    cart_select = p.get_by_test_id(info["selector"])
                    if cart_select.is_visible(timeout=2000):
                        found_cart_types.append(cart_type)
                        log.info(f"找到{info['name']}购物车选择框")
                except Exception as e:
                    log.debug(f"未找到{info['name']}购物车选择框: {str(e)}")
            
            # 至少应该有一种购物车类型
            if len(found_cart_types) == 0:
                log.error("未找到任何购物车类型选择框")
                return False
            
            log.info(f"验证购物车类型选择框成功，找到以下类型: {', '.join(found_cart_types)}")

            # 4. 验证底部提示文本
            tip = p.get_by_test_id(mweb_cart_ele.ele_cart_middle_tip)
            if not tip.is_visible(timeout=3000):
                log.error("底部提示文本不可见")
                return False
            
            if "You can select multiple carts for checkout" not in tip.text_content():
                log.error(f"底部提示文本不正确: {tip.text_content()}")
                return False
            
            log.info("验证底部提示文本成功")

            # 5. 验证结算按钮
            checkout_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_middle_checkout)
            if not checkout_btn.is_visible(timeout=3000):
                log.error("结算按钮不可见")
                return False

            # 验证结算按钮是否禁用
            btn_class = checkout_btn.get_attribute("class") or ""
            if "disabled" not in btn_class and "btn-disabled" not in btn_class:
                log.error(f"结算按钮未被禁用: {btn_class}")
                return False
            
            log.info("验证结算按钮成功")

            # 6. 验证小计金额
            subtotal = p.get_by_test_id(mweb_cart_ele.ele_cart_middle_subtotal)
            if not subtotal.is_visible(timeout=3000):
                log.error("小计金额不可见")
                return False
            
            if "$0.00" not in subtotal.text_content():
                log.error(f"小计金额不正确: {subtotal.text_content()}")
                return False
            
            log.info("验证小计金额成功")

            # 7. 验证关闭按钮
            close_btn = p.get_by_test_id(mweb_cart_ele.ele_cart_middle_close)
            if not close_btn.is_visible(timeout=3000):
                log.error("关闭按钮不可见")
                return False
            
            log.info("验证关闭按钮成功")

            # 点击关闭按钮
            close_btn.click()
            p.wait_for_timeout(2000)

            log.info("所有中间页元素验证成功")
            return True
        
        except Exception as e:
            log.error(f"验证购物车中间页交互失败: {str(e)}")
            import traceback
            log.error(traceback.format_exc())
            return False

    def get_recommend_product_card(self):
        """
        获取购物车推荐商品卡片
        Returns:
            list: 推荐商品卡片列表
        """
        try:
            recommend_tab = self.page.get_by_test_id("wid-cart-recommend-tab-perference_cart")
            if not recommend_tab.is_visible(timeout=3000):
                log.error("推荐模块不可见")
                return []
            recommend_module = self.page.get_by_test_id("mod-cart-Recommendations")
            if not recommend_module.is_visible(timeout=3000):
                log.error("推荐模块没有商品")
                return []
            recommend_cards = recommend_module.get_by_test_id("wid-product-card-container").all()
            return recommend_cards
        except Exception as e:
            log.error(f"获取推荐商品卡片失败: {str(e)}")
            return []

    def get_buy_again_product_card(self):
        """
        获取购物车再次购买商品卡片
        Returns:
            list: 推荐商品卡片列表
        """
        try:
            bought_tab = self.page.get_by_test_id("wid-cart-recommend-tab-bought")
            if self.page.get_by_test_id("wid-cart-recommend-tab-bought").is_visible():
                # 切换到曾经购买tab下
                bought_tab.click()
                self.page.wait_for_timeout(2000)
                assert self.page.get_by_test_id("mod-cart-Buy Again").is_visible(), "切换到曾经购买tab失败"
                bought_module = self.page.get_by_test_id("mod-cart-Buy Again")
                bought_cards = bought_module.get_by_test_id("wid-product-card-container").all()
                return bought_cards
        except Exception as e:
            log.error(f"获取推荐商品卡片失败: {str(e)}")
            return []

    def add_recommend_product_product(self, count):
        """
        加购推荐商品
        Args:
            count: 要加购的商品数量
        Returns:
            bool: 操作是否成功
        """
        try:
            product_card = self.get_recommend_product_card()
            if not product_card:
                log.warning("没有找到推荐商品")
                return False
            
            for index, item in enumerate(product_card):
                item.get_by_test_id("btn-atc-plus").click()
                self.page.wait_for_timeout(2000)
                log.info("成功加购推荐商品")
                if index == count:
                    break
            return True
        except Exception as e:
            log.error(f"加购推荐商品失败: {str(e)}")
            return False

    def add_buy_again_product_product(self, count):
        """
        加购曾经购买商品
        Args:
            count: 要加购的商品数量
        Returns:
            bool: 操作是否成功
        """
        try:
            product_card = self.get_buy_again_product_card()
            if not product_card:
                log.warning("没有找到曾经购买的商品")
                return False
            
            for index, item in enumerate(product_card):
                item.get_by_test_id("btn-atc-plus").click()
                self.page.wait_for_timeout(2000)
                log.info("成功加购曾经购买商品")
                if index == count:
                    break
            return True
        except Exception as e:
            log.error(f"加购曾经购买商品失败: {str(e)}")
            return False
