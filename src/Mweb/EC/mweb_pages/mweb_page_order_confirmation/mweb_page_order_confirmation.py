"""
<AUTHOR>  li.zhu
@Version        :  V1.0.0
------------------------------------
@File           :   mweb_page_order_confirmation.py
@Description    :  
@CreateTime     :  2025/6/18 11:52
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/8 11:52
"""
from playwright.sync_api import Page, expect

from src.Mweb.EC.mweb_ele.mweb_home import mweb_home_ele
from src.Mweb.EC.mweb_ele.mweb_order_confirmation import mweb_order_confirmation_ele
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.api.zipcode import switch_zipcode
from src.common.commonui import close_advertise_on_home
from src.config.base_config import TEST_URL
from src.config.weee.log_help import log


class MWebOrderComfirmationPage(MWebCommonPage):
    """订单确认页面类"""

    def __init__(self, page: Page, header, browser_context, zipcode: str = "98011", page_url: str = None):
        """
        构造方法主要包含以下功能：
        1. 进入订单成功页面
        2.如果zipcode不是98011，则切换zipcode
        3.调用close_advertise_on_home(self.page)关掉首页的广告
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入订单成功页
        self.page.goto(TEST_URL + page_url)

        # 获取顶部语言
        if not self.page.get_by_test_id("wid-language").locator(
                "//span[text()='English'and contains(@class,'Header')]").all():
            # 切换为英文
            pass
        close_advertise_on_home(self.page)

    def start_earning(self):
        """
        点击开始赚取积分按钮，打开分享弹窗
        """
        # 点击开始赚取积分按钮
        start_button = self.FE.ele(mweb_order_confirmation_ele.ele_start_earning)
        assert start_button.is_visible(), "开始赚取积分按钮不可见"
        start_button.click()
        self.page.wait_for_timeout(2000)
        share_link = self.page.locator("//input[contains(@value, '/order/share/')]").first
        if share_link.is_visible():
            share_url = share_link.get_attribute("value")
            log.info(f"分享链接: {share_url}")

            # 验证链接格式是否正确
            assert "/order/share/grocery/view/" in share_url, "分享链接格式不正确"
            log.info("分享链接格式正确")


    def verify_recommendations_section(self):
        """
        验证订单成功页的推荐商品区域
        
        验证点:
        1. 推荐商品区域标题是否可见
        2. 推荐商品列表是否存在
        3. 第一个推荐商品的名称、价格和加入购物车按钮
        
        Returns:
            dict: 包含验证结果的字典
        """
        p = self.page
        result = {
            "success": True,
            "details": {}
        }
        
        try:
            # 验证推荐商品区域
            recommendations = p.locator(mweb_order_confirmation_ele.ele_recommendations_item)
            is_visible = recommendations.is_visible()
            result["details"]["section_visible"] = is_visible
            
            if is_visible:
                recommendations_text = recommendations.text_content()
                result["details"]["section_title"] = recommendations_text
                
                # 验证推荐商品项
                recommendation_items = p.locator(mweb_order_confirmation_ele.ele_recommendations_item).all()
                result["details"]["items_count"] = len(recommendation_items)
                
                # 验证是否有推荐商品
                if len(recommendation_items) > 0:
                    result["details"]["has_items"] = True
                    first_item = recommendation_items[0]
                    
                    # 验证商品名称
                    item_name = first_item.get_by_test_id("wid-product-card-title")
                    if item_name.is_visible():
                        name_text = item_name.text_content()
                        result["details"]["first_item_name"] = name_text
                    
                    # 验证商品价格
                    item_price = first_item.get_by_test_id("wid-product-card-price")
                    if item_price.is_visible():
                        price_text = item_price.text_content()
                        result["details"]["first_item_price"] = price_text
                        result["details"]["price_has_dollar_sign"] = "$" in price_text
                    
                    # 验证加入购物车按钮
                    add_to_cart_btn = first_item.get_by_test_id("btn-atc-plus")
                    result["details"]["add_to_cart_visible"] = add_to_cart_btn.is_visible()
                else:
                    result["details"]["has_items"] = False
            else:
                result["success"] = False
                result["details"]["error"] = "推荐商品区域不可见"
        
        except Exception as e:
            result["success"] = False
            result["details"]["error"] = str(e)
        
        return result
