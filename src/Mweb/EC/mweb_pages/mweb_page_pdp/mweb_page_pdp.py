from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.Dweb.EC.dweb_ele.dweb_product.dweb_pdp_ele import *
from src.common.commfunc import empty_cart

from src.config.weee.log_help import log
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage


class MWebPDPPage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入PDP页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 清空购物车
        try:
            empty_cart(self.header)
        except Exception as e:
            log.info("清空购物车发生异常" + str(e))
        # 关闭首页广告
        self.close_advertisement_in_homepage()


    def goto_pdp_and_check(self, pdp_url):
        """
        进入pdp页面校验
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url)

        # 2. 校验页面
        self._check_pdp_page()

        # 3. 将related商品加入购物车
        related_add_to_cart_button_list = self.FE.eles(ele_pdp_related_add_to_cart_button)
        self.add_pdp_related_products_to_cart(related_add_to_cart_button_list, 3)

    def goto_fbw_pdp_and_check(self, url):
        # 1. 进入fbw pdp页面
        self.page.goto(url)

        # 2. 元素校验
        self._check_fbw_pdp_page()

        # 3. 将fbw mweb_page_pdp related商品加入购物车
        # 这种用locator的方式有问题，会导致页面刷新，按钮次序不对
        # fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id("btn-atc-plus").all()
        fbw_related_products_add_to_cart_buttons = self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id(
            "btn-atc-plus").element_handles()
        if fbw_related_products_add_to_cart_buttons:
            self.add_pdp_related_products_to_cart(fbw_related_products_add_to_cart_buttons, 3)

    def goto_pdp_and_check_same_vendor(self, pdp_url):
        """
        此方法包含以下功能：
        1. 进入pdp页面
        2. 校验页面基本元素
        3. 校验店铺推荐模块
        4. 验证店铺推荐商品信息（商品名、价格）
        5. 加购店铺推荐商品
        """
        # 1. 进入pdp页面
        self.page.goto(pdp_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)

        # 2. 校验页面
        self._check_pdp_page()

        # 3. 滚动到店铺推荐模块
        from src.common.commonui import scroll_one_page_until
        from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
        scroll_one_page_until(self.page, mweb_pdp_ele.ele_pdp_same_vendor_card)

        # 4. 校验店铺推荐模块
        self._check_same_vendor_module()

        # 5. 加购店铺推荐商品
        self._add_same_vendor_products_to_cart()

    def _check_same_vendor_module(self):
        """
        校验店铺推荐模块元素
        """
        from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele

        # 校验店铺推荐模块存在
        assert self.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_card).is_visible(), "店铺推荐模块不可见"

        # 校验店铺推荐标题
        assert self.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_title).is_visible(), "店铺推荐标题不可见"

        # 校验查看全部按钮
        assert self.FE.ele(mweb_pdp_ele.ele_pdp_same_vendor_view_all).is_visible(), "查看全部按钮不可见"

        # 校验商品卡片存在
        same_vendor_products = self.FE.eles(mweb_pdp_ele.ele_pdp_same_vendor_product_card)
        assert len(same_vendor_products) > 0, "店铺推荐商品卡片不存在"

        # 校验第一个商品的基本信息
        first_product = same_vendor_products[0]
        product_name = first_product.query_selector("div[data-testid='wid-product-card-title'] div").text_content()
        product_price = first_product.query_selector("div[data-testid='wid-product-card-price'] div div").text_content()

        assert product_name, "商品名称为空"
        assert product_price and product_price.startswith("$"), "商品价格格式不正确"

        log.info(f"店铺推荐商品名称: {product_name}")
        log.info(f"店铺推荐商品价格: {product_price}")

    def _add_same_vendor_products_to_cart(self):
        """
        加购店铺推荐商品
        """
        from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele

        # 获取加购按钮
        atc_buttons = self.FE.eles(mweb_pdp_ele.ele_pdp_same_vendor_atc_btn)

        if atc_buttons:
            # 点击第一个商品的加购按钮
            first_atc_btn = atc_buttons[0]
            first_atc_btn.click()
            self.page.wait_for_timeout(2000)
            log.info("成功加购店铺推荐商品")
    def _check_pdp_page(self):
        # networkidle比load, domcontentloaded更稳定
        self.page.wait_for_load_state("networkidle", timeout=60000)
        self.pdp_page_common_check()
        self.FE.ele(ele_pdp_header_content).is_visible()
        self.page.get_by_test_id("wid-mweb_page_pdp-related-card").is_visible()

    def _check_fbw_pdp_page(self):
        self.page.wait_for_load_state("networkidle", timeout=60000)
        self.pdp_page_common_check()
        assert self.FE.ele("//span[text()='Fresh daily']").is_visible()
        assert self.FE.ele("//span[text()='Shop more']").is_visible()
        # 商品名
        assert self.page.locator("div[class^='Header_desc'] h2").is_visible()
        # 商品 subname
        assert self.page.locator("div[class^='Header_subname']").is_visible()
        # 商品价格
        assert self.page.locator(
            "div[class^='Header_desc'] div[class^='Header_price_price']").text_content().startswith("$")
        # related products
        assert self.page.get_by_test_id("wid-mweb_page_pdp-related-card").get_by_test_id(
            "wid-product-card-container").all(), f"fbw pdp页面没有找到related products"

    def close_advertisement_in_homepage(self):
        if self.page.locator("//img[contains(@aria-label, 'close button')]").all():
            self.page.locator("//img[contains(@aria-label, 'close button')]").click()

    def check_reviews_total_count_display(self):
        """
        检查reviews栏总review数显示
        """
        from src.Mweb.EC.mweb_ele.mweb_product import mweb_pdp_ele
        reviews_count_element = self.FE.ele(mweb_pdp_ele.ele_reviews_total_count)
        assert reviews_count_element.is_visible(), "Reviews总数未显示"
        count_text = reviews_count_element.text_content()
        assert count_text and count_text.strip(), "Reviews总数文本为空"
        return count_text

