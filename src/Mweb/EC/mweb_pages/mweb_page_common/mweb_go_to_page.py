from src.config.base_config import TEST_URL
from playwright.sync_api import Page
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage


class MWebGoToPage(MWebCommonPage):
    def __init__(self, page: Page, header, browser_context, page_url: str = None):
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入指定页面
        self.page.goto(TEST_URL + page_url + "?joinEnki=true")
        self.page.wait_for_timeout(5000)
