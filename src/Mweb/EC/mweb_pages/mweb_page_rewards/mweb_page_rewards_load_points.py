from playwright.sync_api import Page, TimeoutError
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_common_page import MWebCommonPage
from src.Mweb.EC.mweb_ele.mweb_rewards.mweb_rewards_ele import *
from src.common.commonui import scroll_one_page_until
from src.config.weee.log_help import log
from src.config.base_config import TEST_URL


class MWebRewardsLoadPointsPage(MWebCommonPage):
    """积分升级页面操作类 - MWeb版本"""
    
    def __init__(self, page: Page, header, browser_context, page_url: str = "/account/rewards/level"):
        """
        初始化积分升级页面
        :param page: Playwright页面对象
        :param header: 请求头
        :param browser_context: 浏览器上下文
        :param page_url: rewards页面URL路径
        """
        super().__init__(page, header)
        self.bc = browser_context
        # 直接进入rewards页面
        self.page.goto(TEST_URL + "/" + page_url)
        # 等待页面加载完成
        self.page.wait_for_load_state("load")
        log.info(f"成功进入rewards页面: {TEST_URL}/{page_url}")

    def click_upgrade_now(self):
        """
        点击立即升级按钮
        """
        self.FE.ele(ele_rewards_upgrade_button).click()
        log.info("成功点击立即升级按钮")

    def select_gold_upgrade(self):
        """
        选择升级黄金模块
        """
        self.FE.ele(ele_rewards_upgrade_purchase_pop_option_desc).click()
        log.info("成功选择升级黄金模块")

    def select_payment_method(self):
        """
        点击支付方式选择框
        """
        self.FE.ele(ele_payment_box).click()
        log.info("成功点击支付方式选择框")

    def select_paypal(self):
        """
        选择PayPal支付方式
        """
        self.FE.ele(ele_paypal_payment).click()
        log.info("成功选择PayPal支付方式")

    def click_load_and_upgrade(self):
        """
        点击充值并且升级按钮
        """
        self.FE.ele(ele_rewards_upgrade_btn).click()
        log.info("成功点击充值并且升级按钮")

    def click_pay_method_confirm(self):
        """
        点击支付方式确认按钮（充值并且升级按钮）
        """
        self.FE.ele(ele_pay_method_confirm_btn).click()
        log.info("成功点击支付方式确认按钮")

