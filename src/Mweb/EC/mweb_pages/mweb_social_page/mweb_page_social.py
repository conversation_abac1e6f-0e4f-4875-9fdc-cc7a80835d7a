from playwright.sync_api import Page

from src.Mweb.EC.mweb_ele.mweb_social.mweb_social_ele import *
from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.base_config import TEST_URL

class MWebPageSocial(PageH5CommonOperations):

    def __init__(self, page: Page, header, bc):
        super().__init__(page, header)
        self.bc = bc


        self.page.goto(TEST_URL + "/social?joinEnki=true")
        self.page.wait_for_timeout(5000)
        # 关闭首页广告
        self.close_advertisement_in_homepage()

    def click_search(self, username):
        self.page.locator("#searchElId").click()
        _input = self.page.get_by_placeholder("What are you looking for?")
        _input.click()
        _input.fill(username)
        self.page.keyboard.press("Enter")
        self.page.wait_for_timeout(2000)
        self.page.locator("//button[text()='Videos']").click()
        self.page.wait_for_timeout(2000)
        self.page.wait_for_selector("//button[text()='Accounts']").click()
        self.page.wait_for_timeout(3000)
        assert self.page.locator(f"//span[text()='{username}']").all()
        self._follow()
        self.page.wait_for_selector("//span[text()='Following']").click()

    def account_verify(self, username):
        self.page.locator(f"//span[text()='{username}' and contains(@class, 'enki')]").click()
        self.page.wait_for_timeout(2000)
        assert self.page.locator("//span[text()='Follow']").all() or self.page.locator("//span[text()='Following']").all()
        self._follow()
        assert self.page.locator("//div[text()='Following']").all()
        assert self.page.locator("//div[text()='Followers']").all()
        assert self.page.locator("//div[text()='Likes']").all()
        assert self.page.locator("//button[text()='Posts']").all()
        assert self.page.locator("//button[text()='Reviews']").all()

    def _follow(self):
        if self.page.locator("//span[text()='Follow']").all():
            self.page.locator("//span[text()='Follow']").all()[0].click()
        if self.page.locator("//span[text()='Following']").all():
            self.page.locator("//span[text()='Following']").all()[0].click()







