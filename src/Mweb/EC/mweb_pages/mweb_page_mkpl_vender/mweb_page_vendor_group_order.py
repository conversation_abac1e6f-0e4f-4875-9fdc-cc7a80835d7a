from playwright.sync_api import Page

from src.Mweb.EC.mweb_pages.mweb_page_common.mweb_page_common_operations import PageH5CommonOperations
from src.config.weee.log_help import log
from src.Mweb.EC.mweb_ele.mweb_mkpl_vender.mweb_mkpl_vender_ele import (
    ele_create_group_order,
    ele_group_order_pop_up,
    ele_group_order_pop_up_close,
    ele_group_order_pop_up_confirm,
    ele_group_order_share_pop_up,
    ele_group_order_share_copy_link,
    ele_group_order_cancel_button,
    ele_group_order_delete_btn,
    ele_group_order_cancel_popup,
    ele_group_order_cancel_popup_cancel_confirm,
    ele_seller_tab_all
)


class MWebVendorGroupOrderPage(PageH5CommonOperations):

    def __init__(self, page: Page, header, browser_context):
        super().__init__(page, header)
        self.bc = browser_context

    def navigate_to_vendor_page(self):
        """访问商家页面"""
        self.page.goto("https://www.sayweee.com/zh/mkpl/vendor/6887")
        self.page.wait_for_timeout(5000)
        log.info("访问商家页面完成")

    """
    def check_group_order_button_exists(self):
        #检查好友拼单按钮是否存在#
        group_order_btn = self.page.get_by_test_id(ele_create_group_order)
        if group_order_btn.count() > 0:
            log.info("好友拼单按钮存在")
            return True
        else:
            # 等待10秒后再次检查
            self.page.wait_for_timeout(10000)
            if group_order_btn.count() > 0:
                log.info("等待后好友拼单按钮存在")
                return True
            else:
                log.info("好友拼单按钮不存在")
                return False
    """

    def click_group_order_button(self):
        """点击好友拼单按钮"""
        group_order_btn = self.page.get_by_test_id(ele_create_group_order)
        if group_order_btn.count() > 0:
            group_order_btn.click()
            log.info("点击好友拼单按钮成功")
            return True
        else:
            log.info("好友拼单按钮不存在")
            return False

    def check_popup_exists(self):
        """检查弹窗是否存在"""
        popup = self.page.get_by_test_id(ele_group_order_pop_up)
        exists = popup.count() > 0
        log.info(f"拼单弹窗存在状态: {exists}")
        return exists

    def close_popup(self):
        """关闭弹窗"""
        close_btn = self.page.get_by_test_id(ele_group_order_pop_up_close)
        if close_btn.count() > 0:
            close_btn.click()
            log.info("关闭拼单弹窗成功")
            return True
        else:
            log.info("弹窗关闭按钮不存在")
            return False

    def click_confirm_button(self):
        """点击邀请好友按钮"""
        confirm_btn = self.page.get_by_test_id(ele_group_order_pop_up_confirm)
        if confirm_btn.count() > 0:
            confirm_btn.click()
            log.info("点击邀请好友按钮成功")
            return True
        else:
            log.info("邀请好友按钮不存在")
            return False

    def check_share_popup_exists(self):
        """检查分享弹窗是否存在"""
        share_popup = self.page.get_by_test_id(ele_group_order_share_pop_up)
        exists = share_popup.count() > 0
        log.info(f"分享弹窗存在状态: {exists}")
        return exists

    def click_copy_link_button(self):
        """点击复制链接按钮"""
        copy_link_btn = self.page.get_by_test_id(ele_group_order_share_copy_link)
        if copy_link_btn.count() > 0:
            copy_link_btn.click()
            log.info("点击复制链接按钮成功")
            return True
        else:
            log.info("复制链接按钮不存在")
            return False

    def click_delete_button(self):
        """点击删除按钮"""
        delete_btn = self.page.get_by_test_id(ele_group_order_cancel_button)
        if delete_btn.count() > 0:
            delete_btn.click()
            log.info("点击删除按钮成功")
            return True
        else:
            log.info("删除按钮不存在")
            return False

    def check_cancel_popup_exists(self):
        """检查取消弹窗是否存在"""
        cancel_popup = self.page.get_by_test_id(ele_group_order_cancel_popup)
        exists = cancel_popup.count() > 0
        log.info(f"取消弹窗存在状态: {exists}")
        return exists

    def click_cancel_confirm_button(self):
        """点击取消确认按钮"""
        cancel_confirm_btn = self.page.get_by_test_id(ele_group_order_cancel_popup_cancel_confirm)
        if cancel_confirm_btn.count() > 0:
            cancel_confirm_btn.click()
            log.info("点击取消确认按钮成功")
            return True
        else:
            log.info("取消确认按钮不存在")
            return False

    def check_page_url(self, expected_url):
        """检查页面URL"""
        current_url = self.page.url
        log.info(f"当前URL: {current_url}")
        return expected_url in current_url

    def check_and_click_seller_tab_all(self):
        """检查并点击全部商品标签页"""
        tab_all_element = self.page.locator(ele_seller_tab_all)
        if tab_all_element.count() > 0:
            first_tab = tab_all_element.first
            if first_tab.is_visible():
                first_tab.click()
                log.info("点击全部商品标签页成功")
                return True
        log.info("全部商品标签页不存在或不可见，跳过操作")
        return False

    def execute_group_order_flow(self):
        """执行好友拼单流程"""
        self.navigate_to_vendor_page()
        
        if not self.click_delete_button():
            return False
        
        self.click_group_order_button()
        self.page.wait_for_timeout(3000)
        
        if self.check_popup_exists():
            self.page.wait_for_timeout(3000)
            self.close_popup()
        
        self.click_group_order_button()
        self.page.wait_for_timeout(3000)
        self.click_confirm_button()
        
        self.page.wait_for_timeout(5000)
        if self.check_share_popup_exists():
            self.page.wait_for_timeout(3000)
            self.click_copy_link_button()
            
            self.page.wait_for_timeout(3000)
            self.click_delete_button()
            
            self.page.wait_for_timeout(3000)
            if self.check_cancel_popup_exists():
                self.page.wait_for_timeout(3000)
                self.click_cancel_confirm_button()
                
                self.page.wait_for_timeout(3000)
                return self.check_page_url("https://www.sayweee.com/en/mkpl/vendor/6887")
        
        return True