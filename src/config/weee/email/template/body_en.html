<div id="resultContainer" class="card">
    <div class="card-header border-bottom">
        <span style="float: left;">
            <h5 class="mb-0">Result</h5>
        </span>
        <span style="float: right;">
            <a href='javascript:showStep(0, {{ channel }})' class="btn btn-dark btn-sm">Summary</a>
            <a href='javascript:showStep(1, {{ channel }})' class="btn btn-success btn-sm">Pass</a>
            <a href='javascript:showStep(2, {{ channel }})' class="btn btn-warning btn-sm">Failed</a>
            <a href='javascript:showStep(3, {{ channel }})' class="btn btn-danger btn-sm">Error</a>
            <a href='javascript:showStep(4, {{ channel }})' class="btn btn-secondary btn-sm">Skip</a>
            <a href='javascript:showStep(5, {{ channel }})' class="btn btn-info btn-sm">All</a>
        </span>
    </div>
    <div class="table-responsive">
        <table class="table table-hover table-nowrap">
            <thead class="table-light">
            <tr>
                <th scope="col">Case Description</th>
                <th scope="col">Test Group/Test Case</th>
                <th scope="col">case_type</th>
                <th scope="col">Duration</th>
                <!--                    <th scope="col">Count(times)</th>-->
                <!--                    <th scope="col">Pass(times)</th>-->
                <!--                    <th scope="col">Fail(times)</th>-->
                <!--                    <th scope="col">Error(times)</th>-->
                <th scope="col">status</th>
                <th scope="col">View</th>
                <!--                <th scope="col">Screenshots</th>-->
            </tr>
            </thead>
            <tbody>
            {{ test_list }}
            </tbody>
        </table>
    </div>
    <div class="card-footer border-0 py-5">
        <span class="text-muted text-sm">Cases Total:
            <button type="button"
                    class="btn btn-sm bg-dark bg-opacity-20 bg-opacity-100-hover text-dark text-white-hover">{{ count }}</button> =
            <button type="button"
                    class="btn btn-sm bg-success bg-opacity-20 bg-opacity-100-hover text-success text-white-hover">{{ Pass }}</button> +
            <button type="button"
                    class="btn btn-sm bg-warning bg-opacity-20 bg-opacity-100-hover text-warning text-white-hover">{{ fail }}</button> +
            <button type="button"
                    class="btn btn-sm bg-danger bg-opacity-20 bg-opacity-100-hover text-danger text-white-hover">{{ error }}</button> +
            <button type="button"
                    class="btn btn-sm bg-secondary bg-opacity-20 bg-opacity-100-hover text-secondary text-white-hover">{{ skip }}</button>
        </span>
    </div>
</div>
<div style="height:120px"></div>
