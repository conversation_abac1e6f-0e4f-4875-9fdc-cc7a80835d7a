<div width="70%" cellpadding="0" cellspacing="0"
     style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;
        box-sizing: border-box;
        display: inline-block;
        font-size: 14px;
        overflow: hidden;
        border-radius: 7px;
        margin: 0;
        border: 1px solid #e9e9e9;
        width: 70%;
        background-color: #f5f9fc;">
    <div style="font-size: 16px;
            vertical-align: top;
            color: #fff;
            background-color: #006400;
            padding: 20px;">
<!--        <img src="{{ weee_icon }}" />-->
        <span style="margin-top:20px;display:block"> PC UI Test Report </span>
    </div>
    <div style="margin-top: 30px;
        margin-left: 20px;
        margin-right: 20px;
        margin-bottom: 30px;">
        <table width="100%" cellpadding="0" cellspacing="0" style="font-size:14px;margin:0">
            <tbody>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Title:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_title }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    Tester:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_tester }}
                </td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Start Time:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ start_time }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    End Time:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ end_time }}
                </td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Duration:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ duration }}
                </td>
                <td style="padding:0 0 20px; width: 25%;"></td>
                <td style="padding:0 0 20px; width: 25%;"></td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Pass Case:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_pass }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    Pass Rate:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ pass_rate }}
                </td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Fail Case:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_fail }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    Fail Rate:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ failure_rate }}
                </td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Error Case:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_error }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    Error Rate:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ error_rate }}
                </td>
            </tr>
            <tr>
                <td style="padding:0 0 20px; width: 25%;">
                    Skip Case:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ mail_skip }}
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    Skip Rate:
                </td>
                <td style="padding:0 0 20px; width: 25%;">
                    {{ skip_rate }}
                </td>
            </tr>
            </tbody>
        </table>


    </div>
</div>
<p></p>
<div width="70%" cellpadding="0" cellspacing="0"
     style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;
        box-sizing: border-box;
        display: inline-block;
        font-size: 14px;
        overflow: hidden;
        border-radius: 7px;
        margin: 0;
        border: 1px solid #e9e9e9;
        width: 70%;
        background-color: #f5f9fc;">
        <h3 align="center">Details For Failed Cases</h3>
        <table style="width:100%;border:2px solid">
            <thead>
                <tr>
                    <th>Case Desc</th>
                    <th>Case Name</th>
                    <th>Error MSG</th>
                </tr>
            </thead>
            {% for item in error_case_info %}
            <tr>
                <td align="center">{{ item.case_desc}}</td>
                <td align="center">{{ item.case_name}}</td>
                <td align="center">{{ item.out_message}}</td>
            </tr>
            {% endfor %}

        </table>
</div>
<!--<p></p>-->
<!--<div width="70%" cellpadding="0" cellspacing="0"-->
<!--     style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;-->
<!--        box-sizing: border-box;-->
<!--        display: inline-block;-->
<!--        font-size: 14px;-->
<!--        overflow: hidden;-->
<!--        border-radius: 7px;-->
<!--        margin: 0;-->
<!--        border: 1px solid #e9e9e9;-->
<!--        width: 70%;-->
<!--        background-color: #f5f9fc;">-->
<!--        <h3 align="center">Stat Group By Module For Scenario</h3>-->
<!--        <table style="width:100%;border:2px solid">-->
<!--            <thead>-->
<!--                <tr style="">-->
<!--                    <th>Module</th>-->
<!--                    <th>Total</th>-->
<!--                    <th>Success</th>-->
<!--                    <th>Fail</th>-->
<!--                    <th>Skipped</th>-->
<!--                </tr>-->
<!--            </thead>-->
<!--            {% for item in group_by_info %}-->
<!--            <tr>-->
<!--                <td align="center">{{ item.module}}</td>-->
<!--                <td align="center">{{ item.success + item.fail + item.error + item.skip }}</td>-->
<!--                <td align="center">{{ item.success}}</td>-->
<!--                <td align="center"><font color="{{ 'red' if item.fail > 0}}">{{ item.fail }}</font></td>-->
<!--                <td align="center">{{ item.skip}}</td>-->
<!--            </tr>-->
<!--            {% endfor %}-->

<!--        </table>-->
<!--</div>-->
<!--<p></p>-->
<!--<div width="70%" cellpadding="0" cellspacing="0"-->
<!--     style="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;-->
<!--        box-sizing: border-box;-->
<!--        display: inline-block;-->
<!--        font-size: 14px;-->
<!--        overflow: hidden;-->
<!--        border-radius: 7px;-->
<!--        margin: 0;-->
<!--        border: 1px solid #e9e9e9;-->
<!--        width: 70%;-->
<!--        background-color: #f5f9fc;">-->
<!--        <h3 align="center">Stat Group By Module For Simple API</h3>-->
<!--        <table style="width:100%;border:2px solid">-->
<!--            <thead>-->
<!--                <tr style="">-->
<!--                    <th>Module</th>-->
<!--                    <th>Total</th>-->
<!--                    <th>Success</th>-->
<!--                    <th>Fail</th>-->
<!--                    <th>Skipped</th>-->
<!--                </tr>-->
<!--            </thead>-->
<!--            {% for item in group_by_info_simple %}-->
<!--            <tr>-->
<!--                <td align="center">{{ item.module}}</td>-->
<!--                <td align="center">{{ item.success + item.fail + item.error + item.skip }}</td>-->
<!--                <td align="center">{{ item.success}}</td>-->
<!--&lt;!&ndash;                <td align="center" bgcolor="{{ 'red' if item.fail > 0}}">{{ item.fail }}</td>&ndash;&gt;-->
<!--                <td align="center"><font color="{{ 'red' if item.fail > 0}}">{{ item.fail }}</font></td>-->
<!--                <td align="center">{{ item.skip}}</td>-->
<!--            </tr>-->
<!--            {% endfor %}-->

<!--        </table>-->
<!--</div>-->

<p align="center">
    <h3> Allure Report </h3>
    <a href="{{ allure_report }}">allure report</a>
</p>

