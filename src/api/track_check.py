from src.utils.HttpRequest import HttpRequest as HR


def query_track(user_id, platform, begin_at, end_at):
    res = HR.request({
        "method": "post",
        "path": "http://10.201.110.22:8888/api/datatrack/clickhousedata",
        "request_body": {
            "userId": user_id,
            "platform": platform,
            "beginAt": begin_at,
            "endAt": end_at
        }
    })
    return res.json()
