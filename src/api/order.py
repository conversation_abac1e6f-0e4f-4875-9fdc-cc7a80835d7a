from src.config.base_config import BASE_URL
from src.utils.HttpRequest import HttpRequest as HR


def cancel_points_order(headers, order_id):
    """取消积分订单"""
    data = {
        "orderId": order_id,
        "refund_reason": "fp-issue_other",
        "source": "central-cs"
    }

    res = HR.request({
        "method": "post",
        "path": BASE_URL + "/central/so/cancel/points_order",
        "headers": headers,
        "request_body": data
    })
    return res.json()


def query_all_orders(headers, data):
    res = HR.request({
        "method": "post",
        "path": BASE_URL + "/ec/so/order/query/listMyOrder/v2",
        "headers": headers,
        "request_body": data
    })
    return res.json()
