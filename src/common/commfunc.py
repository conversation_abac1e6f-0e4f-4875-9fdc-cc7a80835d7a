from src.api.cart import query_preorder_v5, porder_items_v3, remove_cart_v3, remove_save_for_later
from src.api.porder import query_simple_preorder_v1, update_zipcode_v1
from jmespath import search


def jmespath(data, expression, options=None):
    """
    search jmespath data
    jmespath search data
    https://github.com/jmespath/jmespath.py
    """
    return search(expression, data, options)


class CommonCheck:

    @staticmethod
    def list_check(list1, list2):
        """ 判断列表1是否存在于列表2中"""
        result = True
        for l1 in list1:
            status = True if l1 in list2 else False
            result = result and status

        return result

    @staticmethod
    def set_porder(header, zipcode):
        porder = query_simple_preorder_v1(headers=header)["object"]
        if porder["zipcode"] != zipcode:
            # 切换账号zipcode到98011
            update_zipcode_v1(headers=header, zipcode=zipcode)


def empty_cart(header):
    porder = query_simple_preorder_v1(headers=header)["object"]
    my_porder = query_preorder_v5(headers=header)
    sections = my_porder["object"]["sections"]
    invalid_items = my_porder["object"]["invalid_items"]
    nested_activity_items = jmespath(my_porder, "object.sections[*].activity_info[*].items[*].product_id")
    save_for_later = my_porder["object"]["save_for_later_response"]
    if len(sections) > 0:
        for section in sections:
            items = section.get("items")
            for item in items:
                # 移除购物车正常商品
                remove_cart_v3(
                    headers=header,
                    product_id=item.get("product_id"),
                    date=porder["delivery_pickup_date"],
                    refer_type=section["cart_id"]
                )
    if invalid_items:
        for item in invalid_items:
            # 移除购物车失效商品
            remove_cart_v3(
                headers=header,
                product_id=item.get("product_id"),
                date=porder["delivery_pickup_date"]
            )
    activity_item_lst = [item for cart in nested_activity_items for activity in cart for item in activity]
    if activity_item_lst:
        for item in activity_item_lst:
            # 移除换购商品、赠品
            remove_cart_v3(
                headers=header,
                product_id=item,
                date=porder["delivery_pickup_date"]
            )

    if save_for_later and save_for_later.get("items"):
        for item in save_for_later["items"]:
            # 移除购物车save_for_later商品
            remove_save_for_later(headers=header, product_keys=item.get("product_id"))


if __name__ == '__main__':
    empty_cart("header")
