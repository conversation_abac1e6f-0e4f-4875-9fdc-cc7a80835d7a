import json


class TrackVerificationHomePageV2:
    @staticmethod
    def home_t2_click_product(track: [], mod_nm, page_key):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get('params'):
                    track_params = json.loads(item.get('params'))
                    if track_params.get('co') and track_params.get('co').get('target_type'):
                        if track_params.get('co').get('target_type') == 'product':
                            if track_params.get('mod_nm') == mod_nm:
                                if track_params.get('mod_nm') == 'cm_collection_v2_manual_rank2' or track_params.get('mod_nm') == 'cm_item_ads_collection':
                                    if track_params.get('sec_nm'):
                                        return True
                                    else:
                                        return False
                                flag = True
        return flag

    @staticmethod
    def home_t2_click_banner(track: [], mod_nm, page_key):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get('params'):
                    track_params = json.loads(item.get('params'))
                    if track_params.get("mod_nm") == mod_nm:
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'home_top_banner':
                                if TrackVerificationHomePageV2._is_null_in_dict(track_params.get('co')):
                                    flag = True
        return flag

    @staticmethod
    def home_t2_click_banner_line(track: [], mod_nm, page_key):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get('params'):
                    track_params = json.loads(item.get('params'))
                    if track_params.get("mod_nm") == mod_nm:
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'banner_line':
                                if TrackVerificationHomePageV2._is_null_in_dict(track_params.get('co')):
                                    flag = True
        return flag

    @classmethod
    def home_t2_click_normal_button(cls, track, mod_nm, page_key):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get('params'):
                    track_params = json.loads(item.get('params'))
                    if track_params.get("mod_nm") == mod_nm:
                        if track_params.get('co') and track_params.get('co').get('target_nm'):
                            if track_params.get('co').get('target_nm') == 'explore_more':
                                flag = True
        return flag

    @classmethod
    def home_t2_click_category_icon(cls, track, mod_nm, page_key):
        flag = False
        for item in track:
            if item.get('l0_page_key') and item.get('l0_page_key') == page_key:
                if item.get('params'):
                    track_params = json.loads(item.get('params'))
                    if track_params.get("mod_nm") == mod_nm:
                        if track_params.get('co') and track_params.get('co').get('target_type'):
                            if track_params.get('co').get('target_type') == 'category':
                                if TrackVerificationHomePageV2._is_null_in_dict(track_params.get('co')):
                                    flag = True
        return flag


    @staticmethod
    def _is_null_in_dict(d: dict):
        flag = True
        for item in d.values():
            if item == "" or item == " " or item is None or item == "null":
                flag = False
        return flag








