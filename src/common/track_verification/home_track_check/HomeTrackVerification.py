import json

from src.common.commfunc import CommonCheck


class HomeTrackVerification:

    # 搜索埋点
    # home_search 用关键词豆腐搜索
    @staticmethod
    def home_page_search(track: []):
        flag = False
        for item in track:
            if item.get('params') and item.get('l0_page_key') == 'dweb_home':
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'search_active':
                    assert track_params.get('co').get('target_nm') == '豆腐', f"track_params={track_params}"
                    assert track_params.get('co').get('target_type') == 'normal_button', f"track_params={track_params}"
                    flag = True
                    break
        return flag

    @classmethod
    def home_page_banner_list(cls, track):
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'cm_main_banner' and track_params.get('co').get('banner_id'):
                    assert CommonCheck.list_check(['banner_id', 'banner_pos', 'banner_type', 'url'],
                                                  track_params.get('co').keys())
                    flag = True
                    break
        return flag

    # 加购新品上架埋点
    @staticmethod
    def home_page_click_new_arrivals(track: []):
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'cm_item_new' and track_params.get('co').get('source'):
                    assert track_params.get('co').get(
                        'source') == 'dweb_home-cm_item_new-null', f"track_params={track_params}"
                    flag = True
                    break
        return flag

    # 首页新品上架列表显示埋点
    @staticmethod
    def home_new_arrivals_list_track(track: []) -> bool:
        flag = False
        for item in track:
            # 这个item就是某个埋点，校验代码就写在这里
            if item.get('params'):
                track_params = json.loads(item.get('params'))

                if track_params.get("mod_nm") == 'cm_item_new' and not track_params.get('co').get('source'):
                    assert CommonCheck.list_check(['mod_pos', "co"], track_params.keys())
                    assert track_params.get('co')
                    assert all(value and value.lower() != 'none' and value.lower() != 'null' for value in
                               track_params.get('co').values() if type(value) is str)
                    assert all(value is not None and value != '' for value in
                               track_params.get('co').values() if type(value) is int)
                    # assert CommonCheck.list_check(['biz_type', 'price', 'prod_id', 'prod_name'], track_params.get('co').keys())
                    # and track_params.get('co').get('label_list') == ['New']
                    flag = True
                    break
        return flag

    # 加购人气热卖买点
    @staticmethod
    def home_page_click_trending(track: []):
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'cm_item_trending' and track_params.get('co').get('source'):
                    assert track_params.get('co').get(
                        'source') == 'dweb_home-cm_item_trending-null', f"track_params={track_params}"
                    assert CommonCheck.list_check(['biz_type', 'refer_type'], track_params.get('co').keys())
                    flag = True
                    break
        return flag

    @staticmethod
    def home_page_click_mkpl_top(track: []):
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'mkpl_top_x_carousel' and track_params.get('co').get('source'):
                    assert track_params.get('co').get(
                        'source') == 'dweb_home-mkpl_top_x_carousel-null', f"track_params={track_params}"
                    assert CommonCheck.list_check(['biz_type', 'refer_type'], track_params.get('co').keys())
                    flag = True
                    break
        return flag

    @staticmethod
    def home_page_click_sale(track):
        """首页加购特价精选"""
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'cm_item_sale' and track_params.get('co').get('source'):
                    assert track_params.get('co').get(
                        'source') == 'dweb_home-cm_item_sale-null', f"track_params={track_params}"
                    assert CommonCheck.list_check(['biz_type', 'refer_type'], track_params.get('co').keys())
                    flag = True
                    break
        return flag

    @staticmethod
    def home_page_click_perference(track):
        """首页加购猜你喜欢"""
        flag = False
        for item in track:
            if item.get('params'):
                track_params = json.loads(item.get('params'))
                if track_params.get('mod_nm') == 'cm_item_perference' and track_params.get('co').get('source'):
                    assert track_params.get('co').get(
                        'source') == 'dweb_home-cm_item_perference-null', f"track_params={track_params}"
                    assert CommonCheck.list_check(['biz_type', 'refer_type'], track_params.get('co').keys())
                    flag = True
                    break
        return flag
