import copy

from src.config.base_config import BASE_URL, header_init
from src.utils.HttpRequest import HttpRequest as HR


def anon_auth():
    """# 匿名获取token"""
    resp = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/customer/login/token/generate"
    })

    return resp.json()


def anony_header():
    a_header = copy.deepcopy(header_init)
    a_header["authorization"] = "Bearer " + anon_auth()["object"]["token"]
    resp = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/tracking/session_id",
        "headers": header_init
    })
    a_header['weee-session-token'] = str(resp.json()["object"]["weee_session_token"])
    a_header['b-cookie'] = str(resp.json()["object"]["weee_session_token"])
    return a_header


def login_header(email: str = "<EMAIL>", password: str = "A1234567"):
    l_header = copy.deepcopy(header_init)
    login = HR.request({
        "method": "post",
        "path": BASE_URL + "/ec/customer/login/email",
        "headers": anony_header(),
        "request_body": {
            "channelFrom": "",
            "channelID": "",
            "email": email,
            "epCode": "",
            "ep_partner": "",
            "ftu": "",
            "ftu_flags": "",
            "ftu_params": "",
            "ftu_popup": False,
            "ftu_source": "",
            "ftu_url": "",
            "password": password,
            "referral_id": 0,
            "referrer_id": 0,
            "source": ""
        }
    })
    l_header['authorization'] = 'Bearer ' + login.json()["object"]["token"]

    login_session_resp = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/tracking/session_id",
        "headers": l_header,
    })
    l_header['weee-session-token'] = str(login_session_resp.json()["object"]["weee_session_token"])
    l_header['b-cookie'] = str(login_session_resp.json()["object"]["weee_session_token"])
    print("login_header===>", l_header)

    return l_header


if __name__ == '__main__':
    # print(anon_auth())
    # print(anony_header())
    print(login_header())
